/**
 * Mobile Fixes for Corporate Prompt Master Game
 * Comprehensive mobile responsiveness fixes
 */

/* Prevent horizontal scrolling on mobile and remove unnecessary scrollbars */
html, body {
    overflow-x: hidden !important;
    max-width: 100vw !important;
    margin: 0 !important;
    padding: 0 !important;
}

/* Remove any body padding on mobile */
@media (max-width: 992px) {
    body {
        padding-top: 0 !important;
        margin-top: 0 !important;
    }
}

/* MOBILE LAYOUT OVERRIDES - HIGHEST PRIORITY */
@media (max-width: 992px) {
    /* RESET BODY CLASSES ON MOBILE */
    body {
        /* Remove any default sidebar classes */
        overflow-x: hidden !important;
    }

    body:not(.sidebar-visible):not(.right-sidebar-visible) {
        /* Ensure body is normal when no sidebars are visible */
        overflow: auto !important;
        position: static !important;
        width: 100% !important;
        height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
    }

    /* Ensure html element doesn't create white space */
    html {
        height: 100vh !important;
        margin: 0 !important;
        padding: 0 !important;
        overflow-x: hidden !important;
    }
    /* ===== LEFT SIDEBAR (NAVIGATION & CONTACT INFO) ===== */
    body .sidebar,
    body #sidebar,
    body .left-sidebar,
    .game-container .sidebar {
        position: fixed !important;
        top: 0 !important;
        left: 0 !important;
        width: 85vw !important; /* Professional mobile width - not full screen */
        max-width: 320px !important; /* Max width for larger phones */
        height: 100vh !important;
        z-index: 9999 !important;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important;
        overflow-y: auto !important;
        padding: 0 !important; /* Remove padding, add it to content */
        border: none !important;
        box-shadow: 4px 0 20px rgba(0, 0, 0, 0.3) !important;
        transition: transform 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;

        /* Hide by default - slide from left */
        transform: translateX(-100%) !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }

    /* ===== RIGHT SIDEBAR (CAREER & HIERARCHY INFO) ===== */
    body .right-sidebar,
    body #right-sidebar,
    .game-container .right-sidebar {
        position: fixed !important;
        top: 0 !important;
        right: -100% !important; /* Start hidden off-screen */
        left: auto !important; /* Override left positioning */
        width: 85vw !important; /* Professional mobile width */
        max-width: 320px !important; /* Max width for larger phones */
        height: 100vh !important;
        z-index: 9999 !important;
        background: linear-gradient(135deg, #2e1a1a 0%, #3e1616 100%) !important;
        overflow-y: auto !important;
        padding: 20px !important; /* Add padding for content */
        border: none !important;
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3) !important;
        transition: right 0.3s cubic-bezier(0.4, 0, 0.2, 1) !important;
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;

        /* Hide by default - slide from right */
        transform: translateX(100%) !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
    }

    /* ===== SIDEBAR VISIBLE STATES ===== */

    /* LEFT SIDEBAR VISIBLE - Slide in from left */
    body.sidebar-visible .sidebar,
    body.sidebar-visible #sidebar,
    body.sidebar-visible .left-sidebar,
    .game-container.sidebar-visible .sidebar {
        transform: translateX(0) !important; /* Slide in */
    }

    /* RIGHT SIDEBAR VISIBLE - Slide in from right - COPY LEFT SIDEBAR APPROACH */
    body.right-sidebar-visible .right-sidebar,
    body.right-sidebar-visible #right-sidebar,
    .game-container.right-sidebar-visible .right-sidebar {
        right: 0 !important; /* Slide in from right - SAME AS LEFT SIDEBAR LOGIC */
        transform: translateX(0) !important; /* Ensure no transform offset */
    }

    /* ===== NO OVERLAY - CLEAN INDEPENDENT SIDEBARS ===== */
    /* Overlay removed for clean, independent sidebar functionality */

    /* ===== SIDEBAR CONTENT STYLING ===== */

    /* Left Sidebar Header */
    .sidebar .sidebar-header,
    .sidebar-header {
        background: rgba(255, 255, 255, 0.1) !important;
        padding: 1.5rem 1rem !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin: 0 !important;
    }

    .sidebar .logo h1,
    .sidebar-header h1 {
        color: white !important;
        font-size: 1.2rem !important;
        margin: 0 !important;
        font-weight: 600 !important;
    }

    .sidebar .subtitle,
    .sidebar-header .subtitle {
        color: rgba(255, 255, 255, 0.7) !important;
        font-size: 0.85rem !important;
        margin-top: 0.25rem !important;
    }

    /* Right Sidebar Header */
    .right-sidebar .right-sidebar-header,
    .right-sidebar-header {
        background: rgba(255, 255, 255, 0.1) !important;
        padding: 1.5rem 1rem !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
        margin: 0 !important;
    }

    .right-sidebar .right-sidebar-title,
    .right-sidebar-title {
        color: white !important;
        font-size: 1.2rem !important;
        margin: 0 !important;
        font-weight: 600 !important;
    }

    /* Sidebar Content Areas */
    .sidebar .character-info,
    .right-sidebar .org-chart-container,
    .sidebar > div:not(.sidebar-header),
    .right-sidebar > div:not(.right-sidebar-header) {
        padding: 1rem !important;
        color: white !important;
    }

    /* ===== SIDEBAR CLOSE BUTTONS ===== */

    /* Add close button to sidebar headers */
    .sidebar-header::after,
    .right-sidebar-header::after {
        content: '✕' !important;
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        width: 32px !important;
        height: 32px !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-size: 16px !important;
        cursor: pointer !important;
        transition: background 0.2s ease !important;
        z-index: 10000 !important;
    }

    .sidebar-header::after:hover,
    .right-sidebar-header::after:hover {
        background: rgba(255, 255, 255, 0.2) !important;
    }

    /* Make headers relative for close button positioning */
    .sidebar-header,
    .right-sidebar-header {
        position: relative !important;
    }

    /* MAIN CONTENT TAKES FULL WIDTH */
    body .main-content,
    body .game-container,
    body .content-wrapper,
    .game-container .main-content {
        width: 100vw !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        padding-left: 0 !important;
        padding-right: 0 !important;
        left: 0 !important;
        right: 0 !important;
        position: relative !important;
        z-index: 1 !important;
    }

    /* Custom scrollbar styling for mobile */
    ::-webkit-scrollbar {
        width: 0px !important;
        background: transparent !important;
    }

    /* For Firefox */
    * {
        scrollbar-width: none !important;
    }
}

/* Fix viewport issues on mobile */
@viewport {
    width: device-width;
    zoom: 1.0;
}

/* Mobile-first responsive breakpoints */
@media (max-width: 992px) {
    /* Remove padding-top from app container to eliminate white space */
    .app-container {
        flex-direction: column !important;
        height: 100vh !important;
        overflow-x: hidden !important;
        overflow-y: hidden !important;
        padding-top: 0 !important;
        margin-top: 0 !important;
        padding-bottom: 0 !important;
        margin-bottom: 0 !important;
        position: relative !important;
    }

    /* Hide the fixed game header on mobile to remove white space */
    .game-header {
        display: none !important;
    }

    /* Hide hover header completely */
    .hover-header {
        display: none !important;
    }

    /* OLD CONFLICTING RULES REMOVED - USING NEW POSITIONING SYSTEM */

    /* SIDEBAR VISIBLE STATES - SLIDE IN AND COVER FULL SCREEN */
    body.sidebar-visible .sidebar,
    body.sidebar-visible .left-sidebar,
    body.sidebar-visible #sidebar,
    .sidebar-visible .game-container .sidebar {
        left: 0 !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
        z-index: 9999 !important;
    }

    body.right-sidebar-visible .right-sidebar,
    body.right-sidebar-visible #right-sidebar,
    .right-sidebar-visible .game-container .right-sidebar {
        right: 0 !important;
        transform: translateX(0) !important;
        opacity: 1 !important;
        visibility: visible !important;
        display: block !important;
        z-index: 9999 !important;
    }

    /* PREVENT BODY SCROLL WHEN SIDEBAR IS VISIBLE */
    body.sidebar-visible,
    body.right-sidebar-visible {
        overflow: hidden !important;
        position: fixed !important;
        width: 100% !important;
        height: 100vh !important;
        top: 0 !important;
        left: 0 !important;
    }

    /* HIDE MAIN CONTENT WHEN SIDEBAR IS VISIBLE */
    body.sidebar-visible .main-content,
    body.right-sidebar-visible .main-content {
        overflow: hidden !important;
        position: fixed !important;
        z-index: 1 !important;
        height: 100vh !important;
        width: 100% !important;
        top: 0 !important;
        left: 0 !important;
    }

    /* REMOVED OVERLAY BACKDROP - NO OVERLAY NEEDED */

    /* MOBILE HEADER LAYOUT - ENSURE RIGHT TOGGLE AT FAR RIGHT */
    .header {
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        padding: 10px 15px !important;
    }

    .header-left {
        flex: 0 0 auto !important;
    }

    .header-center {
        flex: 1 1 auto !important;
        text-align: center !important;
    }

    .header-right {
        flex: 0 0 auto !important;
        margin-left: auto !important;
    }

    /* MOBILE SIDEBAR CONTENT STYLING */
    .sidebar .logo h1,
    .right-sidebar .right-sidebar-title {
        font-size: 1.5rem !important;
        margin-bottom: 1.5rem !important;
        text-align: center !important;
        color: white !important;
    }

    .sidebar .character-info,
    .sidebar .game-stats,
    .sidebar .context-info,
    .sidebar .instructions,
    .right-sidebar .role-progression-container {
        margin-bottom: 1.5rem !important;
        padding: 1rem !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 8px !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
    }

    /* Make buttons more touch-friendly */
    .sidebar button,
    .right-sidebar button {
        min-height: 44px !important;
        padding: 0.75rem 1rem !important;
        font-size: 1rem !important;
        border-radius: 6px !important;
        margin: 0.5rem 0 !important;
    }

    /* Close button for mobile sidebars */
    .sidebar::before,
    .right-sidebar::before {
        content: '✕' !important;
        position: absolute !important;
        top: 1rem !important;
        right: 1rem !important;
        width: 44px !important;
        height: 44px !important;
        background: rgba(255, 255, 255, 0.2) !important;
        border-radius: 50% !important;
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        color: white !important;
        font-size: 1.2rem !important;
        cursor: pointer !important;
        z-index: 10000 !important;
    }

    /* Mobile sidebars hidden class */
    .mobile-sidebars-hidden .sidebar,
    .mobile-sidebars-hidden .right-sidebar {
        width: 0 !important;
        max-width: 0 !important;
        max-height: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        padding: 0 !important;
        margin: 0 !important;
        opacity: 0 !important;
        border: none !important;
    }

    /* Override any existing sidebar states on mobile - ONLY WHEN NOT VISIBLE */
    body:not(.sidebar-visible) .sidebar,
    body:not(.right-sidebar-visible) .right-sidebar {
        width: 0 !important;
        max-width: 0 !important;
        max-height: 0 !important;
        height: 0 !important;
        overflow: hidden !important;
        padding: 0 !important;
        margin: 0 !important;
        opacity: 0 !important;
        border: none !important;
    }

    /* Main content mobile layout - full screen when sidebars hidden */
    .main-content {
        flex: 1 !important;
        height: 100vh !important;
        min-height: 0 !important;
        overflow-x: hidden !important;
        overflow-y: hidden !important;
        display: flex !important;
        flex-direction: column !important;
        margin-left: 0 !important;
        margin-right: 0 !important;
        width: 100vw !important;
        max-width: 100vw !important;
        position: relative !important;
    }

    /* Adjust main content when sidebar is visible */
    .sidebar-visible .main-content {
        height: 100vh !important;
        width: 100% !important;
        max-width: 100% !important;
        overflow: hidden !important;
    }

    /* Header mobile optimizations - make it the main header */
    .header {
        flex-shrink: 0 !important;
        padding: 0.75rem 1rem !important;
        background: #0f1117 !important;
        border-bottom: 1px solid var(--border-primary) !important;
        display: flex !important;
        justify-content: space-between !important;
        align-items: center !important;
        position: relative !important;
        z-index: 1000 !important;
        min-height: 56px !important;
    }

    /* Add title to mobile header */
    .header::after {
        content: "Corporate Prompt Master" !important;
        color: white !important;
        font-weight: bold !important;
        font-size: 1.1rem !important;
        position: absolute !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
    }

    /* Ensure hamburger menu is visible */
    #sidebar-toggle {
        display: flex !important;
        order: -1 !important;
        z-index: 1001 !important;
    }

    /* MOBILE RIGHT SIDEBAR TOGGLE - MATCHING LEFT SIDEBAR STYLE */
    .right-sidebar-toggle,
    #right-sidebar-toggle,
    div.right-sidebar-toggle,
    div#right-sidebar-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        padding: 0 !important;
        position: relative !important;
        z-index: 1002 !important;
    }

    .right-sidebar-toggle:hover,
    .right-sidebar-toggle:focus,
    #right-sidebar-toggle:hover,
    #right-sidebar-toggle:focus {
        background: var(--primary-dark) !important;
        transform: scale(1.05) !important;
    }

    /* Right sidebar toggle hamburger lines */
    .right-sidebar-toggle span,
    #right-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* Right sidebar toggle shortcut hint */
    .right-sidebar-toggle .shortcut-hint,
    #right-sidebar-toggle .shortcut-hint {
        position: absolute !important;
        bottom: -25px !important;
        left: 50% !important;
        transform: translateX(-50%) !important;
        background: rgba(0, 0, 0, 0.8) !important;
        color: white !important;
        padding: 4px 8px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        white-space: nowrap !important;
        opacity: 0 !important;
        transition: opacity 0.3s ease !important;
        pointer-events: none !important;
        z-index: 1003 !important;
    }

    .right-sidebar-toggle:hover .shortcut-hint,
    #right-sidebar-toggle:hover .shortcut-hint {
        opacity: 1 !important;
    }

    /* Hide shortcut hints when forcibly hidden */
    .shortcut-hint.forcibly-hidden {
        display: none !important;
        visibility: hidden !important;
        opacity: 0 !important;
        pointer-events: none !important;
        position: absolute !important;
        left: -9999px !important;
        width: 0px !important;
        height: 0px !important;
        overflow: hidden !important;
    }

    /* OVERRIDE HIDE-RIGHT-TOGGLE.CSS - FORCE RIGHT TOGGLE VISIBLE ON MOBILE */
    .right-sidebar-toggle,
    #right-sidebar-toggle,
    div.right-sidebar-toggle,
    div#right-sidebar-toggle,
    button.right-sidebar-toggle,
    [class*="right-toggle"],
    [class*="toggle-right"],
    [id*="right-toggle"],
    [id*="toggle-right"] {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        position: relative !important;
        left: auto !important;
        width: 44px !important;
        height: 44px !important;
        overflow: visible !important;
    }

    /* FORCE RIGHT SIDEBAR TO BE VISIBLE AND PROPERLY POSITIONED */
    .right-sidebar,
    div.right-sidebar,
    [class*="right-sidebar"] {
        display: block !important;
        visibility: visible !important;
        opacity: 1 !important;
        pointer-events: auto !important;
        position: fixed !important;
        top: 0 !important;
        right: -100% !important;
        left: auto !important;
        width: 85vw !important;
        max-width: 320px !important;
        height: 100vh !important;
        z-index: 9999 !important;
        background: linear-gradient(135deg, #1a1a2e 0%, #16213e 100%) !important; /* Match left sidebar colors */
        transition: right 0.3s ease-in-out !important;
        overflow-y: auto !important;
        padding: 0 !important; /* Remove padding, add it to content */
        box-shadow: -4px 0 20px rgba(0, 0, 0, 0.3) !important;
        border: none !important;
    }

    /* FORCE RIGHT SIDEBAR VISIBLE STATE - OVERRIDE ALL HIDDEN RULES */
    body.right-sidebar-visible .right-sidebar,
    body.right-sidebar-visible div.right-sidebar,
    body.right-sidebar-visible [class*="right-sidebar"] {
        right: 0 !important;
        transform: translateX(0) !important;
        width: 85vw !important;
        max-width: 320px !important;
        height: 100vh !important;
        opacity: 1 !important;
        visibility: visible !important;
        pointer-events: auto !important;
        overflow-y: auto !important;
        display: block !important;
    }

    /* RIGHT SIDEBAR HEADER STYLING TO MATCH LEFT SIDEBAR */
    .right-sidebar .right-sidebar-header {
        background: rgba(255, 255, 255, 0.1) !important;
        padding: 15px 20px !important;
        border-bottom: 1px solid rgba(255, 255, 255, 0.2) !important;
        margin-bottom: 0 !important;
        display: flex !important;
        align-items: center !important;
        justify-content: space-between !important;
    }

    .right-sidebar .right-sidebar-title {
        color: #ffffff !important;
        font-size: 16px !important;
        font-weight: 600 !important;
        margin: 0 !important;
    }

    .right-sidebar .right-sidebar-collapse-indicator {
        color: #ffffff !important;
        opacity: 0.7 !important;
        font-size: 12px !important;
    }

    /* RIGHT SIDEBAR CONTENT STYLING */
    .right-sidebar .org-chart-container,
    .right-sidebar .role-progression-container {
        padding: 20px !important;
        color: #ffffff !important;
    }

    .right-sidebar h3 {
        color: #ffffff !important;
        font-size: 14px !important;
        font-weight: 600 !important;
        margin: 0 0 15px 0 !important;
        text-transform: uppercase !important;
        letter-spacing: 0.5px !important;
    }

    /* RIGHT SIDEBAR BUTTONS AND INTERACTIVE ELEMENTS */
    .right-sidebar .toggle-button {
        background: rgba(255, 255, 255, 0.1) !important;
        color: #ffffff !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        padding: 8px 12px !important;
        border-radius: 4px !important;
        font-size: 12px !important;
        cursor: pointer !important;
        transition: all 0.2s ease !important;
    }

    .right-sidebar .toggle-button:hover {
        background: rgba(255, 255, 255, 0.2) !important;
        border-color: rgba(255, 255, 255, 0.3) !important;
    }

    /* Ensure hamburger menu is properly styled */
    #sidebar-toggle,
    .mobile-sidebar-toggle {
        display: flex !important;
        order: -1 !important;
        z-index: 1001 !important;
        width: 40px !important;
        height: 40px !important;
        cursor: pointer !important;
        background: rgba(255, 255, 255, 0.1) !important;
        border-radius: 6px !important;
        align-items: center !important;
        justify-content: center !important;
        transition: background 0.2s ease !important;
        margin-right: 1rem !important;
    }

    #sidebar-toggle:hover,
    .mobile-sidebar-toggle:hover {
        background: rgba(255, 255, 255, 0.2) !important;
    }

    /* Mobile sidebar toggle */
    .mobile-sidebar-toggle {
        display: flex !important;
        align-items: center !important;
        justify-content: center !important;
        width: 44px !important;
        height: 44px !important;
        background: var(--primary-color) !important;
        border-radius: 8px !important;
        cursor: pointer !important;
        transition: all 0.3s ease !important;
        border: none !important;
        padding: 0 !important;
    }

    .mobile-sidebar-toggle:hover,
    .mobile-sidebar-toggle:focus {
        background: var(--primary-dark) !important;
        transform: scale(1.05) !important;
    }

    .mobile-sidebar-toggle span {
        display: block !important;
        width: 20px !important;
        height: 2px !important;
        background: white !important;
        margin: 3px 0 !important;
        transition: 0.3s !important;
        border-radius: 1px !important;
    }

    /* RIGHT SIDEBAR TOGGLE - INHERITS MOBILE-SIDEBAR-TOGGLE STYLES */

    /* Messages container mobile - fill remaining space */
    .messages-container {
        flex: 1 !important;
        overflow-y: auto !important;
        overflow-x: hidden !important;
        padding: 1rem !important;
        min-height: 0 !important;
        scrollbar-width: thin !important;
        height: calc(100vh - 56px - 80px) !important; /* viewport - header - input area */
        max-height: calc(100vh - 56px - 80px) !important;
    }

    /* Hide scrollbar for messages container on mobile */
    .messages-container::-webkit-scrollbar {
        width: 3px !important;
        background: transparent !important;
    }

    .messages-container::-webkit-scrollbar-thumb {
        background: rgba(255, 255, 255, 0.3) !important;
        border-radius: 3px !important;
    }

    /* Input area mobile - fixed at bottom */
    .input-area {
        flex-shrink: 0 !important;
        padding: 1rem !important;
        background: var(--bg-secondary) !important;
        border-top: 1px solid var(--border-primary) !important;
        min-height: 80px !important;
        max-height: 80px !important;
    }

    /* Form controls mobile */
    .prompt-input, .response-editor {
        font-size: 16px !important; /* Prevent zoom on iOS */
        min-height: 44px !important;
        padding: 0.75rem !important;
        border-radius: 8px !important;
        border: 2px solid var(--border-primary) !important;
        width: 100% !important;
        resize: vertical !important;
    }

    .prompt-input:focus, .response-editor:focus {
        border-color: var(--primary-color) !important;
        box-shadow: 0 0 0 0.2rem rgba(102, 126, 234, 0.25) !important;
        outline: none !important;
    }

    /* Buttons mobile */
    .btn, .preview-button, .primary-button, .secondary-button {
        min-height: 44px !important;
        padding: 0.75rem 1.25rem !important;
        font-size: 1rem !important;
        border-radius: 8px !important;
        font-weight: 500 !important;
        touch-action: manipulation !important;
    }

    /* Button groups mobile */
    .preview-actions, .edit-actions {
        display: flex !important;
        flex-wrap: wrap !important;
        gap: 0.5rem !important;
        margin-top: 1rem !important;
    }

    /* Messages mobile */
    .message {
        max-width: 95% !important;
        margin-bottom: 1rem !important;
        border-radius: 12px !important;
    }

    .message-content {
        padding: 0.75rem 1rem !important;
        font-size: 0.95rem !important;
        line-height: 1.4 !important;
    }

    .message-sender {
        font-size: 0.8rem !important;
        margin-bottom: 0.25rem !important;
    }

    /* Character info mobile */
    .character-info {
        padding: 0.75rem !important;
    }

    .character-name {
        font-size: 1rem !important;
        font-weight: 600 !important;
    }

    .character-title {
        font-size: 0.85rem !important;
        opacity: 0.8 !important;
    }

    /* Hide instructions on mobile to save space */
    .instructions {
        display: none !important;
    }

    /* Modal mobile optimizations */
    .modal-dialog {
        margin: 0.5rem !important;
        max-width: calc(100% - 1rem) !important;
    }

    .modal-content {
        border-radius: 12px !important;
    }

    .modal-body {
        padding: 1.5rem !important;
    }

    .modal-buttons {
        display: flex !important;
        flex-direction: column !important;
        gap: 0.75rem !important;
    }

    .modal-buttons .btn {
        width: 100% !important;
    }
}

/* Small mobile devices (phones) */
@media (max-width: 576px) {
    .sidebar {
        max-height: 200px !important;
        padding: 0.5rem !important;
    }

    .main-content {
        height: calc(100vh - 200px) !important;
    }

    .header {
        padding: 0.5rem !important;
    }

    .messages-container {
        padding: 0.75rem !important;
    }

    .input-area {
        padding: 0.75rem !important;
    }

    .prompt-input, .response-editor {
        height: 70px !important;
        padding: 0.5rem !important;
    }

    .btn, .preview-button, .primary-button, .secondary-button {
        padding: 0.5rem 1rem !important;
        font-size: 0.9rem !important;
    }

    .character-info {
        padding: 0.5rem !important;
    }

    .character-name {
        font-size: 0.9rem !important;
    }

    .character-title {
        font-size: 0.8rem !important;
    }

    .message-content {
        padding: 0.5rem 0.75rem !important;
        font-size: 0.9rem !important;
    }
}

/* Landscape orientation fixes */
@media (max-width: 992px) and (orientation: landscape) {
    .sidebar {
        max-height: 150px !important;
    }

    .main-content {
        height: calc(100vh - 150px) !important;
    }
}

/* Touch device optimizations */
@media (pointer: coarse) {
    /* Larger touch targets */
    .btn, button, .mobile-sidebar-toggle {
        min-height: 48px !important;
        min-width: 48px !important;
    }

    /* Prevent text selection on UI elements */
    .mobile-sidebar-toggle, .btn, button {
        -webkit-user-select: none !important;
        -moz-user-select: none !important;
        -ms-user-select: none !important;
        user-select: none !important;
    }

    /* Improve scrolling on touch devices */
    .messages-container, .sidebar {
        -webkit-overflow-scrolling: touch !important;
        scroll-behavior: smooth !important;
    }
}

/* High DPI display fixes */
@media (-webkit-min-device-pixel-ratio: 2), (min-resolution: 192dpi) {
    .app-container, .main-content, .sidebar {
        height: 100vh !important;
        min-height: 100vh !important;
    }
}

/* ===== DESKTOP RIGHT SIDEBAR BEHAVIOR - COPY LEFT SIDEBAR LOGIC ===== */
@media (min-width: 993px) {
    /* Right sidebar hidden state on desktop - SAME AS LEFT SIDEBAR */
    body.right-sidebar-hidden .right-sidebar {
        transform: translateX(300px) !important;
        width: 0 !important;
        padding: 0 !important;
        overflow: hidden !important;
        border-left: none !important;
    }

    /* Adjust main content when right sidebar is hidden - SAME AS LEFT SIDEBAR */
    body.right-sidebar-hidden .main-content {
        margin-right: 0 !important;
    }

    /* Right sidebar toggle button styling on desktop */
    .right-sidebar-toggle {
        display: flex !important;
        visibility: visible !important;
        opacity: 1 !important;
    }
}
