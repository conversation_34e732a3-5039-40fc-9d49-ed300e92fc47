/* Corporate Prompt Master - Global CSS Framework */
/* Modern, Professional Design System for All Templates */

/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=JetBrains+Mono:wght@400;500;600&display=swap');

/* ===== CSS CUSTOM PROPERTIES ===== */
:root {
    /* Primary Brand Colors */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #7c8aed;
    --primary-rgb: 102, 126, 234;

    /* Secondary Colors */
    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --secondary-color: #f093fb;
    --secondary-dark: #e081e8;
    --secondary-light: #f5a6fc;

    /* Status Colors */
    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-color: #4facfe;
    --success-dark: #3d8bfe;
    --success-light: #6bc5fe;

    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --warning-color: #fcb69f;
    --warning-dark: #f9a583;
    --warning-light: #ffc7bb;

    --danger-gradient: linear-gradient(135deg, #ff6b6b 0%, #ee5a52 100%);
    --danger-color: #ff6b6b;
    --danger-dark: #ff5252;
    --danger-light: #ff8a80;

    --info-gradient: linear-gradient(135deg, #74b9ff 0%, #0984e3 100%);
    --info-color: #74b9ff;
    --info-dark: #0984e3;
    --info-light: #a8d1ff;

    /* Dark Theme Background Colors */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: #1e1e3f;
    --bg-elevated: #252547;
    --bg-overlay: rgba(15, 15, 35, 0.95);

    /* Light Theme Background Colors */
    --bg-light-primary: #ffffff;
    --bg-light-secondary: #f8fafc;
    --bg-light-tertiary: #f1f5f9;
    --bg-light-card: #ffffff;
    --bg-light-elevated: #f8fafc;

    /* Text Colors - Dark Theme */
    --text-primary: #ffffff;
    --text-secondary: #b8bcc8;
    --text-muted: #8b8fa3;
    --text-accent: #667eea;
    --text-inverse: #0f0f23;

    /* Text Colors - Light Theme */
    --text-light-primary: #1a202c;
    --text-light-secondary: #4a5568;
    --text-light-muted: #718096;
    --text-light-accent: #667eea;

    /* Border Colors */
    --border-primary: #2d2d4a;
    --border-secondary: #3a3a5c;
    --border-accent: #667eea;
    --border-light: rgba(255, 255, 255, 0.1);

    /* Light Theme Borders */
    --border-light-primary: #e2e8f0;
    --border-light-secondary: #cbd5e0;
    --border-light-accent: #667eea;

    /* Shadows */
    --shadow-sm: 0 1px 3px rgba(0, 0, 0, 0.12), 0 1px 2px rgba(0, 0, 0, 0.24);
    --shadow-md: 0 4px 6px rgba(0, 0, 0, 0.07), 0 2px 4px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px rgba(0, 0, 0, 0.1), 0 4px 6px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px rgba(0, 0, 0, 0.15), 0 10px 10px rgba(0, 0, 0, 0.04);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);
    --shadow-glow-success: 0 0 20px rgba(79, 172, 254, 0.3);
    --shadow-glow-danger: 0 0 20px rgba(255, 107, 107, 0.3);

    /* Spacing Scale */
    --space-xs: 0.25rem;    /* 4px */
    --space-sm: 0.5rem;     /* 8px */
    --space-md: 1rem;       /* 16px */
    --space-lg: 1.5rem;     /* 24px */
    --space-xl: 2rem;       /* 32px */
    --space-2xl: 3rem;      /* 48px */
    --space-3xl: 4rem;      /* 64px */

    /* Border Radius */
    --radius-sm: 0.25rem;   /* 4px */
    --radius-md: 0.5rem;    /* 8px */
    --radius-lg: 0.75rem;   /* 12px */
    --radius-xl: 1rem;      /* 16px */
    --radius-2xl: 1.5rem;   /* 24px */
    --radius-full: 9999px;

    /* Typography Scale */
    --text-xs: 0.75rem;     /* 12px */
    --text-sm: 0.875rem;    /* 14px */
    --text-base: 1rem;      /* 16px */
    --text-lg: 1.125rem;    /* 18px */
    --text-xl: 1.25rem;     /* 20px */
    --text-2xl: 1.5rem;     /* 24px */
    --text-3xl: 1.875rem;   /* 30px */
    --text-4xl: 2.25rem;    /* 36px */

    /* Line Heights */
    --leading-tight: 1.25;
    --leading-normal: 1.5;
    --leading-relaxed: 1.75;

    /* Transitions */
    --transition-fast: 150ms ease-in-out;
    --transition-normal: 250ms ease-in-out;
    --transition-slow: 350ms ease-in-out;

    /* Z-Index Scale */
    --z-dropdown: 1000;
    --z-sticky: 1020;
    --z-fixed: 1030;
    --z-modal-backdrop: 1040;
    --z-modal: 1050;
    --z-popover: 1060;
    --z-tooltip: 1070;
    --z-toast: 1080;

    /* Container Widths */
    --container-sm: 640px;
    --container-md: 768px;
    --container-lg: 1024px;
    --container-xl: 1280px;
    --container-2xl: 1536px;
}

/* ===== GLOBAL RESET & BASE STYLES ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    font-size: 16px;
    line-height: 1.5;
    -webkit-text-size-adjust: 100%;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: var(--leading-normal);
    font-weight: 400;
    min-height: 100vh;
    overflow-x: hidden;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: var(--leading-tight);
    margin-bottom: var(--space-md);
    color: var(--text-primary);
}

h1 { font-size: var(--text-4xl); }
h2 { font-size: var(--text-3xl); }
h3 { font-size: var(--text-2xl); }
h4 { font-size: var(--text-xl); }
h5 { font-size: var(--text-lg); }
h6 { font-size: var(--text-base); }

p {
    margin-bottom: var(--space-md);
    color: var(--text-secondary);
    line-height: var(--leading-relaxed);
}

a {
    color: var(--text-accent);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-light);
    text-decoration: underline;
}

code {
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
    background: var(--bg-elevated);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-sm);
    font-size: 0.9em;
}

/* ===== UTILITY CLASSES ===== */
.container {
    width: 100%;
    max-width: var(--container-xl);
    margin: 0 auto;
    padding: 0 var(--space-md);
}

.container-sm { max-width: var(--container-sm); }
.container-md { max-width: var(--container-md); }
.container-lg { max-width: var(--container-lg); }
.container-xl { max-width: var(--container-xl); }
.container-2xl { max-width: var(--container-2xl); }

/* Flexbox Utilities */
.flex { display: flex; }
.flex-col { flex-direction: column; }
.flex-row { flex-direction: row; }
.flex-wrap { flex-wrap: wrap; }
.flex-nowrap { flex-wrap: nowrap; }
.flex-1 { flex: 1; }
.flex-auto { flex: auto; }
.flex-none { flex: none; }

.items-start { align-items: flex-start; }
.items-center { align-items: center; }
.items-end { align-items: flex-end; }
.items-stretch { align-items: stretch; }

.justify-start { justify-content: flex-start; }
.justify-center { justify-content: center; }
.justify-end { justify-content: flex-end; }
.justify-between { justify-content: space-between; }
.justify-around { justify-content: space-around; }

/* Grid Utilities */
.grid { display: grid; }
.grid-cols-1 { grid-template-columns: repeat(1, 1fr); }
.grid-cols-2 { grid-template-columns: repeat(2, 1fr); }
.grid-cols-3 { grid-template-columns: repeat(3, 1fr); }
.grid-cols-4 { grid-template-columns: repeat(4, 1fr); }
.grid-cols-6 { grid-template-columns: repeat(6, 1fr); }
.grid-cols-12 { grid-template-columns: repeat(12, 1fr); }

.gap-1 { gap: var(--space-xs); }
.gap-2 { gap: var(--space-sm); }
.gap-4 { gap: var(--space-md); }
.gap-6 { gap: var(--space-lg); }
.gap-8 { gap: var(--space-xl); }

/* Spacing Utilities */
.m-0 { margin: 0; }
.m-1 { margin: var(--space-xs); }
.m-2 { margin: var(--space-sm); }
.m-4 { margin: var(--space-md); }
.m-6 { margin: var(--space-lg); }
.m-8 { margin: var(--space-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--space-xs); }
.p-2 { padding: var(--space-sm); }
.p-4 { padding: var(--space-md); }
.p-6 { padding: var(--space-lg); }
.p-8 { padding: var(--space-xl); }

/* Text Utilities */
.text-xs { font-size: var(--text-xs); }
.text-sm { font-size: var(--text-sm); }
.text-base { font-size: var(--text-base); }
.text-lg { font-size: var(--text-lg); }
.text-xl { font-size: var(--text-xl); }
.text-2xl { font-size: var(--text-2xl); }

.text-left { text-align: left; }
.text-center { text-align: center; }
.text-right { text-align: right; }

.font-light { font-weight: 300; }
.font-normal { font-weight: 400; }
.font-medium { font-weight: 500; }
.font-semibold { font-weight: 600; }
.font-bold { font-weight: 700; }
.font-extrabold { font-weight: 800; }

.text-primary { color: var(--text-primary); }
.text-secondary { color: var(--text-secondary); }
.text-muted { color: var(--text-muted); }
.text-accent { color: var(--text-accent); }

/* Background Utilities */
.bg-primary { background: var(--bg-primary); }
.bg-secondary { background: var(--bg-secondary); }
.bg-tertiary { background: var(--bg-tertiary); }
.bg-card { background: var(--bg-card); }
.bg-elevated { background: var(--bg-elevated); }

.bg-gradient-primary { background: var(--primary-gradient); }
.bg-gradient-secondary { background: var(--secondary-gradient); }
.bg-gradient-success { background: var(--success-gradient); }
.bg-gradient-warning { background: var(--warning-gradient); }
.bg-gradient-danger { background: var(--danger-gradient); }

/* Border Utilities */
.border { border: 1px solid var(--border-primary); }
.border-secondary { border: 1px solid var(--border-secondary); }
.border-accent { border: 1px solid var(--border-accent); }

.rounded-sm { border-radius: var(--radius-sm); }
.rounded { border-radius: var(--radius-md); }
.rounded-lg { border-radius: var(--radius-lg); }
.rounded-xl { border-radius: var(--radius-xl); }
.rounded-2xl { border-radius: var(--radius-2xl); }
.rounded-full { border-radius: var(--radius-full); }

/* Shadow Utilities */
.shadow-sm { box-shadow: var(--shadow-sm); }
.shadow { box-shadow: var(--shadow-md); }
.shadow-lg { box-shadow: var(--shadow-lg); }
.shadow-xl { box-shadow: var(--shadow-xl); }
.shadow-glow { box-shadow: var(--shadow-glow); }

/* Position Utilities */
.relative { position: relative; }
.absolute { position: absolute; }
.fixed { position: fixed; }
.sticky { position: sticky; }

.top-0 { top: 0; }
.right-0 { right: 0; }
.bottom-0 { bottom: 0; }
.left-0 { left: 0; }

/* Display Utilities */
.block { display: block; }
.inline-block { display: inline-block; }
.inline { display: inline; }
.hidden { display: none; }

/* Overflow Utilities */
.overflow-hidden { overflow: hidden; }
.overflow-auto { overflow: auto; }
.overflow-scroll { overflow: scroll; }

/* Width & Height Utilities */
.w-full { width: 100%; }
.w-auto { width: auto; }
.h-full { height: 100%; }
.h-auto { height: auto; }
.min-h-screen { min-height: 100vh; }

/* ===== COMPONENT STYLES ===== */

/* Buttons */
.btn {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-sm);
    font-weight: 500;
    line-height: 1.5;
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    cursor: pointer;
    transition: all var(--transition-fast);
    text-decoration: none;
    white-space: nowrap;
    user-select: none;
    position: relative;
    overflow: hidden;
}

.btn:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    pointer-events: none;
}

.btn:focus {
    outline: none;
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.3);
}

/* Button Sizes */
.btn-xs {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
}

.btn-sm {
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-sm);
}

.btn-lg {
    padding: var(--space-md) var(--space-lg);
    font-size: var(--text-lg);
}

.btn-xl {
    padding: var(--space-lg) var(--space-xl);
    font-size: var(--text-xl);
}

/* Button Variants */
.btn-primary {
    background: var(--primary-gradient);
    color: white;
    border-color: var(--primary-color);
}

.btn-primary:hover {
    background: linear-gradient(135deg, #5a67d8 0%, #6b46c1 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg), var(--shadow-glow);
}

.btn-secondary {
    background: var(--bg-elevated);
    color: var(--text-primary);
    border-color: var(--border-primary);
}

.btn-secondary:hover {
    background: var(--bg-card);
    border-color: var(--border-accent);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-success {
    background: var(--success-gradient);
    color: white;
    border-color: var(--success-color);
}

.btn-success:hover {
    background: linear-gradient(135deg, #3d8bfe 0%, #00d4fe 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg), var(--shadow-glow-success);
}

.btn-danger {
    background: var(--danger-gradient);
    color: white;
    border-color: var(--danger-color);
}

.btn-danger:hover {
    background: linear-gradient(135deg, #ff5252 0%, #e53e3e 100%);
    transform: translateY(-1px);
    box-shadow: var(--shadow-lg), var(--shadow-glow-danger);
}

.btn-outline {
    background: transparent;
    color: var(--text-accent);
    border-color: var(--border-accent);
}

.btn-outline:hover {
    background: var(--primary-color);
    color: white;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-ghost {
    background: transparent;
    color: var(--text-secondary);
    border-color: transparent;
}

.btn-ghost:hover {
    background: var(--bg-elevated);
    color: var(--text-primary);
}

/* Cards */
.card {
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-md);
    overflow: hidden;
    transition: all var(--transition-normal);
}

.card:hover {
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-accent);
}

.card-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-primary);
    background: var(--bg-elevated);
}

.card-body {
    padding: var(--space-lg);
}

.card-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--border-primary);
    background: var(--bg-elevated);
}

/* Forms */
.form-group {
    margin-bottom: var(--space-lg);
}

.form-label {
    display: block;
    font-size: var(--text-sm);
    font-weight: 500;
    color: var(--text-primary);
    margin-bottom: var(--space-sm);
}

.form-control {
    display: block;
    width: 100%;
    padding: var(--space-sm) var(--space-md);
    font-size: var(--text-base);
    line-height: 1.5;
    color: var(--text-primary);
    background: var(--bg-elevated);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.form-control:focus {
    outline: none;
    border-color: var(--border-accent);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
    background: var(--bg-card);
}

.form-control::placeholder {
    color: var(--text-muted);
}

.form-control:disabled {
    opacity: 0.6;
    cursor: not-allowed;
    background: var(--bg-secondary);
}

.form-select {
    background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
    background-position: right var(--space-sm) center;
    background-repeat: no-repeat;
    background-size: 1.5em 1.5em;
    padding-right: 2.5rem;
}

.form-textarea {
    resize: vertical;
    min-height: 100px;
}

.form-check {
    display: flex;
    align-items: center;
    margin-bottom: var(--space-sm);
}

.form-check-input {
    width: 1rem;
    height: 1rem;
    margin-right: var(--space-sm);
    accent-color: var(--primary-color);
}

.form-check-label {
    font-size: var(--text-sm);
    color: var(--text-secondary);
    margin-bottom: 0;
}

/* Alerts */
.alert {
    padding: var(--space-md);
    border-radius: var(--radius-md);
    border: 1px solid transparent;
    margin-bottom: var(--space-lg);
    position: relative;
}

.alert-primary {
    background: rgba(102, 126, 234, 0.1);
    border-color: rgba(102, 126, 234, 0.3);
    color: var(--primary-light);
}

.alert-success {
    background: rgba(79, 172, 254, 0.1);
    border-color: rgba(79, 172, 254, 0.3);
    color: var(--success-light);
}

.alert-warning {
    background: rgba(252, 182, 159, 0.1);
    border-color: rgba(252, 182, 159, 0.3);
    color: var(--warning-color);
}

.alert-danger {
    background: rgba(255, 107, 107, 0.1);
    border-color: rgba(255, 107, 107, 0.3);
    color: var(--danger-light);
}

.alert-info {
    background: rgba(116, 185, 255, 0.1);
    border-color: rgba(116, 185, 255, 0.3);
    color: var(--info-light);
}

/* Badges */
.badge {
    display: inline-flex;
    align-items: center;
    padding: var(--space-xs) var(--space-sm);
    font-size: var(--text-xs);
    font-weight: 500;
    border-radius: var(--radius-full);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.badge-primary {
    background: var(--primary-gradient);
    color: white;
}

.badge-secondary {
    background: var(--bg-elevated);
    color: var(--text-secondary);
    border: 1px solid var(--border-primary);
}

.badge-success {
    background: var(--success-gradient);
    color: white;
}

.badge-warning {
    background: var(--warning-gradient);
    color: var(--text-inverse);
}

.badge-danger {
    background: var(--danger-gradient);
    color: white;
}

/* Progress Bars */
.progress {
    height: 0.5rem;
    background: var(--bg-elevated);
    border-radius: var(--radius-full);
    overflow: hidden;
    box-shadow: inset 0 1px 2px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    background: var(--primary-gradient);
    border-radius: var(--radius-full);
    transition: width var(--transition-normal);
    position: relative;
    overflow: hidden;
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    bottom: 0;
    right: 0;
    background: linear-gradient(
        45deg,
        rgba(255, 255, 255, 0.2) 25%,
        transparent 25%,
        transparent 50%,
        rgba(255, 255, 255, 0.2) 50%,
        rgba(255, 255, 255, 0.2) 75%,
        transparent 75%,
        transparent
    );
    background-size: 1rem 1rem;
    animation: progress-stripes 1s linear infinite;
}

@keyframes progress-stripes {
    0% { background-position: 0 0; }
    100% { background-position: 1rem 0; }
}

/* Tables */
.table {
    width: 100%;
    border-collapse: collapse;
    background: var(--bg-card);
    border-radius: var(--radius-lg);
    overflow: hidden;
    box-shadow: var(--shadow-md);
}

.table th,
.table td {
    padding: var(--space-md);
    text-align: left;
    border-bottom: 1px solid var(--border-primary);
}

.table th {
    background: var(--bg-elevated);
    font-weight: 600;
    color: var(--text-primary);
    font-size: var(--text-sm);
    text-transform: uppercase;
    letter-spacing: 0.05em;
}

.table td {
    color: var(--text-secondary);
}

.table tbody tr:hover {
    background: var(--bg-elevated);
}

.table tbody tr:last-child td {
    border-bottom: none;
}

/* Navigation */
.navbar {
    background: var(--bg-card);
    border-bottom: 1px solid var(--border-primary);
    box-shadow: var(--shadow-md);
    position: sticky;
    top: 0;
    z-index: var(--z-sticky);
}

.navbar-brand {
    font-size: var(--text-xl);
    font-weight: 700;
    color: var(--text-primary);
    text-decoration: none;
}

.navbar-nav {
    display: flex;
    list-style: none;
    margin: 0;
    padding: 0;
    gap: var(--space-md);
}

.nav-item {
    position: relative;
}

.nav-link {
    display: block;
    padding: var(--space-sm) var(--space-md);
    color: var(--text-secondary);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.nav-link:hover,
.nav-link.active {
    color: var(--text-primary);
    background: var(--bg-elevated);
}

/* Dropdown */
.dropdown {
    position: relative;
}

.dropdown-menu {
    position: absolute;
    top: 100%;
    left: 0;
    min-width: 200px;
    background: var(--bg-card);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    z-index: var(--z-dropdown);
    opacity: 0;
    visibility: hidden;
    transform: translateY(-10px);
    transition: all var(--transition-fast);
}

.dropdown:hover .dropdown-menu,
.dropdown.show .dropdown-menu {
    opacity: 1;
    visibility: visible;
    transform: translateY(0);
}

.dropdown-item {
    display: block;
    padding: var(--space-sm) var(--space-md);
    color: var(--text-secondary);
    text-decoration: none;
    transition: all var(--transition-fast);
}

.dropdown-item:hover {
    background: var(--bg-elevated);
    color: var(--text-primary);
}

/* Modals */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: var(--bg-overlay);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: var(--z-modal);
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-normal);
}

.modal.show {
    opacity: 1;
    visibility: visible;
}

.modal-dialog {
    background: var(--bg-card);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    max-width: 500px;
    width: 90%;
    max-height: 90vh;
    overflow: hidden;
    transform: scale(0.9);
    transition: transform var(--transition-normal);
}

.modal.show .modal-dialog {
    transform: scale(1);
}

.modal-header {
    padding: var(--space-lg);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    align-items: center;
    justify-content: space-between;
}

.modal-title {
    font-size: var(--text-xl);
    font-weight: 600;
    color: var(--text-primary);
    margin: 0;
}

.modal-close {
    background: none;
    border: none;
    color: var(--text-muted);
    font-size: var(--text-xl);
    cursor: pointer;
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
}

.modal-close:hover {
    background: var(--bg-elevated);
    color: var(--text-primary);
}

.modal-body {
    padding: var(--space-lg);
    overflow-y: auto;
}

.modal-footer {
    padding: var(--space-lg);
    border-top: 1px solid var(--border-primary);
    display: flex;
    gap: var(--space-md);
    justify-content: flex-end;
}

/* Tooltips */
.tooltip {
    position: relative;
}

.tooltip::before {
    content: attr(data-tooltip);
    position: absolute;
    bottom: 100%;
    left: 50%;
    transform: translateX(-50%);
    background: var(--bg-elevated);
    color: var(--text-primary);
    padding: var(--space-xs) var(--space-sm);
    border-radius: var(--radius-md);
    font-size: var(--text-xs);
    white-space: nowrap;
    opacity: 0;
    visibility: hidden;
    transition: all var(--transition-fast);
    z-index: var(--z-tooltip);
    margin-bottom: var(--space-xs);
    box-shadow: var(--shadow-md);
}

.tooltip:hover::before {
    opacity: 1;
    visibility: visible;
}

/* Loading Spinner */
.spinner {
    width: 2rem;
    height: 2rem;
    border: 2px solid var(--border-primary);
    border-top: 2px solid var(--primary-color);
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

.spinner-sm {
    width: 1rem;
    height: 1rem;
    border-width: 1px;
}

.spinner-lg {
    width: 3rem;
    height: 3rem;
    border-width: 3px;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive Utilities */
@media (max-width: 640px) {
    .sm\:hidden { display: none; }
    .sm\:block { display: block; }
    .sm\:flex { display: flex; }
    .sm\:grid { display: grid; }

    .sm\:flex-col { flex-direction: column; }
    .sm\:text-center { text-align: center; }
    .sm\:text-sm { font-size: var(--text-sm); }

    .container { padding: 0 var(--space-sm); }

    .btn {
        width: 100%;
        justify-content: center;
    }

    .modal-dialog {
        width: 95%;
        margin: var(--space-md);
    }

    .navbar-nav {
        flex-direction: column;
        gap: var(--space-sm);
    }
}

@media (max-width: 768px) {
    .md\:hidden { display: none; }
    .md\:block { display: block; }
    .md\:flex { display: flex; }
    .md\:grid { display: grid; }

    .md\:flex-col { flex-direction: column; }
    .md\:text-center { text-align: center; }
}

@media (max-width: 1024px) {
    .lg\:hidden { display: none; }
    .lg\:block { display: block; }
    .lg\:flex { display: flex; }
    .lg\:grid { display: grid; }
}

/* Dark/Light Mode Toggle */
.theme-toggle {
    background: var(--bg-elevated);
    border: 1px solid var(--border-primary);
    border-radius: var(--radius-full);
    padding: var(--space-sm);
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--text-secondary);
}

.theme-toggle:hover {
    background: var(--bg-card);
    color: var(--text-primary);
    transform: scale(1.05);
}

/* Light Theme Overrides */
.light-theme {
    --bg-primary: var(--bg-light-primary);
    --bg-secondary: var(--bg-light-secondary);
    --bg-tertiary: var(--bg-light-tertiary);
    --bg-card: var(--bg-light-card);
    --bg-elevated: var(--bg-light-elevated);

    --text-primary: var(--text-light-primary);
    --text-secondary: var(--text-light-secondary);
    --text-muted: var(--text-light-muted);
    --text-accent: var(--text-light-accent);

    --border-primary: var(--border-light-primary);
    --border-secondary: var(--border-light-secondary);
    --border-accent: var(--border-light-accent);
}

/* Animation Utilities */
.animate-fade-in {
    animation: fadeIn 0.3s ease-in-out;
}

.animate-slide-up {
    animation: slideUp 0.3s ease-out;
}

.animate-bounce {
    animation: bounce 0.6s ease-in-out;
}

@keyframes fadeIn {
    from { opacity: 0; }
    to { opacity: 1; }
}

@keyframes slideUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes bounce {
    0%, 20%, 53%, 80%, 100% {
        transform: translate3d(0, 0, 0);
    }
    40%, 43% {
        transform: translate3d(0, -8px, 0);
    }
    70% {
        transform: translate3d(0, -4px, 0);
    }
    90% {
        transform: translate3d(0, -2px, 0);
    }
}

/* Print Styles */
@media print {
    .no-print { display: none !important; }

    body {
        background: white !important;
        color: black !important;
    }

    .card {
        border: 1px solid #ccc !important;
        box-shadow: none !important;
    }
}
