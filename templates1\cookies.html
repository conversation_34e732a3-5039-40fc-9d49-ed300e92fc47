{% extends 'base/layout.html' %}

{% block title %}<PERSON>ie Policy - Corporate Prompt Master{% endblock %}

{% block content %}
<!-- Enhanced <PERSON>ie Policy Hero Section -->
<section class="cookies-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <h1 class="display-4 fw-bold mb-3 text-shadow">
                                    <i class="fas fa-cookie-bite me-3"></i>
                                    Cookie <span class="text-gradient">Policy</span>
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Learn how Corporate Prompt Master uses cookies to enhance your experience
                                    and protect your privacy while using our platform.
                                </p>
                                <div class="cookies-badges d-flex flex-wrap gap-3">
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-shield-alt me-2"></i>Privacy First
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-cog me-2"></i>Customizable
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-check-circle me-2"></i>GDPR Compliant
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="cookies-stats text-center">
                                <div class="stat-item mb-4">
                                    <div class="stat-number display-6 fw-bold text-white">3</div>
                                    <div class="stat-label text-white-75">Cookie Types</div>
                                </div>
                                <div class="stat-item mb-4">
                                    <div class="stat-number display-6 fw-bold text-white">100%</div>
                                    <div class="stat-label text-white-75">Transparent</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number display-6 fw-bold text-white">You</div>
                                    <div class="stat-label text-white-75">Control</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Cookie Policy Content -->
<div class="container-fluid">
    <!-- Cookie Types -->
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Types of Cookies We Use</h2>
                <p class="lead text-muted">Understanding how we use cookies to improve your experience</p>
            </div>
        </div>

        <!-- Essential Cookies -->
        <div class="col-lg-4 col-md-6">
            <div class="cookie-card card border-0 shadow-lg h-100">
                <div class="card-header bg-gradient-primary text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="cookie-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-cogs fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">Essential Cookies</h5>
                            <p class="mb-0 text-white-75 small">Required for basic functionality</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <p class="mb-3">These cookies are necessary for the website to function and cannot be switched off in our systems.</p>
                    <ul class="cookie-list list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Authentication</strong><br>
                            <small class="text-muted">Keep you logged in securely</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Security</strong><br>
                            <small class="text-muted">Protect against CSRF attacks</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Session Management</strong><br>
                            <small class="text-muted">Maintain your session state</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Load Balancing</strong><br>
                            <small class="text-muted">Ensure optimal performance</small>
                        </li>
                    </ul>
                    <div class="cookie-control mt-4">
                        <span class="badge bg-primary">Always Active</span>
                    </div>
                </div>
            </div>
        </div>

        <!-- Analytics Cookies -->
        <div class="col-lg-4 col-md-6">
            <div class="cookie-card card border-0 shadow-lg h-100">
                <div class="card-header bg-gradient-success text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="cookie-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-chart-line fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">Analytics Cookies</h5>
                            <p class="mb-0 text-white-75 small">Help us improve our service</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <p class="mb-3">These cookies allow us to count visits and traffic sources so we can measure and improve performance.</p>
                    <ul class="cookie-list list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Usage Analytics</strong><br>
                            <small class="text-muted">Track feature usage patterns</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Performance Metrics</strong><br>
                            <small class="text-muted">Monitor page load times</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Error Tracking</strong><br>
                            <small class="text-muted">Identify and fix issues</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>User Journey</strong><br>
                            <small class="text-muted">Understand user behavior</small>
                        </li>
                    </ul>
                    <div class="cookie-control mt-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="analyticsToggle" checked>
                            <label class="form-check-label fw-bold" for="analyticsToggle">
                                Enable Analytics
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Preference Cookies -->
        <div class="col-lg-4 col-md-6">
            <div class="cookie-card card border-0 shadow-lg h-100">
                <div class="card-header bg-gradient-info text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="cookie-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-user-cog fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">Preference Cookies</h5>
                            <p class="mb-0 text-white-75 small">Remember your settings</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <p class="mb-3">These cookies enable the website to provide enhanced functionality and personalization.</p>
                    <ul class="cookie-list list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Theme Preferences</strong><br>
                            <small class="text-muted">Remember dark/light mode</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Language Settings</strong><br>
                            <small class="text-muted">Store language preferences</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Dashboard Layout</strong><br>
                            <small class="text-muted">Remember customizations</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Notification Settings</strong><br>
                            <small class="text-muted">Store notification preferences</small>
                        </li>
                    </ul>
                    <div class="cookie-control mt-4">
                        <div class="form-check form-switch">
                            <input class="form-check-input" type="checkbox" id="preferencesToggle" checked>
                            <label class="form-check-label fw-bold" for="preferencesToggle">
                                Enable Preferences
                            </label>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cookie Management -->
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Manage Your Cookie Preferences</h2>
                <p class="lead text-muted">You have full control over your cookie settings</p>
            </div>
        </div>

        <div class="col-lg-8 mx-auto">
            <div class="cookie-management card border-0 shadow-lg">
                <div class="card-header bg-gradient-warning text-white border-0 py-4 text-center">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-sliders-h me-2"></i>Cookie Settings
                    </h5>
                </div>
                <div class="card-body p-5">
                    <div class="cookie-setting mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">Essential Cookies</h6>
                                <p class="text-muted small mb-0">Required for basic website functionality</p>
                            </div>
                            <div>
                                <span class="badge bg-secondary">Always On</span>
                            </div>
                        </div>
                    </div>

                    <div class="cookie-setting mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">Analytics Cookies</h6>
                                <p class="text-muted small mb-0">Help us improve our service</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="analyticsMain" checked>
                                <label class="form-check-label" for="analyticsMain"></label>
                            </div>
                        </div>
                    </div>

                    <div class="cookie-setting mb-4">
                        <div class="d-flex justify-content-between align-items-center">
                            <div>
                                <h6 class="fw-bold mb-1">Preference Cookies</h6>
                                <p class="text-muted small mb-0">Remember your settings and preferences</p>
                            </div>
                            <div class="form-check form-switch">
                                <input class="form-check-input" type="checkbox" id="preferencesMain" checked>
                                <label class="form-check-label" for="preferencesMain"></label>
                            </div>
                        </div>
                    </div>

                    <hr class="my-4">

                    <div class="text-center">
                        <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                            <button type="button" class="btn btn-primary" onclick="savePreferences()">
                                <i class="fas fa-save me-2"></i>Save Preferences
                            </button>
                            <button type="button" class="btn btn-outline-secondary" onclick="acceptAll()">
                                <i class="fas fa-check me-2"></i>Accept All
                            </button>
                            <button type="button" class="btn btn-outline-danger" onclick="rejectAll()">
                                <i class="fas fa-times me-2"></i>Reject Optional
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Cookie Information -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient-dark text-white border-0 py-4 text-center">
                    <h4 class="mb-0 fw-bold">
                        <i class="fas fa-info-circle me-2"></i>Additional Information
                    </h4>
                </div>
                <div class="card-body p-5">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <h5 class="fw-bold mb-3">How to Control Cookies</h5>
                            <p class="mb-3">You can control and/or delete cookies as you wish. You can delete all cookies that are already on your computer and you can set most browsers to prevent them from being placed.</p>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fab fa-chrome text-warning me-2"></i>
                                    <strong>Chrome:</strong> Settings > Privacy and security > Cookies
                                </li>
                                <li class="mb-2">
                                    <i class="fab fa-firefox text-orange me-2"></i>
                                    <strong>Firefox:</strong> Options > Privacy & Security > Cookies
                                </li>
                                <li class="mb-2">
                                    <i class="fab fa-safari text-primary me-2"></i>
                                    <strong>Safari:</strong> Preferences > Privacy > Cookies
                                </li>
                                <li class="mb-0">
                                    <i class="fab fa-edge text-info me-2"></i>
                                    <strong>Edge:</strong> Settings > Cookies and site permissions
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="fw-bold mb-3">Third-Party Services</h5>
                            <p class="mb-3">We use the following third-party services that may set cookies:</p>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-chart-bar text-primary me-2"></i>
                                    <strong>Google Analytics:</strong> Website analytics
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-shield-alt text-success me-2"></i>
                                    <strong>Cloudflare:</strong> Security and performance
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-comments text-info me-2"></i>
                                    <strong>Intercom:</strong> Customer support chat
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-video text-danger me-2"></i>
                                    <strong>YouTube:</strong> Embedded videos
                                </li>
                            </ul>
                            <p class="mt-3">
                                <small class="text-muted">
                                    Each service has its own privacy policy and cookie settings.
                                </small>
                            </p>
                        </div>
                    </div>
                    <hr class="my-4">
                    <div class="text-center">
                        <p class="mb-3"><strong>Questions about our Cookie Policy?</strong></p>
                        <p class="mb-3">Contact us at <strong><EMAIL></strong></p>
                        <p class="text-muted small">Last updated: {{ "now"|date:"F d, Y" }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
/* Cookie Policy Hero Section */
.cookies-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Cookie Stats */
.cookies-stats .stat-item {
    transition: all 0.3s ease;
}

.cookies-stats .stat-item:hover {
    transform: translateY(-5px);
}

/* Cookie Cards */
.cookie-card {
    transition: all 0.3s ease;
    border-radius: 1rem;
    overflow: hidden;
}

.cookie-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.cookie-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Cookie Lists */
.cookie-list li {
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.cookie-list li:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(10px);
}

/* Cookie Controls */
.cookie-control {
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    border-radius: 0.5rem;
}

.form-check-input:checked {
    background-color: #667eea;
    border-color: #667eea;
}

/* Cookie Settings */
.cookie-setting {
    padding: 1rem;
    border-radius: 0.5rem;
    transition: all 0.3s ease;
}

.cookie-setting:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* Buttons */
.btn {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Cards */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .cookies-hero .hero-content {
        padding: 2rem !important;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .cookies-stats .stat-number {
        font-size: 2rem;
    }

    .cookie-card .card-header {
        padding: 1.5rem !important;
    }

    .cookie-icon {
        width: 40px;
        height: 40px;
    }

    .d-grid.gap-2.d-md-flex {
        gap: 0.5rem !important;
    }
}
</style>

<script>
function savePreferences() {
    const analytics = document.getElementById('analyticsMain').checked;
    const preferences = document.getElementById('preferencesMain').checked;

    // Save to localStorage
    localStorage.setItem('cookiePreferences', JSON.stringify({
        analytics: analytics,
        preferences: preferences,
        timestamp: Date.now()
    }));

    // Show success message
    alert('Cookie preferences saved successfully!');
}

function acceptAll() {
    document.getElementById('analyticsMain').checked = true;
    document.getElementById('preferencesMain').checked = true;
    document.getElementById('analyticsToggle').checked = true;
    document.getElementById('preferencesToggle').checked = true;
    savePreferences();
}

function rejectAll() {
    document.getElementById('analyticsMain').checked = false;
    document.getElementById('preferencesMain').checked = false;
    document.getElementById('analyticsToggle').checked = false;
    document.getElementById('preferencesToggle').checked = false;
    savePreferences();
}

// Sync toggles
document.getElementById('analyticsMain').addEventListener('change', function() {
    document.getElementById('analyticsToggle').checked = this.checked;
});

document.getElementById('preferencesMain').addEventListener('change', function() {
    document.getElementById('preferencesToggle').checked = this.checked;
});

document.getElementById('analyticsToggle').addEventListener('change', function() {
    document.getElementById('analyticsMain').checked = this.checked;
});

document.getElementById('preferencesToggle').addEventListener('change', function() {
    document.getElementById('preferencesMain').checked = this.checked;
});

// Load saved preferences on page load
document.addEventListener('DOMContentLoaded', function() {
    const saved = localStorage.getItem('cookiePreferences');
    if (saved) {
        const prefs = JSON.parse(saved);
        document.getElementById('analyticsMain').checked = prefs.analytics;
        document.getElementById('preferencesMain').checked = prefs.preferences;
        document.getElementById('analyticsToggle').checked = prefs.analytics;
        document.getElementById('preferencesToggle').checked = prefs.preferences;
    }
});
</script>
{% endblock %}
{% endblock %}
