{% extends 'corporate/base.html' %}

{% block title %}Verify Certificate - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-certificate me-2"></i>Certificate Verification</h4>
            </div>
            <div class="card-body">
                <form method="get" class="mb-4">
                    <div class="input-group">
                        <input type="text" name="code" class="form-control" placeholder="Enter certificate verification code" value="{{ verification_code }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Verify
                        </button>
                    </div>
                </form>

                {% if verification_code and not certificate %}
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>Invalid verification code. The certificate could not be found.
                </div>
                {% endif %}

                {% if certificate %}
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle me-2"></i>Certificate verified successfully!
                </div>

                <div class="certificate-preview mb-4">
                    <div class="certificate-inner">
                        <div class="certificate-header">
                            <h3>Certificate of Completion</h3>
                        </div>
                        <div class="certificate-content">
                            <div class="certificate-name">{{ certificate.user.get_full_name|default:certificate.user.username }}</div>
                            <p class="certificate-text">{{ certificate.description }}</p>
                            <p class="certificate-role">Highest Role: {{ certificate.highest_role|title }}</p>
                            <p class="certificate-score">Final Score: {{ certificate.final_score }}</p>

                            {% if certificate.get_completed_games %}
                            <div class="certificate-games">
                                <p class="certificate-games-title">Completed Games:</p>
                                <ul class="certificate-games-list">
                                    {% for game in certificate.get_completed_games %}
                                    <li>{{ game }}</li>
                                    {% endfor %}
                                </ul>
                            </div>
                            {% endif %}

                            <p class="certificate-date">Issued on {{ certificate.issue_date|date:"F d, Y" }}</p>
                            <p class="certificate-verification">Verification Code: {{ certificate.verification_code }}</p>

                            <div class="certificate-signatures">
                                <div class="signature">
                                    <div class="signature-line"></div>
                                    <div class="signature-name">Allan Scofield</div>
                                    <div class="signature-title">CEO, 24seven Assistants</div>
                                </div>
                            </div>

                            <div class="certificate-seal">
                                CERTIFIED
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Certificate Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Recipient:</strong> {{ certificate.user.get_full_name|default:certificate.user.username }}</p>
                                <p><strong>Title:</strong> {{ certificate.title }}</p>
                                <p><strong>Issue Date:</strong> {{ certificate.issue_date|date:"F d, Y" }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Final Score:</strong> {{ certificate.final_score }}</p>
                                <p><strong>Highest Role:</strong> {{ certificate.highest_role|title }}</p>
                                <p><strong>Verification Code:</strong> {{ certificate.verification_code }}</p>
                            </div>
                        </div>
                    </div>
                </div>

                {% if certificate.user.corporate_profile %}
                <div class="card">
                    <div class="card-header">
                        <h5 class="mb-0">Company Information</h5>
                    </div>
                    <div class="card-body">
                        <p><strong>Company:</strong> {{ certificate.user.corporate_profile.company.name }}</p>
                        <p><strong>Job Title:</strong> {{ certificate.user.corporate_profile.job_title|default:"Not specified" }}</p>
                        <p><strong>Department:</strong> {{ certificate.user.corporate_profile.department|default:"Not specified" }}</p>
                    </div>
                </div>
                {% endif %}
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
    .certificate-preview {
        border: 1px solid #ddd;
        padding: 20px;
        background-color: #f9f9f9;
        border-radius: 5px;
        margin: 0 auto;
        max-width: 800px;
    }

    .certificate-inner {
        background: #111111 url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAEklEQVQImWNgYGD4z0AswK4SAFXuAf8EPy+xAAAAAElFTkSuQmCC') repeat;
        border: 10px solid #d4af37;
        padding: 30px;
        text-align: center;
        position: relative;
        min-height: 500px;
        box-shadow: 0 5px 15px rgba(0,0,0,0.5);
    }

    .certificate-header h3 {
        color: #d4af37;
        font-size: 28px;
        margin-bottom: 20px;
        text-transform: uppercase;
        font-weight: bold;
        text-shadow: 1px 1px 5px rgba(0,0,0,0.5);
        letter-spacing: 2px;
    }

    .certificate-name {
        font-size: 36px;
        font-weight: bold;
        margin: 0 auto 20px;
        border-bottom: 2px solid #d4af37;
        display: inline-block;
        padding-bottom: 5px;
        color: #ffffff;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    }

    .certificate-content {
        padding: 10px;
    }

    .certificate-text, .certificate-role, .certificate-score, .certificate-date, .certificate-verification {
        margin-bottom: 15px;
        font-size: 16px;
        color: #d4af37;
    }

    .certificate-role, .certificate-score {
        font-weight: bold;
        color: #ffffff;
        font-size: 18px;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    }

    .certificate-games {
        margin: 20px auto;
        padding: 15px;
        border: 2px solid #d4af37;
        border-radius: 8px;
        background-color: rgba(0, 0, 0, 0.5);
        max-width: 80%;
        box-shadow: 0 2px 5px rgba(0,0,0,0.3);
    }

    .certificate-games-title {
        font-weight: bold;
        color: #d4af37;
        margin-bottom: 10px;
        text-align: center;
        font-size: 18px;
        text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    }

    .certificate-games-list {
        list-style-type: none;
        padding: 0;
        margin: 0;
        text-align: center;
    }

    .certificate-games-list li {
        font-weight: bold;
        padding: 5px 0;
        font-size: 16px;
        color: #ffffff;
    }

    .certificate-signatures {
        display: flex;
        justify-content: space-around;
        margin: 30px auto 20px;
        width: 80%;
    }

    .signature {
        text-align: center;
        width: 200px;
    }

    .signature-line {
        width: 100%;
        border-bottom: 1px solid #d4af37;
        margin: 0 auto 10px;
    }

    .signature-name {
        font-weight: bold;
        font-size: 16px;
        color: #d4af37;
    }

    .signature-title {
        font-size: 14px;
        color: #ffffff;
    }

    .certificate-seal {
        position: absolute;
        bottom: 80px;
        right: 60px;
        width: 100px;
        height: 100px;
        background: radial-gradient(circle, #d4af37, #b8860b);
        border-radius: 50%;
        display: flex;
        align-items: center;
        justify-content: center;
        color: white;
        font-weight: bold;
        font-size: 16px;
        transform: rotate(-15deg);
        border: 2px solid #8B4513;
        box-shadow: 0 2px 10px rgba(139, 69, 19, 0.3);
    }

    .certificate-date {
        margin-top: 20px;
        font-style: italic;
    }

    .certificate-verification {
        font-size: 14px;
        color: #a0a0a0;
    }

    /* Responsive adjustments */
    @media (max-width: 768px) {
        .certificate-inner {
            padding: 15px;
            min-height: auto;
        }

        .certificate-name {
            font-size: 28px;
        }

        .certificate-header h3 {
            font-size: 22px;
        }

        .certificate-signatures {
            flex-direction: column;
            align-items: center;
        }

        .signature {
            margin-bottom: 20px;
        }

        .certificate-seal {
            position: relative;
            bottom: auto;
            right: auto;
            margin: 20px auto;
        }
    }
</style>
{% endblock %}
