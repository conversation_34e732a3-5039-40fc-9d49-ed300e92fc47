{% load i18n %}
<!-- Enhanced Professional Header -->
<header class="main-header">
    <nav class="navbar navbar-expand-lg navbar-light bg-white shadow-sm">
        <div class="container-fluid px-4">
            <!-- Brand -->
            <a class="navbar-brand d-flex align-items-center fw-bold" href="/">
                <div class="bg-gradient-primary rounded-circle p-2 me-3">
                    <i class="fas fa-brain text-white"></i>
                </div>
                <span class="text-primary">Corporate Prompt Master</span>
            </a>

            <!-- Mobile Toggle -->
            <div class="d-flex align-items-center order-lg-3">
                <!-- Sidebar Toggle (Mobile) -->
                <button class="btn btn-outline-primary d-lg-none me-2" type="button" id="sidebarToggle"
                        title="Toggle Sidebar" aria-label="Toggle Sidebar">
                    <i class="fas fa-bars"></i>
                </button>

                <!-- Navbar Toggle -->
                <button class="navbar-toggler border-0" type="button" data-bs-toggle="collapse"
                        data-bs-target="#navbarNav" title="Toggle Navigation" aria-label="Toggle Navigation">
                    <i class="fas fa-ellipsis-v"></i>
                </button>
            </div>

            <!-- Navigation -->
            <div class="collapse navbar-collapse order-lg-2" id="navbarNav">
                <ul class="navbar-nav me-auto">
                    <li class="nav-item">
                        <a class="nav-link fw-semibold" href="/">
                            <i class="fas fa-home me-2"></i>{% trans "Home" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-semibold" href="/game/">
                            <i class="fas fa-gamepad me-2"></i>{% trans "Play Game" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link fw-semibold" href="/leaderboard/">
                            <i class="fas fa-trophy me-2"></i>{% trans "Leaderboard" %}
                        </a>
                    </li>
                    {% if request.user.is_superuser %}
                    <li class="nav-item">
                        <a class="nav-link fw-semibold text-danger" href="{% url 'superadmin:dashboard' %}">
                            <i class="fas fa-shield-alt me-2"></i>{% trans "Superadmin" %}
                        </a>
                    </li>
                    {% endif %}
                </ul>

                <!-- Right Side Navigation -->
                <ul class="navbar-nav align-items-center">
                    <!-- Help Button -->
                    <li class="nav-item me-3">
                        <a class="btn btn-outline-info btn-sm" href="{% url 'game:help_redirect' %}" target="_blank">
                            <i class="fas fa-question-circle me-1"></i>{% trans "Help" %}
                        </a>
                    </li>

                    <!-- User Account -->
                    {% if request.user.is_authenticated %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle d-flex align-items-center" href="#" id="userDropdown"
                           role="button" data-bs-toggle="dropdown" aria-expanded="false">
                            <div class="bg-gradient-primary rounded-circle p-1 me-2">
                                <i class="fas fa-user text-white"></i>
                            </div>
                            <span class="fw-semibold">{{ request.user.username }}</span>
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end shadow border-0" aria-labelledby="userDropdown">
                            <li class="dropdown-header">
                                <div class="text-center">
                                    <div class="bg-gradient-primary rounded-circle p-2 d-inline-block mb-2">
                                        <i class="fas fa-user text-white"></i>
                                    </div>
                                    <div class="fw-bold">{{ request.user.get_full_name|default:request.user.username }}</div>
                                    <small class="text-muted">{{ request.user.email }}</small>
                                </div>
                            </li>
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="/profile/">
                                    <i class="fas fa-user-circle me-3 text-primary"></i>
                                    <span>{% trans "Profile" %}</span>
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="{% url 'corporate:corporate_register' %}">
                                    <i class="fas fa-building me-3 text-success"></i>
                                    <span>{% trans "Create Company" %}</span>
                                </a>
                            </li>
                            {% if request.user.is_staff %}
                            <li>
                                <a class="dropdown-item d-flex align-items-center" href="{% url 'admin:index' %}">
                                    <i class="fas fa-cog me-3 text-warning"></i>
                                    <span>{% trans "Admin" %}</span>
                                </a>
                            </li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item d-flex align-items-center text-danger" href="/logout/">
                                    <i class="fas fa-sign-out-alt me-3"></i>
                                    <span>{% trans "Logout" %}</span>
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <li class="nav-item">
                        <a class="btn btn-outline-primary me-2" href="/login/">
                            <i class="fas fa-sign-in-alt me-1"></i>{% trans "Login" %}
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="btn btn-primary" href="/register/">
                            <i class="fas fa-user-plus me-1"></i>{% trans "Register" %}
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
</header>
