/* Corporate Prompt Master - Context Aware Edition Styles - Enhanced Professional Design */

/* Import modern fonts */
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700&family=JetBrains+Mono:wght@400;500&display=swap');

/* CSS Custom Properties for Enhanced Design System */
:root {
    /* Enhanced Color Palette */
    --primary-gradient: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    --primary-color: #667eea;
    --primary-dark: #5a67d8;
    --primary-light: #7c8aed;

    --secondary-gradient: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    --secondary-color: #f093fb;

    --success-gradient: linear-gradient(135deg, #4facfe 0%, #00f2fe 100%);
    --success-color: #4facfe;

    --warning-gradient: linear-gradient(135deg, #ffecd2 0%, #fcb69f 100%);
    --warning-color: #fcb69f;

    --danger-gradient: linear-gradient(135deg, #ff9a9e 0%, #fecfef 100%);
    --danger-color: #ff9a9e;

    /* Dark Theme Colors */
    --bg-primary: #0f0f23;
    --bg-secondary: #1a1a2e;
    --bg-tertiary: #16213e;
    --bg-card: #1e1e3f;
    --bg-elevated: #252547;

    /* Text Colors */
    --text-primary: #ffffff;
    --text-secondary: #b8bcc8;
    --text-muted: #8b8fa3;
    --text-accent: #667eea;

    /* Border Colors */
    --border-primary: #2d2d4a;
    --border-secondary: #3a3a5c;
    --border-accent: #667eea;

    /* Shadows */
    --shadow-sm: 0 2px 4px rgba(0, 0, 0, 0.1);
    --shadow-md: 0 4px 12px rgba(0, 0, 0, 0.15);
    --shadow-lg: 0 8px 24px rgba(0, 0, 0, 0.2);
    --shadow-xl: 0 16px 48px rgba(0, 0, 0, 0.25);
    --shadow-glow: 0 0 20px rgba(102, 126, 234, 0.3);

    /* Spacing */
    --space-xs: 0.25rem;
    --space-sm: 0.5rem;
    --space-md: 1rem;
    --space-lg: 1.5rem;
    --space-xl: 2rem;
    --space-2xl: 3rem;

    /* Border Radius */
    --radius-sm: 0.375rem;
    --radius-md: 0.5rem;
    --radius-lg: 0.75rem;
    --radius-xl: 1rem;

    /* Transitions */
    --transition-fast: 0.15s ease-out;
    --transition-normal: 0.3s ease-out;
    --transition-slow: 0.5s ease-out;
}

/* Reset and base styles */
html {
    -webkit-text-size-adjust: 100%;
    -ms-text-size-adjust: 100%;
    text-size-adjust: 100%;
    touch-action: manipulation;
    height: 100%;
    overscroll-behavior: none;
    scroll-behavior: smooth;
}

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
    -webkit-tap-highlight-color: transparent;
}

body {
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    background: var(--bg-primary);
    color: var(--text-primary);
    line-height: 1.6;
    height: 100%;
    width: 100vw;
    max-width: 100vw;
    overflow-x: hidden;
    overflow-y: auto;
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
    font-weight: 400;
    margin: 0 !important;
    padding: 0 !important;
}

.hidden {
    display: none !important;
}

/* Override Bootstrap container constraints */
.container, .container-fluid, .container-sm, .container-md, .container-lg, .container-xl, .container-xxl {
    max-width: none !important;
    width: 100% !important;
    padding-left: 0 !important;
    padding-right: 0 !important;
    margin-left: 0 !important;
    margin-right: 0 !important;
}

/* Enhanced Full-Width App Container */
.app-container {
    display: flex;
    min-height: 100vh;
    height: 100vh;
    min-height: calc(var(--vh, 1vh) * 100);
    width: 100vw;
    max-width: 100vw;
    margin: 0 !important;
    padding: 0 !important;
    background: var(--bg-primary);
    position: relative;
    overflow: hidden;
}

/* Enhanced Compact Sidebar */
.sidebar {
    width: 280px;
    min-width: 280px;
    max-width: 280px;
    height: 100vh;
    min-height: 100vh;
    background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-right: 1px solid var(--border-primary);
    padding: var(--space-md);
    display: flex;
    flex-direction: column;
    overflow-y: auto;
    position: relative;
    transition: all var(--transition-normal);
    flex-shrink: 0;
}

.sidebar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

/* Enhanced Sidebar Header */
.sidebar-header {
    display: flex;
    align-items: flex-start;
    margin-bottom: var(--space-xl);
    padding-bottom: var(--space-lg);
    border-bottom: 1px solid var(--border-primary);
    position: relative;
}

.sidebar-collapse-indicator {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 32px;
    height: 32px;
    margin-right: var(--space-md);
    margin-top: var(--space-xs);
    color: var(--text-muted);
    font-size: 0.875rem;
    cursor: pointer;
    border-radius: var(--radius-md);
    background: var(--bg-elevated);
    border: 1px solid var(--border-secondary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.sidebar-collapse-indicator:hover {
    background: var(--bg-card);
    color: var(--text-secondary);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.logo {
    flex: 1;
    text-align: center;
}

.logo h1 {
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--text-primary);
    margin-bottom: var(--space-xs);
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

.subtitle {
    font-size: 0.875rem;
    color: var(--text-muted);
    font-weight: 400;
    font-style: italic;
}

/* Enhanced Compact Character Info */
.character-info {
    text-align: center;
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
    position: relative;
    overflow: hidden;
    word-wrap: break-word;
    overflow-wrap: break-word;
}

.character-info::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 2px;
    background: var(--primary-gradient);
}

.character-header {
    font-size: 0.875rem;
    color: var(--text-muted);
    margin-bottom: var(--space-md);
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    line-height: 1.3;
}

.avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    background: var(--primary-gradient);
    color: white;
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 2rem;
    margin: 0 auto var(--space-md);
    box-shadow: var(--shadow-lg);
    border: 3px solid var(--bg-elevated);
    transition: all var(--transition-normal);
}

.avatar:hover {
    transform: scale(1.05);
    box-shadow: var(--shadow-glow);
}

.character-name {
    font-weight: 600;
    font-size: 1.125rem;
    margin-bottom: var(--space-xs);
    color: var(--text-primary);
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    line-height: 1.3;
}

.character-title {
    font-size: 0.875rem;
    color: var(--text-secondary);
    font-weight: 400;
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    line-height: 1.3;
}

.character-department {
    font-size: 0.75rem;
    color: var(--text-primary);
    margin-top: var(--space-sm);
    padding: var(--space-xs) var(--space-sm);
    background: var(--secondary-gradient);
    border-radius: var(--radius-xl);
    display: inline-block;
    font-weight: 500;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    box-shadow: var(--shadow-sm);
}

/* Enhanced Compact Game Stats */
.game-stats {
    margin-bottom: var(--space-lg);
    padding: var(--space-md);
    background: var(--bg-card);
    border-radius: var(--radius-md);
    border: 1px solid var(--border-primary);
    box-shadow: var(--shadow-sm);
}

.stat {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--space-sm);
    padding: var(--space-xs) 0;
    border-bottom: 1px solid var(--border-primary);
    transition: all var(--transition-fast);
    min-height: 24px;
}

.stat:last-child {
    border-bottom: none;
    margin-bottom: 0;
}

.stat:hover {
    background: var(--bg-elevated);
    margin: 0 calc(-1 * var(--space-sm));
    padding: var(--space-sm);
    border-radius: var(--radius-md);
}

.stat-label {
    color: var(--text-secondary);
    font-weight: 500;
    font-size: 0.8rem;
    line-height: 1.2;
    flex: 1;
    margin-right: 8px;
    word-wrap: break-word;
    overflow-wrap: break-word;
    -webkit-hyphens: auto;
    hyphens: auto;
}

.stat-value {
    font-weight: 700;
    color: var(--text-primary);
    font-size: 0.9rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    flex-shrink: 0;
    text-align: right;
    min-width: -webkit-fill-available;
    min-width: fit-content;
}

/* Enhanced Progress Container */
.progress-container {
    height: 12px;
    background: var(--bg-elevated);
    border-radius: var(--radius-xl);
    overflow: hidden;
    margin-top: var(--space-md);
    border: 1px solid var(--border-primary);
    box-shadow: inset 0 2px 4px rgba(0, 0, 0, 0.1);
}

.progress-bar {
    height: 100%;
    background: var(--success-gradient);
    width: 0%;
    transition: width 0.6s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
    border-radius: var(--radius-xl);
    box-shadow: 0 2px 8px rgba(79, 172, 254, 0.3);
}

.progress-bar::after {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: linear-gradient(90deg, transparent 0%, rgba(255, 255, 255, 0.2) 50%, transparent 100%);
    animation: shimmer 2s infinite;
}

@keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
}

.stat-description {
    font-size: 0.8rem;
    color: var(--text-muted);
    margin-top: var(--space-sm);
    margin-bottom: var(--space-lg);
    text-align: center;
    font-style: italic;
    line-height: 1.5;
}

/* Organization Chart */
.org-chart-container {
    margin-bottom: 20px;
    padding-bottom: 15px;
    border-bottom: 1px solid #e0e0e0;
}

.org-chart-container h3 {
    margin-bottom: 10px;
    font-size: 0.9rem;
    color: #d1d5d9;
}

.org-chart {
    display: flex;
    flex-direction: column-reverse;
    gap: 8px;
    padding: 5px;
    background-color: #f8f9fa;
    border-radius: 5px;
    border: 1px solid #e0e0e0;
    max-height: 300px;
    overflow-y: auto;
}

/* Hierarchy visualization */
.hierarchy-level {
    margin-bottom: 8px;
    padding: 5px;
    border-radius: 4px;
    background-color: #f5f5f5;
}

.hierarchy-role {
    display: flex;
    flex-direction: column;
    align-items: center;
    padding: 5px;
    margin: 3px;
    border-radius: 4px;
    font-size: 0.7rem;
    transition: all 0.2s ease;
    border: 1px solid #e0e0e0;
    background-color: #fff;
    width: 100%;
}

.hierarchy-role.current-role {
    border: 2px solid #4a86e8;
    background-color: #e3f2fd;
    transform: scale(1.05);
    z-index: 1;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.1);
}

.hierarchy-role.completed-role {
    border: 1px solid #4caf50;
    background-color: #e8f5e9;
}

.hierarchy-role.future-role {
    opacity: 0.7;
}

.role-indicator {
    font-size: 0.9rem;
    margin-bottom: 2px;
}

.role-title {
    font-weight: bold;
    margin-bottom: 2px;
    text-align: center;
}

.role-department {
    font-size: 0.7rem;
    color: #666;
    background-color: #f0f0f0;
    padding: 1px 5px;
    border-radius: 8px;
}

/* Department colors */
.dept-hr {
    border-left: 3px solid #4a86e8;
}

.dept-marketing {
    border-left: 3px solid #e91e63;
}

.dept-operations {
    border-left: 3px solid #f57c00;
}

.dept-finance {
    border-left: 3px solid #43a047;
}

.dept-executive {
    border-left: 3px solid #7b1fa2;
}

.dept-board {
    border-left: 3px solid #5d4037;
}

.org-chart-content {
    width: 100%;
}

.org-level {
    display: flex;
    flex-direction: column;
    margin-bottom: 10px;
    position: relative;
}

.level-title {
    font-weight: bold;
    font-size: 0.8rem;
    margin-bottom: 5px;
    color: #666;
    text-align: center;
    background-color: #f0f0f0;
    padding: 3px;
    border-radius: 3px;
}

.level-roles {
    display: flex;
    flex-wrap: wrap;
    justify-content: center;
    gap: 5px;
}

.org-node {
    padding: 6px 8px;
    background-color: #e9ecef;
    border-radius: 4px;
    font-size: 0.7rem;
    text-align: center;
    border: 1px solid #dee2e6;
    transition: all 0.3s ease;
    position: relative;
    margin: 2px;
    min-width: 80px;
}

.node-indicator {
    position: absolute;
    top: 2px;
    right: 2px;
    font-size: 0.7rem;
}

.node-title {
    font-size: 0.7rem;
    white-space: nowrap;
    overflow: hidden;
    text-overflow: ellipsis;
}

/* Completed nodes */
.org-node.completed {
    background-color: #d4edda;
    border-color: #c3e6cb;
    color: #155724;
}

/* Current node */
.org-node.current {
    background-color: #cce5ff;
    border-color: #b8daff;
    color: #004085;
    font-weight: bold;
    box-shadow: 0 0 5px rgba(0, 123, 255, 0.5);
    transform: scale(1.05);
    z-index: 1;
}

/* Future nodes */
.org-node.future {
    background-color: #f8f9fa;
    border-color: #dee2e6;
    color: #6c757d;
    opacity: 0.7;
}

/* Placeholder for loading */
.org-chart-placeholder {
    padding: 10px;
    text-align: center;
    color: #666;
    font-style: italic;
    background-color: #f8f9fa;
    border-radius: 4px;
    border: 1px dashed #ccc;
}

.instructions {
    font-size: 0.9rem;
}

.instructions h3 {
    margin-bottom: 10px;
}

.instructions ol {
    margin-left: 20px;
    margin-bottom: 10px;
}

.instructions li {
    margin-bottom: 5px;
}

/* Context awareness indicators */
.context-info {
    margin-top: 15px;
    padding: 12px;
    background-color: #f0f7ff;
    border-radius: 5px;
    font-size: 0.9em;
    border: 1px solid #d0e3ff;
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.05);
}

.context-info div {
    margin-bottom: 8px;
    display: flex;
    align-items: center;
}

.context-label {
    font-weight: bold;
    color: #0066cc;
    display: inline-block;
    width: 120px;
}

#current-manager, #current-task {
    font-weight: 500;
    color: #333;
    background-color: #e6f0ff;
    padding: 2px 8px;
    border-radius: 3px;
    border: 1px solid #c0d6ff;
}

/* Enhanced Main Content */
.main-content {
    flex: 1;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    transition: all var(--transition-normal);
    height: 100vh;
    min-width: 0;
    width: 100%;
    background: var(--bg-primary);
    position: relative;
    max-width: none !important;
}

/* Adjust main content when sidebar is hidden */
.sidebar-hidden .main-content {
    margin-left: 0;
}

/* Enhanced Compact Header */
.header {
    padding: var(--space-md) var(--space-lg);
    background: linear-gradient(135deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    border-bottom: 1px solid var(--border-primary);
    display: flex;
    justify-content: space-between;
    align-items: center;
    min-height: 60px;
    box-shadow: var(--shadow-md);
    position: relative;
}

/* Header sections for proper positioning */
.header-left {
    display: flex;
    align-items: center;
    flex: 0 0 auto;
}

.header-center {
    display: flex;
    align-items: center;
    justify-content: center;
    flex: 1 1 auto;
}

.header-right {
    display: flex;
    align-items: center;
    justify-content: flex-end;
    flex: 0 0 auto;
}

.header::before {
    content: '';
    position: absolute;
    bottom: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

/* CEO name in header styling */
.header .ceo-header-name {
    display: inline-block;
    background: linear-gradient(90deg, rgba(25, 118, 210, 0.15) 0%, transparent 100%);
    color: #1a237e;
    padding: 5px 12px;
    border-radius: 4px;
    font-weight: bold;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    margin-right: 10px;
}

.header .ceo-header-name::before {
    content: "👑";
    margin-right: 5px;
    font-size: 0.9em;
}

.dark-mode .header .ceo-header-name {
    background: linear-gradient(90deg, rgba(255, 255, 255, 0.25) 0%, transparent 100%);
    color: #ffffff;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 700;
    letter-spacing: 0.03em;
}

/* Special styling for CEO name in dark backgrounds */
.dark-bg .ceo-header-name,
.dark-header .ceo-header-name,
.dark-nav .ceo-header-name {
    background: rgba(255, 255, 255, 0.2);
    color: #ffffff;
    padding: 5px 10px;
    border-radius: 4px;
    font-weight: bold;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.5);
}

/* Enhanced Role Display */
.role-display {
    font-weight: 600;
    flex: 1;
    text-align: center;
    color: var(--text-primary);
    font-size: 1.125rem;
    background: var(--primary-gradient);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    letter-spacing: -0.025em;
}

/* Enhanced Sidebar Toggle Button */
.mobile-sidebar-toggle {
    display: flex;
    flex-direction: column;
    justify-content: space-between;
    width: 32px;
    height: 24px;
    cursor: pointer;
    margin-right: var(--space-lg);
    z-index: 10;
    position: relative;
    padding: var(--space-xs);
    border-radius: var(--radius-md);
    background: var(--bg-elevated);
    border: 1px solid var(--border-secondary);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-sm);
}

.mobile-sidebar-toggle:hover {
    background: var(--bg-card);
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.mobile-sidebar-toggle span {
    display: block;
    height: 2px;
    width: 100%;
    background-color: #333;
    border-radius: 2px;
    transition: all 0.3s ease;
}

/* Keyboard shortcut hint */
.shortcut-hint {
    position: absolute;
    top: -20px;
    left: 50%;
    transform: translateX(-50%);
    background-color: #f0f0f0;
    color: #666;
    font-size: 10px;
    padding: 2px 5px;
    border-radius: 3px;
    opacity: 0;
    transition: opacity 0.3s ease;
    pointer-events: none;
    white-space: nowrap;
    box-shadow: 0 1px 3px rgba(0,0,0,0.1);
}

.mobile-sidebar-toggle:hover .shortcut-hint {
    opacity: 1;
}

/* Sidebar toggle animations */
.sidebar-hidden .mobile-sidebar-toggle span:nth-child(1) {
    transform: translateY(8px) rotate(45deg);
}

.sidebar-hidden .mobile-sidebar-toggle span:nth-child(2) {
    opacity: 0;
}

.sidebar-hidden .mobile-sidebar-toggle span:nth-child(3) {
    transform: translateY(-8px) rotate(-45deg);
}

/* Sidebar transition */
.sidebar {
    transition: width 0.3s ease, max-height 0.3s ease, opacity 0.3s ease;
}

.sidebar-hidden .sidebar {
    width: 0;
    padding: 0;
    overflow: hidden;
    opacity: 0;
    border: none;
}

/* Messages container */
.messages-container {
    flex: 1;
    overflow-y: auto; /* Keep this to allow scrolling of messages */
    padding: var(--space-lg);
    background-color: #f9f9f9;
    width: 100%;
    max-width: none !important;
    /* Height will be controlled by JavaScript */
}

/* Enhanced Compact Message Styles */
.message {
    display: flex;
    margin-bottom: var(--space-md);
    max-width: 90%;
    animation: messageSlideIn 0.4s cubic-bezier(0.4, 0, 0.2, 1);
    position: relative;
}

@keyframes messageSlideIn {
    from {
        opacity: 0;
        transform: translateY(20px) scale(0.95);
    }
    to {
        opacity: 1;
        transform: translateY(0) scale(1);
    }
}

.message.from-you {
    margin-left: auto;
    flex-direction: row-reverse;
}

/* Highlighted message for new tasks */
.message.highlight-message {
    animation: highlight-pulse 2s ease-in-out;
    box-shadow: 0 0 15px rgba(74, 134, 232, 0.7);
    border-left: 5px solid #4a86e8;
    border-right: 5px solid #4a86e8;
    transform-origin: center;
}

/* Highlight pulse animation */
@keyframes highlight-pulse {
    0% {
        box-shadow: 0 0 5px rgba(74, 134, 232, 0.3);
        transform: scale(1);
    }
    50% {
        box-shadow: 0 0 20px rgba(74, 134, 232, 0.8);
        transform: scale(1.03);
    }
    100% {
        box-shadow: 0 0 5px rgba(74, 134, 232, 0.3);
        transform: scale(1);
    }
}

/* Enhanced Compact Message Content */
.message-content {
    background: var(--bg-card);
    padding: var(--space-md);
    border-radius: var(--radius-lg);
    margin: 0 var(--space-sm);
    box-shadow: var(--shadow-md);
    transition: all var(--transition-normal);
    border: 1px solid var(--border-primary);
    position: relative;
    -webkit-backdrop-filter: blur(10px);
    backdrop-filter: blur(10px);
}

.message.from-you .message-content {
    background: linear-gradient(135deg, var(--bg-tertiary) 0%, var(--bg-card) 100%);
    border-color: var(--border-accent);
}

.message-content:hover {
    box-shadow: var(--shadow-glow);
    transform: translateY(-2px);
    border-color: var(--border-accent);
}

/* AI Assistant message styling */
.message-content.ai-response {
    background-color: #f0f7ea;
    border-left: 4px solid #4caf50;
    position: relative;
}

.message-content.ai-response::before {
    content: '🤖';
    position: absolute;
    top: -8px;
    left: -8px;
    background-color: #4caf50;
    color: white;
    border-radius: 50%;
    width: 20px;
    height: 20px;
    font-size: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.2);
}

.message-content.ai-response .message-sender {
    color: #2e7d32;
}

/* System message styling */
.message.system-message {
    align-self: center;
    max-width: 90%;
    margin: 10px auto;
    background-color: #f0f7ff;
    border-radius: 8px;
    padding: 10px 15px;
    border-left: 4px solid #607d8b;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    color: #333;
    font-style: italic;
    text-align: center;
}

.message.system-message.info {
    background-color: #f0f7ff;
    border-left: 4px solid #607d8b;
    color: #333;
}

.message.system-message.error {
    background-color: #fff8f8;
    border-left: 4px solid #d32f2f;
    color: #d32f2f;
}

/* Enhanced Message Sender */
.message-sender {
    font-weight: 600;
    margin-bottom: var(--space-sm);
    font-size: 0.875rem;
    color: var(--text-primary);
    letter-spacing: 0.025em;
    position: relative;
    padding-bottom: var(--space-xs);
    text-transform: uppercase;
    font-family: 'Inter', sans-serif;
}

.message-sender::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 50px;
    height: 2px;
    background: var(--primary-gradient);
    border-radius: 1px;
    opacity: 0.8;
}

.dark-mode .message-sender {
    color: #e0e0e0;
}

.dark-mode .message-sender::after {
    background-color: rgba(255, 255, 255, 0.1);
}

/* Special styling for CEO message sender */
.message-sender-ceo {
    font-size: 1.1rem;
    color: #1a237e;
    letter-spacing: 0.02em;
    padding: 6px 0 8px 0;
    margin-bottom: 12px;
    background: linear-gradient(90deg, rgba(25, 118, 210, 0.1) 0%, transparent 100%);
    border-radius: 4px;
    position: relative;
    padding-left: 10px;
    font-weight: 700;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.05);
}

.message-sender-ceo::before {
    content: "👑";
    margin-right: 8px;
    font-size: 0.9em;
    vertical-align: middle;
}

.message-sender-ceo::after {
    content: "";
    position: absolute;
    bottom: 0;
    left: 0;
    width: 100%;
    height: 2px;
    background: linear-gradient(90deg, #1976d2 0%, transparent 100%);
    border-radius: 1px;
}

/* Add a subtle glow effect on hover */
.message-sender-ceo:hover {
    background: linear-gradient(90deg, rgba(25, 118, 210, 0.15) 0%, transparent 100%);
}

.dark-mode .message-sender-ceo {
    color: #c5e1ff; /* Much lighter blue for better contrast */
    background: linear-gradient(90deg, rgba(66, 165, 245, 0.25) 0%, transparent 100%);
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3);
    font-weight: 700;
}

.dark-mode .message-sender-ceo::after {
    background: linear-gradient(90deg, #42a5f5 0%, transparent 100%);
}

.dark-mode .message-sender-ceo:hover {
    background: linear-gradient(90deg, rgba(66, 165, 245, 0.25) 0%, transparent 100%);
}

/* Enhanced Message Text */
.message-text {
    word-break: break-word;
    line-height: 1.7;
    font-size: 0.95rem;
    color: var(--text-primary);
    font-family: 'Inter', sans-serif;
    font-weight: 400;
}

.message-text h1, .message-text h2, .message-text h3 {
    color: var(--text-primary);
    margin-top: var(--space-md);
    margin-bottom: var(--space-sm);
}

.message-text p {
    margin-bottom: var(--space-sm);
}

.message-text strong {
    color: var(--text-primary);
    font-weight: 600;
}

/* Enhanced Message Time */
.message-time {
    font-size: 0.75rem;
    color: var(--text-muted);
    margin-top: var(--space-sm);
    text-align: right;
    font-family: 'Inter', sans-serif;
    letter-spacing: 0.025em;
}

/* Enhanced Compact Input Area */
.input-area {
    padding: var(--space-sm) var(--space-lg);
    border-top: 1px solid var(--border-primary);
    background: linear-gradient(180deg, var(--bg-secondary) 0%, var(--bg-tertiary) 100%);
    box-shadow: 0 -4px 20px rgba(0, 0, 0, 0.15);
    position: relative;
    width: 100%;
    max-width: none !important;
}

.input-area::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 1px;
    background: var(--primary-gradient);
    opacity: 0.6;
}

/* Enhanced Compact Input Fields */
.prompt-input, .response-editor {
    width: 100%;
    min-height: 50px;
    padding: var(--space-sm) var(--space-md);
    border: 1px solid var(--border-secondary);
    border-radius: var(--radius-md);
    font-family: 'JetBrains Mono', 'Consolas', monospace;
    font-size: 0.875rem;
    line-height: 1.4;
    margin-bottom: var(--space-sm);
    transition: all var(--transition-normal);
    background: var(--bg-card);
    color: var(--text-primary);
    resize: vertical;
    box-shadow: var(--shadow-sm);
}

.prompt-input::placeholder, .response-editor::placeholder {
    color: var(--text-muted);
    font-style: italic;
}

.prompt-input:focus, .response-editor:focus {
    outline: none;
    border-color: var(--border-accent);
    box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.2), var(--shadow-md);
    background: var(--bg-elevated);
}

/* Enhanced Button Styles */
.preview-button, .primary-button, .secondary-button {
    padding: var(--space-sm) var(--space-lg);
    border: none;
    border-radius: var(--radius-lg);
    cursor: pointer;
    font-weight: 600;
    transition: all var(--transition-normal);
    font-size: 0.875rem;
    text-transform: uppercase;
    letter-spacing: 0.05em;
    position: relative;
    overflow: hidden;
    box-shadow: var(--shadow-md);
    font-family: 'Inter', sans-serif;
}

.preview-button::before, .primary-button::before, .secondary-button::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

.preview-button:hover::before, .primary-button:hover::before, .secondary-button:hover::before {
    left: 100%;
}

/* Enhanced Preview Button */
.preview-button {
    background: var(--primary-gradient);
    color: white;
    width: 100%;
    border: 1px solid var(--border-accent);
}

.preview-button:hover {
    background: linear-gradient(135deg, var(--primary-dark) 0%, #5a4fcf 100%);
    transform: translateY(-2px);
    box-shadow: var(--shadow-glow);
}

.preview-button:active {
    transform: translateY(0);
    box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.preview-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 15px;
}

.preview-header-actions {
    display: flex;
    align-items: center;
    gap: 10px;
}

.icon-button {
    background: none;
    border: none;
    cursor: pointer;
    font-size: 1.2rem;
    padding: 5px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: background-color 0.2s;
}

.icon-button:hover {
    background-color: #f0f0f0;
}

.zoom-icon {
    font-size: 1.2rem;
}

.quality-container {
    display: flex;
    align-items: center;
}

.quality-indicator {
    padding: 5px 10px;
    border-radius: 3px;
    font-weight: bold;
    margin-left: 10px;
    font-size: 0.8rem;
}

.quality-indicator.good {
    background-color: #4caf50;
    color: white;
}

.quality-indicator.okay {
    background-color: #ff9800;
    color: white;
}

.quality-indicator.bad {
    background-color: #f44336;
    color: white;
}

.quality-indicator.offline {
    background-color: #757575;
    border: 1px dashed #424242;
    font-size: 0.75rem;
}

.preview-content {
    background-color: #e6f2ff; /* Changed to match styles.css */
    padding: 15px;
    border-radius: 5px;
    margin-bottom: 15px;
    border: 1px solid #b8d4ff; /* Changed to match styles.css */
    min-height: 100px;
    white-space: pre-wrap;
    overflow: visible; /* Changed from overflow-y to overflow */
    height: auto; /* Allow content to determine height */
}

/* Similarity score and feedback details */
.similarity-container {
    margin-top: 15px;
    padding: 15px;
    background-color: #f8f9fa;
    border-radius: 8px;
    border: 1px solid #e0e0e0;
    margin-bottom: 15px;
    overflow-y: visible;
}

.similarity-score-container {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.similarity-label {
    font-weight: bold;
    margin-right: 10px;
    color: #555;
}

.similarity-score {
    font-size: 1.2rem;
    font-weight: bold;
    color: #4a86e8;
}

.feedback-details-container {
    margin-top: 10px;
}

.feedback-details-label {
    font-weight: bold;
    margin-bottom: 5px;
    color: #555;
}

.feedback-details-list {
    list-style-type: disc;
    margin-left: 20px;
}

.feedback-details-list li {
    margin-bottom: 5px;
    color: #666;
}

.feedback-details-list li.offline-notice {
    color: #d32f2f;
    font-weight: bold;
    background-color: #ffebee;
    padding: 8px;
    border-radius: 4px;
    margin-bottom: 10px;
    border-left: 3px solid #d32f2f;
}

/* Prompt Evaluation Container */
.prompt-evaluation-container {
    margin-top: 15px;
    padding: 15px;
    border-radius: 8px;
    background-color: #f0f7ff;
    border: 1px solid #d0e3ff;
    overflow-y: visible;
}

.prompt-evaluation-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.prompt-evaluation-label {
    font-weight: bold;
    color: #0056b3;
    font-size: 1.1em;
}

.prompt-evaluation-summary {
    font-size: 0.95em;
    line-height: 1.5;
    color: #333;
    margin-bottom: 15px;
    padding: 12px;
    background-color: #fff;
    border-radius: 6px;
    border-left: 3px solid #0056b3;
}

.prompt-dimensions-container {
    margin-top: 20px;
    margin-bottom: 15px;
    border-top: 1px solid #d0e3ff;
    padding-top: 15px;
}

.prompt-dimensions-label {
    font-weight: bold;
    color: #0056b3;
    margin-bottom: 12px;
}

.dimensions-grid {
    display: grid;
    grid-template-columns: repeat(auto-fill, minmax(250px, 1fr));
    gap: 12px;
    margin-bottom: 15px;
    overflow: visible;
}

.dimension-card {
    background-color: #fff;
    border-radius: 8px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
    transition: transform 0.2s, box-shadow 0.2s;
}

.dimension-card:hover {
    transform: translateY(-2px);
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.15);
}

.dimension-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
}

.dimension-name {
    font-weight: bold;
    color: #333;
    font-size: 1em;
}

.dimension-score {
    font-weight: bold;
    padding: 3px 8px;
    border-radius: 12px;
    font-size: 0.85em;
}

.dimension-score.high {
    background-color: #d4edda;
    color: #155724;
}

.dimension-score.medium {
    background-color: #fff3cd;
    color: #856404;
}

.dimension-score.low {
    background-color: #f8d7da;
    color: #721c24;
}

.dimension-feedback {
    font-size: 0.9em;
    color: #555;
    margin-bottom: 10px;
    line-height: 1.4;
}

.dimension-suggestions {
    font-size: 0.9em;
    color: #0056b3;
    font-style: italic;
    padding-left: 8px;
    border-left: 2px solid #4a86e8;
}

.prompt-improvement-container {
    background-color: #fff;
    border-radius: 8px;
    padding: 15px;
    margin-bottom: 15px;
    border: 1px solid #e0e0e0;
}

.prompt-improvement-label {
    font-weight: bold;
    color: #333;
    margin-bottom: 10px;
    font-size: 1em;
}

.prompt-improvement-list {
    margin: 0;
    padding-left: 25px;
}

.prompt-improvement-list li {
    margin-bottom: 8px;
    color: #333;
    font-size: 0.95em;
    line-height: 1.4;
    padding: 3px 0;
}

.prompt-improvement-list li::marker {
    color: #4a86e8;
    font-weight: bold;
}

/* Section scores styles */
.section-scores-container {
    margin-top: 20px;
    border-top: 1px solid #e0e0e0;
    padding-top: 15px;
}

.section-scores-header {
    margin-bottom: 10px;
}

.section-scores-label {
    font-weight: bold;
    color: #555;
    font-size: 1.05rem;
}

.section-scores-list {
    display: flex;
    flex-direction: column;
    gap: 12px;
}

.section-score-item {
    background-color: #fff;
    border: 1px solid #e0e0e0;
    border-radius: 6px;
    padding: 12px;
    box-shadow: 0 1px 3px rgba(0,0,0,0.05);
}

.section-score-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 8px;
}

.section-name {
    font-weight: bold;
    color: #333;
}

.section-score-value {
    font-weight: bold;
    padding: 2px 8px;
    border-radius: 12px;
    font-size: 0.9rem;
}

.section-score-value.high {
    background-color: #d4edda;
    color: #155724;
}

.section-score-value.medium {
    background-color: #fff3cd;
    color: #856404;
}

.section-score-value.low {
    background-color: #f8d7da;
    color: #721c24;
}

.section-feedback {
    margin-bottom: 8px;
    color: #555;
    font-size: 0.95rem;
    line-height: 1.4;
}

.section-suggestions {
    font-style: italic;
    color: #666;
    font-size: 0.9rem;
    padding-left: 12px;
    border-left: 3px solid #4a86e8;
    margin-top: 8px;
}

.preview-actions, .edit-actions {
    display: flex;
    justify-content: flex-end;
    gap: 10px;
}

/* Enhanced Primary Button */
.primary-button {
    background: var(--success-gradient);
    color: white;
    border: 1px solid var(--success-color);
}

.primary-button:hover {
    background: linear-gradient(135deg, #3a8bfe 0%, #00e2fe 100%);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(79, 172, 254, 0.4);
}

.primary-button:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

/* Enhanced Secondary Button */
.secondary-button {
    background: var(--bg-elevated);
    color: var(--text-secondary);
    border: 1px solid var(--border-secondary);
}

.secondary-button:hover {
    background: var(--bg-card);
    color: var(--text-primary);
    transform: translateY(-2px);
    box-shadow: var(--shadow-lg);
    border-color: var(--border-accent);
}

.secondary-button:active {
    transform: translateY(0);
    box-shadow: var(--shadow-md);
}

.edit-header {
    margin-bottom: 15px;
}

/* Modal */
.modal {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.modal-content {
    background-color: white;
    padding: 30px;
    border-radius: 10px;
    max-width: 500px;
    width: 90%;
    text-align: center;
}

.modal-content h2 {
    margin-bottom: 15px;
    color: #4caf50;
}

.modal-content p {
    margin-bottom: 20px;
}

.final-score-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #f9f9f9;
    border-radius: 5px;
}

.final-score-label {
    font-weight: bold;
    margin-bottom: 10px;
}

.final-score {
    font-size: 2rem;
    font-weight: bold;
    color: #4caf50;
}

.modal-buttons {
    display: flex;
    justify-content: center;
    gap: 15px;
}

/* Task Failure Modal Styles */
.failure-feedback-container {
    margin: 20px 0;
    padding: 15px;
    background-color: #fff8f8;
    border-radius: 5px;
    border-left: 4px solid #dc3545;
}

.failure-feedback-label {
    font-weight: bold;
    margin-bottom: 10px;
    color: #721c24;
}

.failure-feedback {
    font-size: 1rem;
    line-height: 1.5;
    color: #333;
}

/* Zoom Preview Modal */
.zoom-modal-content {
    max-width: 90%;
    width: 90%;
    max-height: 90vh;
    display: flex;
    flex-direction: column;
    text-align: left;
    padding: 0;
    overflow: hidden;
}

.zoom-modal-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 15px 20px;
    background-color: #f8f9fa;
    border-bottom: 1px solid #e0e0e0;
}

.zoom-modal-header h2 {
    margin: 0;
    color: #333;
}

.close-button {
    background: none;
    border: none;
    font-size: 1.5rem;
    cursor: pointer;
    color: #666;
    transition: color 0.2s;
}

.close-button:hover {
    color: #333;
}

.zoomed-preview-content {
    padding: 20px;
    overflow-y: auto;
    flex: 1;
    background-color: #fff;
    white-space: pre-wrap;
    -webkit-overflow-scrolling: touch; /* Smooth scrolling on iOS */
}

/* Loading overlay */
.loading-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.8);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    z-index: 1000;
}

.spinner {
    width: 50px;
    height: 50px;
    border: 5px solid #f3f3f3;
    border-top: 5px solid #3498db;
    border-radius: 50%;
    animation: spin 1s linear infinite;
    margin-bottom: 20px;
}

.loading-text {
    font-weight: bold;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}

/* Responsive design for tablet and mobile */
@media (max-width: 992px) {
    .app-container {
        flex-direction: column;
        min-height: 100vh;
        min-height: calc(var(--vh, 1vh) * 100);
        overflow-y: auto;
        overflow-x: hidden;
    }

    /* Show left sidebar on tablet and mobile */
    .sidebar {
        width: 100%;
        max-height: 250px;
        overflow-y: auto;
        padding: 10px;
        transition: max-height 0.3s ease, opacity 0.3s ease;
        border-right: none;
        border-bottom: 1px solid #e0e0e0;
        flex-shrink: 0;
    }

    /* Mobile-specific sidebar adjustments */
    .sidebar-hidden .sidebar {
        max-height: 0;
        padding: 0;
        overflow: hidden;
        border-bottom: none;
    }

    /* Keep left sidebar toggle visible */
    .mobile-sidebar-toggle {
        display: flex;
        align-items: center;
        justify-content: center;
        width: 44px;
        height: 44px;
        background: var(--primary-color);
        border-radius: 8px;
        cursor: pointer;
        transition: all 0.3s ease;
    }

    .mobile-sidebar-toggle:hover {
        background: var(--primary-dark);
        transform: scale(1.05);
    }

    .mobile-sidebar-toggle span {
        display: block;
        width: 20px;
        height: 2px;
        background: white;
        margin: 3px 0;
        transition: 0.3s;
        border-radius: 1px;
    }

    /* Right sidebar toggle now visible on mobile for independent operation */
    .right-sidebar-toggle {
        display: flex;
    }

    /* Keep header layout normal */
    .header {
        justify-content: space-between;
    }

    .character-info {
        padding: 8px;
    }

    .character-avatar {
        width: 40px;
        height: 40px;
        font-size: 1.2rem;
    }

    .character-details {
        padding-left: 8px;
    }

    .character-name {
        font-size: 0.9rem;
    }

    .character-title {
        font-size: 0.8rem;
    }

    .progress-container {
        margin: 8px 0;
    }

    .org-chart-container {
        margin-bottom: 10px;
        padding-bottom: 10px;
    }

    .org-chart {
        gap: 5px;
    }

    .org-node {
        padding: 4px 8px;
        font-size: 0.7rem;
    }

    .instructions {
        display: none; /* Hide instructions on mobile to save space */
    }

    .main-content {
        flex: 1;
        overflow-x: hidden;
        overflow-y: auto;
        max-height: none;
        height: calc(100vh - 250px); /* Account for sidebar height */
        min-height: 0;
        display: flex;
        flex-direction: column;
    }

    .header {
        padding: 10px;
        flex-shrink: 0;
        background: var(--bg-secondary);
        border-bottom: 1px solid var(--border-primary);
    }

    .messages-container {
        padding: 10px;
        flex: 1;
        overflow-y: auto;
        min-height: 0;
    }

    .message {
        max-width: 90%;
        margin-bottom: 15px;
    }

    .message-content {
        padding: 8px 12px;
        margin: 0 5px;
    }

    .message-sender {
        font-size: 0.8rem;
    }

    .message-text {
        font-size: 0.9rem;
        line-height: 1.4;
    }

    .input-area {
        padding: 10px;
        flex-shrink: 0;
        background: var(--bg-secondary);
        border-top: 1px solid var(--border-primary);
    }

    .prompt-input, .response-editor {
        height: 80px;
        padding: 8px;
        font-size: 16px; /* Prevent zoom on iOS */
        border-radius: 8px;
        border: 1px solid var(--border-primary);
    }

    .preview-button, .primary-button, .secondary-button {
        padding: 12px 20px;
        font-size: 0.9rem;
        min-height: 44px; /* Touch target size */
        border-radius: 8px;
    }

    .preview-actions, .edit-actions {
        flex-wrap: wrap;
        gap: 8px;
    }

    .preview-content {
        max-height: 200px;
        padding: 10px;
        border-radius: 8px;
        background: var(--bg-tertiary);
    }

    /* Modal adjustments for mobile */
    .modal-content {
        padding: 20px;
        width: 95%;
    }

    .modal-buttons {
        flex-direction: column;
        gap: 10px;
    }

    .modal-buttons button {
        width: 100%;
    }
}

/* Small mobile devices */
@media (max-width: 480px) {
    .sidebar {
        max-height: 180px;
        padding: 8px;
    }

    .character-header {
        font-size: 0.8rem;
        letter-spacing: 0.03em;
    }

    .character-name {
        font-size: 1rem;
    }

    .character-title {
        font-size: 0.8rem;
    }

    .main-content {
        height: calc(100vh - 180px); /* Adjust for smaller sidebar */
    }

    .header {
        padding: 8px;
    }

    .messages-container {
        padding: 8px;
    }

    .input-area {
        padding: 8px;
    }

    .prompt-input, .response-editor {
        height: 70px;
        font-size: 16px;
    }

    .preview-button, .primary-button, .secondary-button {
        padding: 10px 16px;
        font-size: 0.85rem;
    }
}

    .org-chart-container {
        margin-bottom: 5px;
    }

    .context-info {
        padding: 8px;
        font-size: 0.8em;
    }

    .context-label {
        width: 90px;
    }

    .message {
        max-width: 95%;
    }

    .message-content.ai-response::before {
        width: 16px;
        height: 16px;
        font-size: 10px;
        top: -5px;
        left: -5px;
    }

    .preview-header {
        flex-direction: column;
        align-items: flex-start;
    }

    .quality-container {
        margin-top: 5px;
    }

    /* Make buttons more touch-friendly */
    .preview-button, .primary-button, .secondary-button {
        min-height: 44px; /* Apple's recommended minimum touch target size */
        font-size: 1rem;
    }

    /* Increase spacing between buttons for easier touch */
    .preview-actions, .edit-actions {
        gap: 12px;
    }

    /* Make sure text inputs are large enough to tap and type */
    .prompt-input, .response-editor {
        font-size: 16px; /* Prevents iOS zoom on focus */
        min-height: 60px;
    }

    /* Keep buttons side-by-side on mobile but adjust sizing */
    .button-row {
        flex-direction: row;
        gap: 8px;
    }

    #restart-game-button-main.btn {
        min-width: 120px;
        flex: 0 0 auto;
        font-size: 0.85rem;
        padding: 8px 12px;
    }

    #preview-button.btn {
        flex: 1;
        font-size: 0.85rem;
        padding: 8px 12px;
    }
}

/* Promotion message styles */
.promotion-message {
    background-color: #e8f4fd;
    padding: 15px;
    border-radius: 8px;
    border-left: 4px solid #4a86e8;
    margin: 10px 0;
    animation: promotion-pulse 2s infinite;
}

@keyframes promotion-pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(74, 134, 232, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(74, 134, 232, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(74, 134, 232, 0);
    }
}

.promotion-message h3 {
    color: #4a86e8;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.4em;
    text-transform: uppercase;
}

.promotion-message p {
    margin: 0;
}

/* Message with is-promotion class */
.message.is-promotion {
    background-color: #e3f2fd;
    border: 2px solid #4a86e8;
    box-shadow: 0 0 15px rgba(74, 134, 232, 0.5);
    animation: promotion-pulse 2s infinite;
    max-width: 90%;
    margin-left: auto;
    margin-right: auto;
}

.message.is-promotion .message-content {
    background-color: #e3f2fd;
    border-left: 4px solid #4a86e8;
}

.message.is-promotion .message-sender {
    color: #1a237e;
    font-size: 1.1rem;
}

.message.is-promotion .message-text {
    font-size: 1.1rem;
    font-weight: 500;
    color: #1a237e;
}

.message.is-promotion .message-text h2 {
    color: #1a237e;
    font-size: 1.3rem;
    margin-top: 0;
    margin-bottom: 10px;
    text-transform: uppercase;
}

.promotion-note {
    font-style: italic;
    color: #555;
    background-color: #e3f2fd;
    padding: 10px;
    border-radius: 5px;
    margin-top: 15px;
    border-left: 3px solid #2196f3;
    font-size: 0.9rem;
}

/* Improvement section styles for promotion messages */
.improvement-section {
    margin-top: 20px;
    padding: 15px;
    background-color: #f5f5f5;
    border-radius: 8px;
    border-left: 4px solid #4a86e8;
}

.improvement-section h3 {
    color: #333;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.2em;
}

.tips-section {
    margin-top: 10px;
    padding: 10px;
    background-color: #e8f4fd;
    border-radius: 5px;
}

.tips-section h4 {
    color: #4a86e8;
    margin-top: 0;
    margin-bottom: 10px;
    font-size: 1.1em;
}

.tips-section ul {
    margin: 0;
    padding-left: 20px;
}

.tips-section li {
    margin-bottom: 5px;
    color: #333;
}

/* Dark mode styles for improvement section */
.dark-mode .improvement-section {
    background-color: #2d2d2d;
    border-left: 4px solid #64b5f6;
}

.dark-mode .improvement-section h3 {
    color: #e0e0e0;
}

.dark-mode .tips-section {
    background-color: #1e3a5f;
}

.dark-mode .tips-section h4 {
    color: #90caf9;
}

.dark-mode .tips-section li {
    color: #e0e0e0;
}

/* Fix for iOS devices to prevent zoom on input focus */
@supports (-webkit-touch-callout: none) {
    input, textarea, select {
        font-size: 16px;
    }
}

/* Fix for zoom-related viewport issues */
@media screen and (min-resolution: 1.25dppx) {
    .app-container {
        height: 100vh !important;
        min-height: 100vh !important;
    }

    .sidebar {
        height: 100vh !important;
        min-height: 100vh !important;
    }
}

/* Additional zoom fix for high DPI displays */
@media screen and (min-resolution: 2dppx) {
    .app-container,
    .sidebar {
        height: 100vh !important;
        min-height: 100vh !important;
        max-height: 100vh !important;
    }
}

/* Ensure sidebar children fill available space */
.sidebar > * {
    flex-shrink: 0;
}

.sidebar .character-info,
.sidebar .game-stats,
.sidebar .context-info,
.sidebar .instructions {
    flex-shrink: 0;
}

/* Force sidebar to maintain height on all zoom levels */
@media screen {
    .sidebar {
        height: 100vh !important;
        min-height: 100vh !important;
    }

    .app-container {
        height: 100vh !important;
        min-height: 100vh !important;
    }
}

/* === Left Sidebar Styles === */

/* 1. Left Sidebar Container */
.sidebar {
    background-color: var(--panel-bg-color);
    padding: 20px;
    color: var(--text-color-light);
    /* Ensure sidebar scrolls independently if content overflows */
    overflow-y: auto;
    height: 100vh; /* Or max-height if preferred */
}

/* 2. Main Title */
.sidebar .logo h1 {
    font-size: 2.2em; /* Adjusted from 2.5em for potentially long title */
    font-weight: bold;
    color: var(--text-color-bright);
    margin-bottom: 25px; /* Adjusted */
}

/* 3. Section Titles & Labels */
.sidebar .character-header,
.sidebar .game-stats .stat-label,
.sidebar .context-info .context-label,
.sidebar .instructions h3 {
    font-size: 0.9em;
    text-transform: uppercase;
    color: var(--text-color-light);
    letter-spacing: 1px;
    margin-top: 20px;
    margin-bottom: 10px;
    font-weight: normal; /* Ensuring they are not bold by default unless specified */
    word-wrap: break-word;
    overflow-wrap: break-word;
    white-space: normal;
    line-height: 1.3;
}

/* Specific margin for the first section header if needed */
.sidebar .character-header {
    margin-top: 0; /* Assuming it's the first prominent header */
}
.sidebar .instructions h3 {
    margin-top: 30px; /* More space before "How to Play" */
}

/* 4. Sidebar List Items & Active Item Styling */
/* General style for items that might appear in lists */
.sidebar-list .list-item, /* A generic class for future lists */
.sidebar .instructions ol li {
    padding: 8px 10px;
    margin-bottom: 5px;
    border-radius: 4px;
    color: var(--text-color-light); /* Default color for list items */
    transition: background-color 0.2s ease, color 0.2s ease;
}

/* Active/Highlighted item styling */
/* This is a general class, can be applied via JS */
.sidebar-list .list-item.active-item,
/* For demonstration, styling #current-role as if it's an active item,
   though it's not in a list in the current HTML.
   The image shows "Applicant" highlighted. */
.sidebar .game-stats .stat #current-role.active-item-demonstration {
    background-color: rgba(var(--accent-color-blue-rgb), 0.15); /* Needs --accent-color-blue-rgb defined */
    /* Fallback if --accent-color-blue-rgb is not defined or for browsers not supporting rgba with css vars well */
    /* background-color: #0d6efd26; */ /* Example: Bootstrap blue at 15% opacity */
    color: var(--text-color-bright);
    border-left: 3px solid var(--accent-color-blue);
    font-weight: bold;
    padding-left: 7px; /* Adjust padding to account for border */
}
/* If #current-role is to be styled like an active item permanently or based on some state: */
/* You might need a more specific selector or a class added by JS */


/* 5. Performance Details Section (within .game-stats) */
.sidebar .game-stats .stat {
    display: flex;
    justify-content: space-between;
    align-items: center; /* Align items vertically */
    margin-bottom: 8px; /* Space between each stat line */
}

.sidebar .game-stats .stat-label {
    margin-top: 0; /* Reset margin for labels within .stat flex container */
    margin-bottom: 0;
    margin-right: 10px; /* Space between label and value/bar */
    flex-shrink: 0; /* Prevent label from shrinking */
}

.sidebar .game-stats .stat-value {
    color: var(--text-color-bright);
    font-weight: bold;
    text-align: right;
}

.sidebar .game-stats .progress-container {
    background-color: #444; /* Darker gray track */
    /* Or use var(--border-color-subtle) if defined and appropriate */
    height: 8px;
    border-radius: 4px;
    margin-bottom: 15px;
    flex-grow: 1; /* Allow progress container to take available space if stat-label is short */
    margin-top: 5px; /* Align with stat value if they are on separate lines in design */
}
/* If stat label and progress bar are on the same line, adjust .stat */
.sidebar .game-stats .stat.has-progress-bar {
    flex-wrap: wrap; /* Allow wrapping if needed */
}
.sidebar .game-stats .stat.has-progress-bar .stat-label {
    width: 100%; /* Make label take full width */
    margin-bottom: 5px; /* Space between label and bar */
}


.sidebar .game-stats #progress-bar { /* This is the fill */
    background-color: var(--accent-color-blue);
    height: 100%;
    border-radius: 4px;
    width: 0%; /* Width will be set by JS */
    transition: width 0.5s ease-in-out;
}
/* Styling for the stat that contains the progress bar specifically */
.sidebar .game-stats .stat:has(.progress-container) .stat-label {
    /* Styles for labels like "Role Progress" that are above a bar */
    /* display: block; */ /* Already block due to flex context or default */
    /* width: 100%; */
}
.sidebar .game-stats .stat:has(.progress-container) .stat-value {
    /* For values like "0/0" associated with progress */
    /* position: absolute; right: 0; top: -20px; */ /* Example if value is above bar */
    /* This needs to be coordinated with HTML structure if value is outside the bar */
}


/* 6. Notes Section (Character Info) */
.sidebar .character-info {
    text-align: center; /* Center avatar and text as per typical sidebar notes */
    margin-bottom: 20px;
}

.sidebar .character-info #character-avatar {
    border-radius: 50%;
    width: 60px; /* Slightly larger than 50px for better presence */
    height: 60px;
    margin: 0 auto 10px auto; /* Center block element */
    background-color: var(--border-color-subtle); /* Placeholder bg if no image */
    color: var(--text-color-bright); /* For emoji avatar */
    display: flex;
    align-items: center;
    justify-content: center;
    font-size: 30px; /* For emoji avatar */
    border: 2px solid var(--accent-color-blue); /* Adding a border as seen in some designs */
}
/* If #character-avatar contains an <img> tag:
.sidebar .character-info #character-avatar img {
    border-radius: 50%;
    width: 100%;
    height: 100%;
    object-fit: cover;
}
*/

.sidebar .character-info #character-name {
    color: var(--text-color-bright);
    font-weight: bold;
    font-size: 1.1em;
}

.sidebar .character-info #character-title {
    color: var(--text-color-light);
    font-size: 0.9em;
    margin-top: 4px;
}

/* 7. How to Play Section */
.sidebar .instructions ol {
    padding-left: 20px; /* Standard padding for ordered lists */
    list-style-type: none; /* Remove default numbers if using custom bullets */
    /* list-style-type: disclosure-closed; */ /* For triangle bullets, but might need ::marker styling */
    margin-top: 5px;
}

.sidebar .instructions ol li {
    color: var(--text-color-light);
    margin-bottom: 8px; /* Space between items */
    position: relative;
    padding-left: 15px; /* Space for custom bullet */
}

/* Custom triangle bullet for "How to Play" items */
.sidebar .instructions ol li::before {
    content: ""; /* Using a border-created triangle */
    display: inline-block;
    width: 0;
    height: 0;
    border-top: 5px solid transparent;
    border-bottom: 5px solid transparent;
    border-left: 6px solid var(--text-color-light); /* Triangle color */
    position: absolute;
    left: 0;
    top: 50%;
    transform: translateY(-50%);
    margin-right: 8px;
}

/* Adjustments for overall sidebar spacing and flow */
.sidebar > div:not(:last-child) {
    margin-bottom: 15px; /* General spacing between direct children divs in sidebar */
}
.sidebar .logo { margin-bottom: 25px; }
.sidebar .character-info { margin-bottom: 25px; }
.sidebar .game-stats { margin-bottom: 25px; }
.sidebar .instructions { margin-bottom: 10px; } /* Less margin at the very end */

/* Ensure --accent-color-blue-rgb is defined in :root or .dark-mode
   If not, the rgba() will not work as expected.
   Example definition (if blue is #0D6EFD):
   :root { --accent-color-blue-rgb: 13, 110, 253; }
   .dark-mode { --accent-color-blue-rgb: 13, 110, 253; } // Or your dark mode equivalent
*/

/* === Chat Interface Styles === */

/* Overall Center Content Area Background */
.main-content {
    background-color: var(--primary-bg-color); /* Darkest gray */
}

/* Chat Messages Area */
#messages-container {
    padding: 20px;
    overflow-y: auto;
    flex-grow: 1; /* Allow it to take available space */
}

/* Individual Message Containers */
.message,
.challenge-description, /* Assuming this might be used for initial challenge messages */
.manager-message { /* Assuming this is for manager feedback messages */
    background-color: var(--panel-bg-color); /* Lighter dark gray */
    padding: 15px 20px;
    border-radius: 8px;
    margin-bottom: 15px;
    color: var(--text-color-light);
    box-shadow: 0 2px 4px rgba(0,0,0,0.1); /* Subtle shadow for depth */
}

.message h2, .message h3, .message strong.message-heading,
.challenge-description h2, .challenge-description h3, .challenge-description strong.message-heading,
.manager-message h2, .manager-message h3, .manager-message strong.message-heading {
    color: var(--text-color-bright);
    margin-top: 10px;
    margin-bottom: 5px;
}

/* Message Header (Avatar, Name, Timestamp) */
.message-info {
    display: flex;
    align-items: center;
    margin-bottom: 10px;
}

.message-avatar img {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    margin-right: 10px;
    border: 1px solid var(--border-color-subtle);
}

.message-sender {
    color: var(--text-color-bright);
    font-weight: bold;
    margin-right: 10px;
    font-size: 0.95em;
}

.message-timestamp {
    color: #888; /* Specific dimmer gray as requested */
    font-size: 0.85em;
}

.message-body {
    /* Styles for the main text content of the message if needed */
    line-height: 1.5;
}


/* Prompt Input Area */
#prompt-input.prompt-input { /* Increased specificity to override existing if any */
    background-color: var(--panel-bg-color);
    color: var(--text-color-light);
    border: 1px solid var(--border-color-subtle);
    border-radius: 6px;
    padding: 8px 12px;
    width: 100%; /* Already has class .prompt-input which might do this */
    min-height: 50px;
    margin-bottom: 8px;
    resize: vertical;
    font-size: 0.9rem; /* Slightly smaller for compactness */
}

#prompt-input.prompt-input::placeholder {
    color: var(--text-color-light);
    opacity: 0.7; /* Standard way to make placeholder lighter */
}

/* "Generate Response" Button (Styling #preview-button) */
#preview-button.preview-button { /* Increased specificity */
    background-color: var(--accent-color-blue);
    color: var(--text-color-bright);
    border: none;
    border-radius: 6px;
    padding: 8px 16px;
    font-weight: bold;
    cursor: pointer;
    width: 100%; /* Make it full width as per image suggestion */
    display: block; /* To take full width */
    text-align: center;
    font-size: 0.9rem; /* Slightly smaller for compactness */
    transition: background-color 0.2s ease;
}

#preview-button.preview-button:hover {
    background-color: var(--accent-color-blue-hover); /* Assuming a hover variable exists or will be added */
}

/* Ensure input-area itself allows for full-width button */
.input-area {
    /* padding: 15px 20px; */ /* This was in original, might conflict with prompt-container padding */
    /* background-color: var(--primary-bg-color); */ /* Already set on main-content */
}

/* Container for prompt textarea and button, if needed for layout */
#prompt-container.prompt-container {
    display: flex;
    flex-direction: column;
    gap: 8px; /* Reduced space between textarea and button */
    padding: 12px 16px; /* More compact padding */
    background-color: var(--primary-bg-color); /* Match main content bg */
}

/* Button row layout for restart and preview buttons */
.button-row {
    display: flex;
    gap: 12px;
    align-items: center;
    margin-top: 12px;
}

/* Restart button styling - Red theme */
#restart-game-button-main.btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    flex: 0 0 auto;
    min-width: 140px;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 10px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
    position: relative;
    overflow: hidden;
}

#restart-game-button-main.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#restart-game-button-main.btn:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(231, 76, 60, 0.4);
}

#restart-game-button-main.btn:hover::before {
    left: 100%;
}

#restart-game-button-main.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* Preview button styling - Enhanced blue theme */
#preview-button.btn {
    background: linear-gradient(135deg, #3498db 0%, #2980b9 100%);
    color: white;
    border: none;
    flex: 1;
    font-size: 0.9rem;
    font-weight: 600;
    padding: 10px 16px;
    border-radius: 8px;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
    position: relative;
    overflow: hidden;
}

#preview-button.btn::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
    transition: left 0.5s;
}

#preview-button.btn:hover {
    background: linear-gradient(135deg, #2980b9 0%, #1f618d 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(52, 152, 219, 0.4);
}

#preview-button.btn:hover::before {
    left: 100%;
}

#preview-button.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(52, 152, 219, 0.3);
}

/* Sidebar restart button styling - Red theme */
#restart-game-button-sidebar.btn {
    background: linear-gradient(135deg, #e74c3c 0%, #c0392b 100%);
    color: white;
    border: none;
    font-weight: 600;
    transition: all 0.3s ease;
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

#restart-game-button-sidebar.btn:hover {
    background: linear-gradient(135deg, #c0392b 0%, #a93226 100%);
    transform: translateY(-2px);
    box-shadow: 0 4px 16px rgba(231, 76, 60, 0.4);
}

#restart-game-button-sidebar.btn:active {
    transform: translateY(0);
    box-shadow: 0 2px 8px rgba(231, 76, 60, 0.3);
}

/* Styles for .message-text content from the image (already partially in HTML style block) */
.message .message-text h1, .message .message-text h2, .message .message-text h3 {
    color: var(--text-color-bright); /* Ensure headings inside messages are bright */
}
.message .message-text {
    color: var(--text-color-light); /* Ensure general text is light */
}

/* Ensure the input area and messages container are within main-content's flow */
.main-content {
    display: flex;
    flex-direction: column;
    height: 100vh; /* Or calc(var(--vh, 1vh) * 100) */
}

/* Make sure the input area doesn't shrink */
.input-area {
    flex-shrink: 0;
}

/* Company Status Warning Animations */
@keyframes slideInRight {
    from {
        transform: translateX(100%);
        opacity: 0;
    }
    to {
        transform: translateX(0);
        opacity: 1;
    }
}

@keyframes slideOutRight {
    from {
        transform: translateX(0);
        opacity: 1;
    }
    to {
        transform: translateX(100%);
        opacity: 0;
    }
}

/* Company Status Warning Styles */
.company-status-warning {
    font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.4;
}

.company-status-warning .btn-close:hover {
    background-color: rgba(133, 100, 4, 0.1);
    border-radius: 50%;
}
