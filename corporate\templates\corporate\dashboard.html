{% extends 'corporate/base.html' %}

{% block title %}Dashboard - Corporate Prompt Master{% endblock %}

{% block content %}
<!-- Enhanced Dashboard Hero Section -->
<section class="dashboard-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <div class="mb-3">
                                    <span class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-crown me-2"></i>{{ company.name }} Member
                                    </span>
                                </div>
                                <h1 class="display-5 fw-bold mb-3 text-shadow">
                                    Welcome back,
                                    <span class="text-gradient">{{ user.get_full_name|default:user.username }}</span>!
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Ready to continue your prompt engineering mastery journey? Let's achieve new milestones together.
                                </p>
                                <div class="quick-actions d-flex flex-wrap gap-3">
                                    <a href="{% url 'corporate:game_list' %}" class="btn btn-light btn-lg px-4 py-3 fw-semibold">
                                        <i class="fas fa-gamepad me-2"></i>Continue Learning
                                    </a>
                                    <a href="{% url 'corporate:leaderboard' %}" class="btn btn-outline-light btn-lg px-4 py-3 fw-semibold">
                                        <i class="fas fa-trophy me-2"></i>View Rankings
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="dashboard-stats text-center">
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ user_progress.level|default:"1" }}</div>
                                            <small class="text-white-75">Current Level</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ user_progress.score|default:"0" }}</div>
                                            <small class="text-white-75">Total Score</small>
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-3 text-white-75">
                                    <i class="fas fa-calendar-alt me-2"></i>{{ "now"|date:"l, F j, Y" }}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Main Dashboard Content -->
<div class="container-fluid">
    <div class="row g-4 mb-5">
        <!-- Company Overview Card -->
        <div class="col-lg-8">
            <div class="card border-0 shadow-lg h-100 hover-lift">
                <div class="card-header bg-gradient-primary text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-building fa-lg text-white"></i>
                        </div>
                        <div>
                            <h4 class="mb-1 fw-bold">{{ company.name }} Overview</h4>
                            <p class="mb-0 text-white-75">Company Performance & Management Hub</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    {% if is_admin and pending_users_count > 0 %}
                    <div class="alert alert-warning border-0 rounded-3 mb-4 shadow-sm">
                        <div class="d-flex align-items-center">
                            <div class="flex-shrink-0">
                                <div class="bg-warning bg-opacity-25 rounded-circle p-3">
                                    <i class="fas fa-exclamation-triangle fa-lg text-warning"></i>
                                </div>
                            </div>
                            <div class="flex-grow-1 ms-3">
                                <h6 class="alert-heading fw-bold mb-1">Action Required</h6>
                                <p class="mb-2">{{ pending_users_count }} team member{{ pending_users_count|pluralize }} awaiting approval to join your workspace.</p>
                                <a href="{% url 'corporate:company_users' %}" class="btn btn-warning btn-sm fw-semibold">
                                    <i class="fas fa-users me-1"></i>Review Applications
                                </a>
                            </div>
                        </div>
                    </div>
                    {% endif %}

                    <div class="row g-4">
                        <!-- Company Profile Section -->
                        <div class="col-md-4">
                            <div class="company-profile text-center">
                                <div class="company-avatar mb-3">
                                    {% if company.logo %}
                                    <div class="company-logo-container position-relative d-inline-block">
                                        <img src="{{ company.logo.url }}" alt="{{ company.name }} Logo"
                                             class="company-logo rounded-3 shadow-sm">
                                        <div class="company-status position-absolute bottom-0 end-0 bg-success rounded-circle border border-white"></div>
                                    </div>
                                    {% else %}
                                    <div class="company-logo-placeholder bg-gradient-primary rounded-3 p-4 d-inline-block shadow-sm">
                                        <i class="fas fa-building fa-2x text-white"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <h5 class="fw-bold text-primary mb-2">{{ company.name }}</h5>
                                {% if corporate_profile.job_title %}
                                <div class="user-role bg-light rounded-pill px-3 py-2 d-inline-block">
                                    <i class="fas fa-user-tie me-2 text-primary"></i>
                                    <span class="fw-semibold">{{ corporate_profile.job_title }}</span>
                                </div>
                                {% endif %}
                                {% if is_admin %}
                                <div class="mt-2">
                                    <span class="badge bg-primary px-3 py-2">
                                        <i class="fas fa-crown me-1"></i>Administrator
                                    </span>
                                </div>
                                {% endif %}
                            </div>
                        </div>

                        <!-- Company Stats Section -->
                        <div class="col-md-8">
                            <div class="company-info-section">
                                <div class="d-flex justify-content-between align-items-center mb-3">
                                    <h6 class="fw-bold text-dark mb-0">
                                        <i class="fas fa-info-circle me-2 text-primary"></i>Company Details
                                    </h6>
                                    {% if user == company.owner %}
                                    <a href="{% url 'corporate:edit_company' %}" class="btn btn-sm btn-primary">
                                        <i class="fas fa-edit me-1"></i>Manage
                                    </a>
                                    {% endif %}
                                </div>

                                <div class="company-description bg-light rounded-3 p-3 mb-3">
                                    <p class="text-muted mb-0">{{ company.description|default:"Transform your team's AI capabilities with our comprehensive prompt engineering training platform." }}</p>
                                </div>

                                {% if company.pending_approval %}
                                <div class="alert alert-warning border-0 rounded-3 shadow-sm">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-warning bg-opacity-25 rounded-circle p-2 me-3">
                                            <i class="fas fa-clock text-warning"></i>
                                        </div>
                                        <div>
                                            <h6 class="fw-bold mb-1">Approval Pending</h6>
                                            <p class="mb-0 small">Your company registration is under review. Full access will be granted upon approval.</p>
                                        </div>
                                    </div>
                                </div>
                                {% elif not company.is_active %}
                                <div class="alert alert-danger border-0 rounded-3 shadow-sm">
                                    <div class="d-flex align-items-center">
                                        <div class="bg-danger bg-opacity-25 rounded-circle p-2 me-3">
                                            <i class="fas fa-ban text-danger"></i>
                                        </div>
                                        <div>
                                            <h6 class="fw-bold mb-1">Account Inactive</h6>
                                            <p class="mb-0 small">Please contact support to reactivate your company account.</p>
                                        </div>
                                    </div>
                                </div>
                                {% endif %}

                                <!-- Enhanced Company Stats -->
                                <div class="company-stats">
                                    <h6 class="fw-bold text-dark mb-3">
                                        <i class="fas fa-chart-bar me-2 text-success"></i>Quick Stats
                                    </h6>
                                    <div class="row g-3">
                                        <div class="col-6">
                                            <div class="stat-card bg-gradient-primary text-white rounded-3 p-3 text-center shadow-sm">
                                                <div class="h4 fw-bold mb-1">{{ available_games.count }}</div>
                                                <div class="small">
                                                    <i class="fas fa-gamepad me-1"></i>Training Modules
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="stat-card bg-gradient-success text-white rounded-3 p-3 text-center shadow-sm">
                                                <div class="h4 fw-bold mb-1">{{ user_certificates.count }}</div>
                                                <div class="small">
                                                    <i class="fas fa-certificate me-1"></i>Achievements
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-lg-4">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-gradient-success text-white border-0">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-bolt me-2"></i>Quick Actions
                    </h5>
                </div>
                <div class="card-body p-0">
                    <div class="list-group list-group-flush">
                        {% if request.user.is_superuser %}
                        <a href="{% url 'superadmin:dashboard' %}" class="list-group-item list-group-item-action border-0 bg-warning bg-opacity-10">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning bg-opacity-25 rounded-circle p-2 me-3">
                                    <i class="fas fa-shield-alt text-warning"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">Return to Admin</h6>
                                    <p class="mb-0 small text-muted">Return to the superadmin dashboard</p>
                                </div>
                            </div>
                        </a>
                        {% elif request.impersonator %}
                        <a href="{% url 'impersonate-stop' %}" class="list-group-item list-group-item-action border-0 bg-danger bg-opacity-10">
                            <div class="d-flex align-items-center">
                                <div class="bg-danger bg-opacity-25 rounded-circle p-2 me-3">
                                    <i class="fas fa-sign-out-alt text-danger"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">Stop Impersonation</h6>
                                    <p class="mb-0 small text-muted">End impersonation and return to superadmin dashboard</p>
                                </div>
                            </div>
                        </a>
                        <a href="{% url 'superadmin:dashboard' %}" target="_blank" class="list-group-item list-group-item-action border-0">
                            <div class="d-flex align-items-center">
                                <div class="bg-secondary bg-opacity-25 rounded-circle p-2 me-3">
                                    <i class="fas fa-external-link-alt text-secondary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">Open Superadmin Dashboard</h6>
                                    <p class="mb-0 small text-muted">Open the superadmin dashboard in a new tab</p>
                                </div>
                            </div>
                        </a>
                        {% endif %}
                        <a href="{% url 'corporate:game_list' %}" class="list-group-item list-group-item-action border-0">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary bg-opacity-25 rounded-circle p-2 me-3">
                                    <i class="fas fa-gamepad text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">Play Games</h6>
                                    <p class="mb-0 small text-muted">Start or continue your prompt engineering training</p>
                                </div>
                            </div>
                        </a>
                        <a href="{% url 'corporate:view_certificates' %}" class="list-group-item list-group-item-action border-0">
                            <div class="d-flex align-items-center">
                                <div class="bg-success bg-opacity-25 rounded-circle p-2 me-3">
                                    <i class="fas fa-certificate text-success"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">View Certificates</h6>
                                    <p class="mb-0 small text-muted">Access your earned certificates</p>
                                </div>
                            </div>
                        </a>
                        <a href="{% url 'corporate:leaderboard' %}" class="list-group-item list-group-item-action border-0">
                            <div class="d-flex align-items-center">
                                <div class="bg-warning bg-opacity-25 rounded-circle p-2 me-3">
                                    <i class="fas fa-trophy text-warning"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">Leaderboard</h6>
                                    <p class="mb-0 small text-muted">See how you rank against others</p>
                                </div>
                            </div>
                        </a>
                        {% if is_admin %}
                        <a href="{% url 'corporate:company_users' %}" class="list-group-item list-group-item-action border-0 {% if pending_users_count > 0 %}bg-warning bg-opacity-10{% endif %}">
                            <div class="d-flex align-items-center">
                                <div class="bg-info bg-opacity-25 rounded-circle p-2 me-3">
                                    <i class="fas fa-users-cog text-info"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <div class="d-flex align-items-center">
                                        <h6 class="mb-1 fw-semibold me-2">Manage Users</h6>
                                        {% if pending_users_count > 0 %}
                                        <span class="badge bg-danger rounded-pill">{{ pending_users_count }}</span>
                                        {% endif %}
                                    </div>
                                    <p class="mb-0 small text-muted">
                                        Invite and manage company users
                                        {% if pending_users_count > 0 %}
                                        <span class="text-danger fw-semibold">({{ pending_users_count }} pending)</span>
                                        {% endif %}
                                    </p>
                                </div>
                            </div>
                        </a>
                        {% endif %}
                        {% if user == company.owner %}
                        <a href="{% url 'corporate:edit_company' %}" class="list-group-item list-group-item-action border-0">
                            <div class="d-flex align-items-center">
                                <div class="bg-secondary bg-opacity-25 rounded-circle p-2 me-3">
                                    <i class="fas fa-edit text-secondary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">Edit Company</h6>
                                    <p class="mb-0 small text-muted">Update company details and settings</p>
                                </div>
                            </div>
                        </a>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

    <!-- Games and Progress Section -->
    <div class="row g-4 mb-4">
        <div class="col-lg-6">
            <div class="card shadow-sm border-0 h-100">
                <div class="card-header bg-gradient-primary text-white border-0">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-gamepad fa-lg me-3"></i>
                        <div>
                            <h5 class="mb-0 fw-bold">Available Games</h5>
                            <small class="text-white-75">Start your training journey</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if available_games %}
                    <div class="list-group list-group-flush">
                        {% for game in available_games %}
                        <a href="{% url 'game:index' %}?company={{ company.slug }}" class="list-group-item list-group-item-action border-0 py-3">
                            <div class="d-flex align-items-center">
                                <div class="bg-primary bg-opacity-25 rounded-circle p-3 me-3">
                                    <i class="fas fa-play text-primary"></i>
                                </div>
                                <div class="flex-grow-1">
                                    <h6 class="mb-1 fw-semibold">{{ game.game_name }}</h6>
                                    <p class="mb-1 text-muted small">Prompt Engineering Training</p>
                                    <small class="text-success">
                                        <i class="fas fa-check-circle me-1"></i>Access granted: {{ game.access_granted_at|date:"M d, Y" }}
                                    </small>
                                </div>
                                <div class="text-end">
                                    <i class="fas fa-arrow-right text-muted"></i>
                                </div>
                            </div>
                        </a>
                        {% endfor %}
                    </div>
                    {% else %}
                    <div class="p-4 text-center">
                        <div class="mb-3">
                            <i class="fas fa-gamepad fa-3x text-muted"></i>
                        </div>
                        <h6 class="text-muted">No Games Available</h6>
                        <p class="text-muted small mb-0">No games are currently available for your company.</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light border-0 text-center">
                    <a href="{% url 'corporate:game_list' %}" class="btn btn-primary">
                        <i class="fas fa-list me-2"></i>View All Games
                    </a>
                </div>
            </div>
        </div>

        <div class="col-lg-6">
            {% include 'corporate/game_progress_widget.html' %}
        </div>
    </div>

    <!-- Leaderboard Section -->
    <div class="row g-4 mb-4">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-gradient-warning text-white border-0">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-trophy fa-lg me-3"></i>
                        <div>
                            <h5 class="mb-0 fw-bold">Top Performers</h5>
                            <small class="text-white-75">Company leaderboard highlights</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if top_performers %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 fw-semibold text-muted small">RANK</th>
                                    <th class="border-0 fw-semibold text-muted small">USER</th>
                                    <th class="border-0 fw-semibold text-muted small">SCORE</th>
                                    <th class="border-0 fw-semibold text-muted small">HIGHEST ROLE</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for entry in top_performers %}
                                <tr {% if entry.user == user %}class="bg-primary bg-opacity-10"{% endif %}>
                                    <td class="border-0 py-3">
                                        <div class="d-flex align-items-center">
                                            {% if forloop.counter == 1 %}
                                            <div class="bg-warning bg-opacity-25 rounded-circle p-2 me-2">
                                                <i class="fas fa-crown text-warning"></i>
                                            </div>
                                            {% elif forloop.counter == 2 %}
                                            <div class="bg-secondary bg-opacity-25 rounded-circle p-2 me-2">
                                                <i class="fas fa-medal text-secondary"></i>
                                            </div>
                                            {% elif forloop.counter == 3 %}
                                            <div class="bg-warning bg-opacity-25 rounded-circle p-2 me-2">
                                                <i class="fas fa-award text-warning"></i>
                                            </div>
                                            {% else %}
                                            <div class="bg-light rounded-circle p-2 me-2">
                                                <span class="small fw-bold text-muted">{{ forloop.counter }}</span>
                                            </div>
                                            {% endif %}
                                        </div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <div class="fw-semibold">{{ entry.user.get_full_name|default:entry.user.username }}</div>
                                        {% if entry.user == user %}
                                        <small class="text-primary fw-semibold">You</small>
                                        {% endif %}
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="badge bg-success rounded-pill">{{ entry.score }}</span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="text-muted">{{ entry.highest_role|title }}</span>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="p-4 text-center">
                        <div class="mb-3">
                            <i class="fas fa-trophy fa-3x text-muted"></i>
                        </div>
                        <h6 class="text-muted">No Leaderboard Entries</h6>
                        <p class="text-muted small mb-0">No leaderboard entries yet. Be the first to complete a game!</p>
                    </div>
                    {% endif %}
                </div>
                <div class="card-footer bg-light border-0 text-center">
                    <a href="{% url 'corporate:leaderboard' %}" class="btn btn-warning">
                        <i class="fas fa-trophy me-2"></i>View Full Leaderboard
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Recent Activity Section -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-gradient-info text-white border-0">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-history fa-lg me-3"></i>
                        <div>
                            <h5 class="mb-0 fw-bold">Recent Activity</h5>
                            <small class="text-white-75">Latest training sessions and progress</small>
                        </div>
                    </div>
                </div>
                <div class="card-body p-0">
                    {% if recent_sessions %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-light">
                                <tr>
                                    <th class="border-0 fw-semibold text-muted small">DATE</th>
                                    <th class="border-0 fw-semibold text-muted small">USER</th>
                                    <th class="border-0 fw-semibold text-muted small">ROLE</th>
                                    <th class="border-0 fw-semibold text-muted small">SCORE</th>
                                    <th class="border-0 fw-semibold text-muted small">STATUS</th>
                                </tr>
                            </thead>
                            <tbody>
                                {% for session in recent_sessions %}
                                <tr>
                                    <td class="border-0 py-3">
                                        <div class="small">{{ session.updated_at|date:"M d, Y" }}</div>
                                        <div class="text-muted small">{{ session.updated_at|date:"H:i" }}</div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <div class="fw-semibold">{{ session.user.get_full_name|default:session.user.username }}</div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="badge bg-secondary bg-opacity-75">{{ session.current_role|title }}</span>
                                    </td>
                                    <td class="border-0 py-3">
                                        <div class="fw-bold text-primary">{{ session.performance_score }}</div>
                                    </td>
                                    <td class="border-0 py-3">
                                        {% if session.game_completed %}
                                        <span class="badge bg-success rounded-pill">
                                            <i class="fas fa-check me-1"></i>Completed
                                        </span>
                                        {% else %}
                                        <span class="badge bg-primary rounded-pill">
                                            <i class="fas fa-play me-1"></i>In Progress
                                        </span>
                                        {% endif %}
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="p-4 text-center">
                        <div class="mb-3">
                            <i class="fas fa-history fa-3x text-muted"></i>
                        </div>
                        <h6 class="text-muted">No Recent Activity</h6>
                        <p class="text-muted small mb-3">No recent activity. Start playing to see your progress!</p>
                        <a href="{% url 'corporate:game_list' %}" class="btn btn-primary">
                            <i class="fas fa-play me-2"></i>Start Training
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Dashboard Hero Section */
.dashboard-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Company Logo Styling */
.company-logo {
    max-height: 80px;
    max-width: 120px;
    object-fit: contain;
}

.company-status {
    width: 16px;
    height: 16px;
}

.company-logo-placeholder {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Enhanced Cards */
.hover-lift {
    transition: all 0.3s ease;
}

.hover-lift:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

.stat-card {
    transition: all 0.3s ease;
}

.stat-card:hover {
    transform: translateY(-2px);
}

/* Quick Actions */
.quick-actions .btn {
    transition: all 0.3s ease;
}

.quick-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Dashboard Stats */
.dashboard-stats .stat-card {
    transition: all 0.3s ease;
}

.dashboard-stats .stat-card:hover {
    transform: scale(1.05);
}

/* Company Profile */
.company-profile .company-avatar {
    position: relative;
}

.user-role {
    font-size: 0.875rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .dashboard-hero .hero-content {
        padding: 2rem !important;
    }

    .display-5 {
        font-size: 2rem;
    }

    .quick-actions {
        justify-content: center;
    }

    .dashboard-stats .row {
        justify-content: center;
    }
}

/* Animation for floating elements */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}
</style>
{% endblock %}
