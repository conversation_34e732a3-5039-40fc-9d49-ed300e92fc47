from django.core.management.base import BaseCommand
from django.db import transaction
from game.models import GameSession
from corporate.models import CorporateUser
import logging

logger = logging.getLogger(__name__)

class Command(BaseCommand):
    help = 'Fix game sessions that are missing company associations for users with corporate profiles'

    def add_arguments(self, parser):
        parser.add_argument(
            '--dry-run',
            action='store_true',
            help='Show what would be updated without making changes',
        )

    def handle(self, *args, **options):
        dry_run = options['dry_run']
        
        if dry_run:
            self.stdout.write(self.style.WARNING('DRY RUN MODE - No changes will be made'))
        
        # Find game sessions without companies for users who have corporate profiles
        sessions_to_fix = []
        
        for session in GameSession.objects.filter(company__isnull=True, user__isnull=False):
            try:
                corporate_profile = CorporateUser.objects.get(user=session.user)
                sessions_to_fix.append((session, corporate_profile.company))
                self.stdout.write(
                    f"Found session {session.id} for user {session.user.username} "
                    f"that should be associated with company {corporate_profile.company.name}"
                )
            except CorporateUser.DoesNotExist:
                # User doesn't have a corporate profile, skip
                continue
        
        if not sessions_to_fix:
            self.stdout.write(self.style.SUCCESS('No game sessions need fixing'))
            return
        
        self.stdout.write(f"Found {len(sessions_to_fix)} game sessions to fix")
        
        if not dry_run:
            with transaction.atomic():
                for session, company in sessions_to_fix:
                    session.company = company
                    session.save(update_fields=['company'])
                    self.stdout.write(
                        self.style.SUCCESS(
                            f"Updated session {session.id} for user {session.user.username} "
                            f"with company {company.name}"
                        )
                    )
            
            self.stdout.write(
                self.style.SUCCESS(f'Successfully updated {len(sessions_to_fix)} game sessions')
            )
        else:
            self.stdout.write(
                self.style.WARNING(f'Would update {len(sessions_to_fix)} game sessions (dry run)')
            )
