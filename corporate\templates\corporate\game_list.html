{% extends 'corporate/base.html' %}

{% block title %}
    {% if company %}
        Training Modules - {{ company.name }}
    {% else %}
        Training Modules - Corporate Prompt Master
    {% endif %}
{% endblock %}

{% block content %}
<!-- Enhanced Games Hero Section -->
<section class="games-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <h1 class="display-5 fw-bold mb-3 text-shadow">
                                    <i class="fas fa-graduation-cap me-3"></i>
                                    Training <span class="text-gradient">Modules</span>
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Master prompt engineering through interactive challenges designed for corporate professionals.
                                </p>
                                {% if is_public_view %}
                                <div class="guest-actions d-flex flex-wrap gap-3">
                                    <a href="{% url 'corporate:corporate_login' %}" class="btn btn-light btn-lg px-4 py-3 fw-semibold">
                                        <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                    </a>
                                    <a href="{% url 'corporate:corporate_register' %}" class="btn btn-outline-light btn-lg px-4 py-3 fw-semibold">
                                        <i class="fas fa-user-plus me-2"></i>Join Now
                                    </a>
                                </div>
                                {% endif %}
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="training-stats text-center">
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ available_games.count }}</div>
                                            <small class="text-white-75">Available Modules</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">8</div>
                                            <small class="text-white-75">Career Levels</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% if is_public_view %}
<!-- Guest Information Banner -->
<div class="container-fluid mb-5">
    <div class="alert alert-info border-0 rounded-3 shadow-sm">
        <div class="row align-items-center">
            <div class="col-lg-8">
                <div class="d-flex align-items-center">
                    <div class="bg-info bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="fas fa-info-circle fa-lg text-info"></i>
                    </div>
                    <div>
                        <h6 class="fw-bold mb-1">Guest Access Available</h6>
                        <p class="mb-0">Experience our training modules as a guest. Sign up to save progress and compete on leaderboards.</p>
                    </div>
                </div>
            </div>
            <div class="col-lg-4 text-lg-end mt-3 mt-lg-0">
                <a href="{% url 'corporate:corporate_register' %}" class="btn btn-info fw-semibold me-2">
                    <i class="fas fa-rocket me-1"></i>Get Started
                </a>
                <a href="{% url 'corporate:corporate_login' %}" class="btn btn-outline-info fw-semibold">
                    <i class="fas fa-sign-in-alt me-1"></i>Sign In
                </a>
            </div>
        </div>
    </div>
</div>
{% endif %}

{% if user_sessions %}
<!-- User Progress Section -->
<div class="container-fluid mb-5">
    <div class="card border-0 shadow-lg">
        <div class="card-header bg-gradient-success text-white py-4">
            <h4 class="mb-0 fw-bold">
                <i class="fas fa-chart-line me-2"></i>Your Learning Progress
            </h4>
        </div>
        <div class="card-body p-4">
            {% include 'corporate/user_game_progress.html' %}
        </div>
    </div>
</div>
{% endif %}

<!-- Enhanced Games Grid -->
{% if available_games %}
<div class="container-fluid">
    <div class="row g-4">
        {% for game in available_games %}
        <div class="col-lg-6 col-xl-4">
            <div class="game-card card border-0 shadow-lg h-100 hover-lift">
                <div class="card-header bg-gradient-primary text-white border-0 py-4">
                    <div class="d-flex align-items-center justify-content-between">
                        <h5 class="mb-0 fw-bold">{{ game.game_name }}</h5>
                        <div class="game-level-badge">
                            <span class="badge bg-white bg-opacity-25 text-white px-3 py-2">
                                Level {{ forloop.counter }}
                            </span>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="game-icon-container text-center mb-4">
                        <div class="game-icon bg-gradient-primary rounded-circle p-4 d-inline-block shadow-sm">
                            <i class="fas fa-brain fa-2x text-white"></i>
                        </div>
                    </div>

                    <div class="game-content">
                        <h5 class="card-title fw-bold mb-3">Prompt Engineering Mastery</h5>
                        <p class="card-text text-muted mb-4">
                            Advance through corporate roles by mastering AI prompt engineering. From entry-level to executive positions,
                            each challenge builds your expertise in real-world scenarios.
                        </p>

                        <div class="game-features mb-4">
                            <div class="row g-3">
                                <div class="col-6">
                                    <div class="feature-item d-flex align-items-center">
                                        <div class="feature-icon bg-primary bg-opacity-10 rounded-circle p-2 me-2">
                                            <i class="fas fa-trophy text-primary"></i>
                                        </div>
                                        <small class="fw-semibold">8 Career Levels</small>
                                    </div>
                                </div>
                                <div class="col-6">
                                    <div class="feature-item d-flex align-items-center">
                                        <div class="feature-icon bg-success bg-opacity-10 rounded-circle p-2 me-2">
                                            <i class="fas fa-certificate text-success"></i>
                                        </div>
                                        <small class="fw-semibold">Certificates</small>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="game-details">
                            <div class="row g-3 mb-3">
                                <div class="col-12">
                                    <div class="detail-item bg-light rounded-3 p-3">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <div>
                                                <small class="text-muted">Access Granted</small>
                                                <div class="fw-semibold">{{ game.access_granted_at|date:"M d, Y" }}</div>
                                            </div>
                                            <div class="text-end">
                                                <small class="text-muted">Status</small>
                                                <div>
                                                    {% if game.is_active %}
                                                    <span class="badge bg-success px-3 py-2">
                                                        <i class="fas fa-check me-1"></i>Active
                                                    </span>
                                                    {% else %}
                                                    <span class="badge bg-danger px-3 py-2">
                                                        <i class="fas fa-times me-1"></i>Inactive
                                                    </span>
                                                    {% endif %}
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            {% if game.access_expires_at %}
                            <div class="alert alert-warning border-0 rounded-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-clock me-2"></i>
                                    <small>Access expires {{ game.access_expires_at|date:"M d, Y" }}</small>
                                </div>
                            </div>
                            {% endif %}

                            {% if is_public_view and game.is_public %}
                            <div class="alert alert-info border-0 rounded-3 mb-3">
                                <div class="d-flex align-items-center">
                                    <i class="fas fa-info-circle me-2"></i>
                                    <small>
                                        Guest mode active.
                                        <a href="{% url 'corporate:corporate_login' %}" class="alert-link fw-semibold">Sign in</a>
                                        to save progress.
                                    </small>
                                </div>
                            </div>
                            {% endif %}
                        </div>
                    </div>

                    <div class="card-footer bg-transparent border-0 pt-0">
                        <div class="d-grid">
                            {% if user_sessions %}
                            <a href="{% url 'game:index' %}{% if company %}?company={{ company.slug }}{% endif %}"
                               class="btn btn-primary btn-lg fw-semibold">
                                <i class="fas fa-play me-2"></i>Continue Training
                            </a>
                            {% else %}
                            <a href="{% url 'game:index' %}{% if company %}?company={{ company.slug }}{% endif %}"
                               class="btn btn-primary btn-lg fw-semibold">
                                <i class="fas fa-rocket me-2"></i>Start Training
                            </a>
                            {% endif %}
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
        </div>
        {% else %}
            {% if is_public_view %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No public games are currently available.
            </div>

            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-gamepad fa-5x text-muted mb-3"></i>
                    <h3>No Public Games Available</h3>
                    <p class="lead">Please check back later for public games.</p>
                </div>
            </div>
            {% else %}
            <div class="alert alert-info">
                <i class="fas fa-info-circle me-2"></i>No games are currently available for your company.
            </div>

            <div class="card shadow">
                <div class="card-body text-center py-5">
                    <i class="fas fa-gamepad fa-5x text-muted mb-3"></i>
                    <h3>No Games Available</h3>
                    <p class="lead">Your company administrator needs to activate games for your account.</p>

                    {% if user.corporate_profile.is_company_admin %}
                    <div class="mt-4">
                        <p>As an administrator, you can contact support to add games to your company account.</p>
                        <a href="mailto:<EMAIL>" class="btn btn-primary">
                            <i class="fas fa-envelope me-2"></i>Contact Support
                        </a>
                    </div>
                    {% endif %}
                </div>
            </div>
            {% endif %}
        {% endif %}
    </div>
</div>

{% if available_games %}
<div class="row">
    <div class="col-md-12">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h5 class="mb-0"><i class="fas fa-info-circle me-2"></i>About Prompt Engineering Training</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-8">
                        <h4>What is Prompt Engineering?</h4>
                        <p>Prompt engineering is the art and science of crafting effective prompts to get the best results from AI language models. It's a critical skill for working with AI systems in a corporate environment.</p>

                        <h4>Game Overview</h4>
                        <p>In this game, you'll progress through a corporate hierarchy by completing prompt engineering challenges. Starting as an applicant, you'll work your way up through various departments, eventually reaching executive positions.</p>

                        <h4>Key Features</h4>
                        <ul>
                            <li><strong>Career Progression:</strong> Advance through 20+ corporate roles</li>
                            <li><strong>Real-world Challenges:</strong> Solve practical prompt engineering tasks</li>
                            <li><strong>Performance Scoring:</strong> Earn points based on the quality of your prompts</li>
                            <li><strong>Detailed Feedback:</strong> Receive personalized feedback from AI managers</li>
                            <li><strong>Certificate:</strong> Earn a certificate upon completion</li>
                        </ul>
                    </div>
                    <div class="col-md-4">
                        <div class="card">
                            <div class="card-header bg-light">
                                <h5 class="mb-0">Benefits</h5>
                            </div>
                            <div class="card-body">
                                <ul class="list-group list-group-flush">
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Improve AI communication skills
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Learn effective prompt patterns
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Understand AI capabilities and limitations
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Develop problem-solving skills
                                    </li>
                                    <li class="list-group-item">
                                        <i class="fas fa-check-circle text-success me-2"></i>Earn a verifiable certificate
                                    </li>
                                </ul>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Restart Game Confirmation Modal -->
<div class="modal fade" id="restartGameModal" tabindex="-1" aria-labelledby="restartGameModalLabel" aria-hidden="true">
  <div class="modal-dialog">
    <div class="modal-content">
      <div class="modal-header bg-danger text-white">
        <h5 class="modal-title" id="restartGameModalLabel"><i class="fas fa-exclamation-triangle me-2"></i>Restart Game?</h5>
        <button type="button" class="btn-close btn-close-white" data-bs-dismiss="modal" aria-label="Close"></button>
      </div>
      <div class="modal-body">
        <p class="lead">Are you sure you want to restart the game?</p>
        <div class="alert alert-warning">
          <i class="fas fa-exclamation-circle me-2"></i>
          <strong>Warning:</strong> This will clear all your messages and reset your progress.
        </div>
      </div>
      <div class="modal-footer">
        <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
        <button type="button" class="btn btn-danger" id="confirmRestartButton">
          <i class="fas fa-redo-alt me-2"></i>Yes, Restart Game
        </button>
      </div>
    </div>
  </div>
</div>

<!-- JavaScript for Restart Game functionality -->
{% block extra_js %}
<script>
  document.addEventListener('DOMContentLoaded', function() {
    // Handle restart game button click
    const confirmRestartButton = document.getElementById('confirmRestartButton');
    let currentSessionId = null;

    // Store the session ID when a restart button is clicked
    const restartButtons = document.querySelectorAll('.restart-game-btn');
    restartButtons.forEach(button => {
      button.addEventListener('click', function() {
        currentSessionId = this.getAttribute('data-session-id');
      });
    });

    // Handle confirmation button click
    if (confirmRestartButton) {
      confirmRestartButton.addEventListener('click', async function() {
        try {
          // Show loading state
          this.innerHTML = '<span class="spinner-border spinner-border-sm me-2" role="status" aria-hidden="true"></span>Restarting...';
          this.disabled = true;

          // Call the start_game API endpoint with a cache-busting parameter
          const timestamp = new Date().getTime();
          const response = await fetch(`/game/api/start_game/?t=${timestamp}`, {
            method: 'GET',
            headers: {
              'Cache-Control': 'no-cache, no-store, must-revalidate',
              'Pragma': 'no-cache',
              'Expires': '0'
            }
          });

          if (response.ok) {
            // Get the response data to ensure the API call is complete
            await response.json();

            // Redirect to the game page with a cache-busting parameter
            window.location.href = `/game/?restart=true&t=${timestamp}`;
          } else {
            throw new Error('Failed to restart game');
          }
        } catch (error) {
          console.error('Error restarting game:', error);
          alert('An error occurred while trying to restart the game. Please try again.');

          // Reset button state
          this.innerHTML = '<i class="fas fa-redo-alt me-2"></i>Yes, Restart Game';
          this.disabled = false;
        }
      });
    }
  });
</script>
{% endblock %}

{% block extra_css %}
<style>
/* Games Hero Section */
.games-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Game Cards */
.game-card {
    transition: all 0.3s ease;
    border-radius: 1rem;
    overflow: hidden;
}

.hover-lift:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.game-icon-container .game-icon {
    width: 80px;
    height: 80px;
    display: flex;
    align-items: center;
    justify-content: center;
    transition: all 0.3s ease;
}

.game-card:hover .game-icon {
    transform: scale(1.1);
}

/* Feature Items */
.feature-item .feature-icon {
    width: 32px;
    height: 32px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Game Details */
.detail-item {
    transition: all 0.3s ease;
}

.game-card:hover .detail-item {
    background-color: #f8f9fa !important;
}

/* Ensure text remains visible on hover */
.game-card:hover .detail-item .fw-semibold,
.game-card:hover .detail-item .text-muted,
.game-card:hover .detail-item small,
.game-card:hover .detail-item div {
    color: inherit !important;
}

.game-card:hover .detail-item .text-muted {
    color: #6c757d !important;
}

.game-card:hover .detail-item .fw-semibold {
    color: #212529 !important;
}

/* Training Stats */
.training-stats .stat-card {
    transition: all 0.3s ease;
}

.training-stats .stat-card:hover {
    transform: scale(1.05);
}

/* Buttons */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Guest Actions */
.guest-actions .btn {
    transition: all 0.3s ease;
}

.guest-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .games-hero .hero-content {
        padding: 2rem !important;
    }

    .display-5 {
        font-size: 2rem;
    }

    .guest-actions {
        justify-content: center;
    }

    .training-stats .row {
        justify-content: center;
    }
}

/* Animation for floating elements */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}

/* Enhanced Card Styling */
.game-card .card-header {
    position: relative;
    overflow: hidden;
}

.game-card .card-header::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="dots" width="20" height="20" patternUnits="userSpaceOnUse"><circle cx="10" cy="10" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23dots)"/></svg>');
}

.game-level-badge .badge {
    font-size: 0.75rem;
    font-weight: 600;
}
</style>
{% endblock %}
{% endblock %}