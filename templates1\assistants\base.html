{% extends 'base/layout.html' %}
{% load static %}

{% block content %}
<div class="container-fluid p-0">
    <div class="row g-0">
        <!-- Enhanced Assistant Sidebar -->
        <div class="col-lg-3 col-xl-2 d-none d-lg-block">
            <div class="assistant-sidebar bg-white border-end shadow-sm h-100">
                <div class="position-sticky" style="top: var(--header-height);">
                    <!-- Enhanced Company Info -->
                    <div class="p-4 border-bottom">
                        <div class="text-center">
                            {% if company.info.logo and company.info.logo.url %}
                                <div class="position-relative d-inline-block mb-3">
                                    <img src="{{ company.info.logo.url }}"
                                         alt="{{ company.name }} Logo"
                                         class="rounded-circle shadow-sm"
                                         style="width: 64px; height: 64px; object-fit: cover;">
                                    <div class="position-absolute bottom-0 end-0 bg-success rounded-circle border border-white"
                                         style="width: 16px; height: 16px;"></div>
                                </div>
                            {% else %}
                                <div class="bg-gradient-primary rounded-circle p-3 d-inline-block mb-3">
                                    <i class="fas fa-building text-white fa-lg"></i>
                                </div>
                            {% endif %}
                            <h6 class="fw-bold mb-1">{{ company.name }}</h6>
                            <small class="text-muted text-uppercase">
                                {% if company.entity_type == 'community' %}
                                    Community Workspace
                                {% else %}
                                    Corporate Workspace
                                {% endif %}
                            </small>
                        </div>
                    </div>

                    <!-- Enhanced Navigation -->
                    <div class="p-3">
                        <h6 class="text-muted text-uppercase fw-bold mb-3 px-2">
                            <i class="fas fa-robot me-2"></i>Assistants
                        </h6>
                        <nav class="nav flex-column gap-1">
                            <a href="{% url 'assistants:list' company.id %}"
                               class="nav-link d-flex align-items-center rounded-3 p-3 {% if request.resolver_match.url_name == 'list' %}active bg-primary text-white{% endif %}">
                                <i class="fas fa-th-large me-3"></i>
                                <span class="fw-semibold">All Assistants</span>
                            </a>
                            <a href="{% url 'assistants:create' company.id %}"
                               class="nav-link d-flex align-items-center rounded-3 p-3 {% if request.resolver_match.url_name == 'create' %}active bg-primary text-white{% endif %}">
                                <i class="fas fa-plus-circle me-3"></i>
                                <span class="fw-semibold">
                                    {% if company.entity_type == 'community' %}
                                        Create Community Assistant
                                    {% else %}
                                        Create Assistant
                                    {% endif %}
                                </span>
                            </a>
                        </nav>

                        {% if assistant %}
                            <hr class="my-4">
                            <h6 class="text-muted text-uppercase fw-bold mb-3 px-2">
                                <i class="fas fa-cog me-2"></i>Current Assistant
                            </h6>
                            <div class="card bg-light border-0 mb-3">
                                <div class="card-body p-3">
                                    <div class="d-flex align-items-center mb-2">
                                        <div class="bg-primary rounded-circle p-2 me-3">
                                            <i class="fas fa-robot text-white"></i>
                                        </div>
                                        <div class="flex-grow-1">
                                            <h6 class="mb-0 fw-bold">{{ assistant.name }}</h6>
                                            <small class="text-muted">Active Assistant</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}"
                           class="nav-link {% if request.resolver_match.url_name == 'assistant_chat' %}active{% endif %}">
                           <i class="bi bi-chat-dots me-2"></i>
                           Chat
                        </a>
                        {% if assistant.is_public %}
                        <a href="{% url 'assistants:assistant_chat' slug=assistant.slug %}"
                           class="nav-link" target="_blank"> {# Open in new tab #}
                           <i class="bi bi-box-arrow-up-right me-2"></i>
                           Public Chat View
                        </a>
                        {% endif %}
                        <a href="{% url 'assistants:history' company.id assistant.id %}"
                           class="nav-link {% if request.resolver_match.url_name == 'history' %}active{% endif %}">
                            <i class="bi bi-clock-history me-2"></i>
                            Chat History
                        </a>
                        <a href="{% url 'assistants:train' company.id assistant.id %}"
                           class="nav-link {% if request.resolver_match.url_name == 'train' %}active{% endif %}">
                            <i class="bi bi-arrow-up-circle me-2"></i>
                            Train Assistant
                        </a>
                        <a href="{% url 'assistants:analytics' company.id assistant.id %}"
                           class="nav-link {% if request.resolver_match.url_name == 'analytics' %}active{% endif %}">
                            <i class="bi bi-graph-up me-2"></i>
                            Analytics
                        </a>
                        <a href="{% url 'assistants:usage' company.id assistant.id %}"
                           class="nav-link {% if request.resolver_match.url_name == 'usage' %}active{% endif %}">
                            <i class="bi bi-speedometer2 me-2"></i>
                            Usage Stats
                        </a>
                        {% if user == company.owner or user.is_staff %}
                            <a href="{% url 'assistants:update' company.id assistant.id %}"
                               class="nav-link {% if request.resolver_match.url_name == 'update' %}active{% endif %}">
                                <i class="bi bi-gear me-2"></i>
                                Settings
                            </a>
                        {% endif %}
                    {% endif %}
                </div>

                <!-- Quick Stats -->
                {% if assistant %}
                    <div class="card border-0 shadow-sm mt-4">
                        <div class="card-body">
                            <h6 class="card-subtitle mb-3 text-muted">Quick Stats</h6>
                            <div class="d-flex align-items-center mb-2">
                                <div class="flex-grow-1">Total Interactions</div>
                                <div class="badge bg-light text-dark">{{ assistant.total_interactions }}</div>
                            </div>
                            {% if assistant.average_rating %}
                                <div class="d-flex align-items-center mb-2">
                                    <div class="flex-grow-1">Average Rating</div>
                                    <div class="badge bg-light text-dark">
                                        <i class="bi bi-star-fill text-warning me-1"></i>
                                        {{ assistant.average_rating|floatformat:1 }}
                                    </div>
                                </div>
                            {% endif %}
                            {% if assistant.last_trained %}
                                <div class="d-flex align-items-center">
                                    <div class="flex-grow-1">Last Trained</div>
                                    <div class="badge bg-light text-dark">
                                        {{ assistant.last_trained|timesince }} ago
                                    </div>
                                </div>
                            {% endif %}
                        </div>
                    </div>
                {% endif %}
            </div>
        </div>

        <!-- Main Content -->
        <main class="col-lg-9 col-xl-10 ms-sm-auto px-md-4 py-4">
            <!-- Header -->
            <div class="d-flex justify-content-between flex-wrap flex-md-nowrap align-items-center pb-2 mb-3 border-bottom">
                <div>
                    {% block page_header %}{% endblock %}
                </div>
                <div class="btn-toolbar">
                    {% block page_actions %}{% endblock %}
                </div>
            </div>

            <!-- Content -->
            {% block main_content %}{% endblock %}
        </main>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
.sidebar {
    position: fixed;
    top: 0;
    bottom: 0;
    left: 0;
    z-index: 100;
    padding: 48px 0 0;
    box-shadow: inset -1px 0 0 rgba(0, 0, 0, .1);
    background-color: #f8f9fa;
}

.sidebar .nav-link {
    font-weight: 500;
    color: #333;
    padding: .5rem 1rem;
}

.sidebar .nav-link.active {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, .1);
}

.sidebar .nav-link:hover {
    color: #0d6efd;
    background-color: rgba(13, 110, 253, .05);
}

.sidebar-heading {
    font-size: .75rem;
    text-transform: uppercase;
}

.dropdown-divider {
    border-color: rgba(0, 0, 0, .1);
}
</style>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    const tooltips = document.querySelectorAll('[data-bs-toggle="tooltip"]');
    tooltips.forEach(tooltip => {
        new bootstrap.Tooltip(tooltip);
    });

    // Handle responsive sidebar
    const sidebar = document.querySelector('.sidebar');
    if (sidebar) {
        const toggleSidebar = document.createElement('button');
        toggleSidebar.className = 'btn btn-link d-lg-none position-fixed';
        toggleSidebar.style.top = '1rem';
        toggleSidebar.style.left = '1rem';
        toggleSidebar.style.zIndex = '1031';
        toggleSidebar.innerHTML = '<i class="bi bi-list fs-4"></i>';

        document.body.appendChild(toggleSidebar);

        toggleSidebar.addEventListener('click', () => {
            sidebar.classList.toggle('d-none');
        });
    }
});
</script>
{# TinyMCE init moved to assistant_form.html template #}
{% endblock %}
