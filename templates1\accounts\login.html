{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Log In - Corporate Prompt Master{% endblock %}

{% block body_class %}no-sidebar{% endblock %}

{% block content %}
<div class="min-vh-100 d-flex align-items-center bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-6 col-lg-5 col-xl-4">
                <!-- Enhanced Login Card -->
                <div class="card border-0 shadow-lg">
                    <!-- Header Section -->
                    <div class="card-header bg-gradient-primary text-white text-center border-0 py-4">
                        <div class="mb-3">
                            <div class="bg-white bg-opacity-25 rounded-circle p-3 d-inline-block">
                                <i class="fas fa-brain fa-2x text-white"></i>
                            </div>
                        </div>
                        <h2 class="h4 mb-1 fw-bold">Welcome Back</h2>
                        <p class="mb-0 text-white-75">Sign in to Corporate Prompt Master</p>
                    </div>

                    <!-- Form Section -->
                    <div class="card-body p-4">
                        <form method="post" novalidate>
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                                <div class="alert alert-danger border-0 rounded-3" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    {% for error in form.non_field_errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Username Field -->
                            <div class="mb-3">
                                <label for="{{ form.username.id_for_label }}" class="form-label fw-semibold">
                                    <i class="fas fa-user me-2 text-primary"></i>Username or Email
                                </label>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-envelope text-muted"></i>
                                    </span>
                                    {% render_field form.username class="form-control border-start-0 ps-0" placeholder="Enter your username or email" %}
                                </div>
                                {% if form.username.errors %}
                                    <div class="invalid-feedback d-block">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ form.username.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Password Field -->
                            <div class="mb-4">
                                <div class="d-flex justify-content-between align-items-center mb-2">
                                    <label for="{{ form.password.id_for_label }}" class="form-label fw-semibold mb-0">
                                        <i class="fas fa-lock me-2 text-primary"></i>Password
                                    </label>
                                    <a href="{% url 'accounts:password_reset' %}" class="small text-decoration-none">
                                        <i class="fas fa-question-circle me-1"></i>Forgot Password?
                                    </a>
                                </div>
                                <div class="input-group">
                                    <span class="input-group-text bg-light border-end-0">
                                        <i class="fas fa-key text-muted"></i>
                                    </span>
                                    {% render_field form.password class="form-control border-start-0 ps-0" placeholder="Enter your password" %}
                                </div>
                                {% if form.password.errors %}
                                    <div class="invalid-feedback d-block">
                                        <i class="fas fa-exclamation-circle me-1"></i>
                                        {{ form.password.errors|join:", " }}
                                    </div>
                                {% endif %}
                            </div>

                            <!-- Captcha Field -->
                            {% if form.captcha %}
                                <div class="mb-3">
                                    <label class="form-label fw-semibold">
                                        <i class="fas fa-shield-alt me-2 text-primary"></i>Security Verification
                                    </label>
                                    {{ form.captcha }}
                                </div>
                            {% endif %}

                            <!-- Submit Button -->
                            <div class="d-grid mb-4">
                                <button type="submit" class="btn btn-primary btn-lg fw-semibold">
                                    <i class="fas fa-sign-in-alt me-2"></i>Sign In
                                </button>
                            </div>

                            <input type="hidden" name="next" value="{{ next }}">
                        </form>

                        <!-- Registration Link -->
                        <div class="text-center">
                            <p class="text-muted mb-0">
                                Don't have an account?
                                <a href="{% url 'accounts:register' %}" class="text-decoration-none fw-semibold">
                                    Create Account
                                </a>
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Enhanced SSO Options -->
                {% if social_auth_providers %}
                    <div class="card border-0 shadow-sm mt-4">
                        <div class="card-body p-4">
                            <div class="text-center mb-3">
                                <small class="text-muted text-uppercase fw-bold">Or continue with</small>
                            </div>
                            <div class="d-grid gap-2">
                                {% for provider in social_auth_providers %}
                                    <a href="{% url 'social:begin' provider.id %}"
                                       class="btn btn-outline-secondary d-flex align-items-center justify-content-center">
                                        <i class="fab fa-{{ provider.icon }} me-2"></i>
                                        <span>{{ provider.name }}</span>
                                    </a>
                                {% endfor %}
                            </div>
                        </div>
                    </div>
                {% endif %}

                <!-- Enhanced Help Links -->
                <div class="text-center mt-4">
                    <div class="d-flex justify-content-center gap-3">
                        <a href="{% url 'about' %}" class="text-decoration-none text-muted small">
                            <i class="fas fa-info-circle me-1"></i>About
                        </a>
                        <a href="{% url 'privacy' %}" class="text-decoration-none text-muted small">
                            <i class="fas fa-shield-alt me-1"></i>Privacy
                        </a>
                        <a href="{% url 'terms' %}" class="text-decoration-none text-muted small">
                            <i class="fas fa-file-contract me-1"></i>Terms
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Focus username field
    document.getElementById('{{ form.username.id_for_label }}').focus();

    // Enable password toggle
    const togglePassword = document.createElement('button');
    togglePassword.type = 'button';
    togglePassword.className = 'btn btn-outline-secondary';
    togglePassword.innerHTML = '<i class="bi bi-eye"></i>';
    togglePassword.onclick = function() {
        const password = document.getElementById('{{ form.password.id_for_label }}');
        const type = password.getAttribute('type') === 'password' ? 'text' : 'password';
        password.setAttribute('type', type);
        this.innerHTML = type === 'password' ? '<i class="bi bi-eye"></i>' : '<i class="bi bi-eye-slash"></i>';
    };

    const passwordField = document.getElementById('{{ form.password.id_for_label }}');
    const wrapper = document.createElement('div');
    wrapper.className = 'input-group';
    passwordField.parentNode.insertBefore(wrapper, passwordField);
    wrapper.appendChild(passwordField);
    wrapper.appendChild(togglePassword);
});
</script>
{% endblock %}
