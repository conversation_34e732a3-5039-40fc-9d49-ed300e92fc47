{% extends 'base/layout.html' %}

{% block title %}Privacy Policy - Corporate Prompt Master{% endblock %}

{% block content %}
<!-- Enhanced Privacy Policy Hero Section -->
<section class="privacy-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <h1 class="display-4 fw-bold mb-3 text-shadow">
                                    <i class="fas fa-shield-alt me-3"></i>
                                    Privacy <span class="text-gradient">Policy</span>
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Your privacy is our priority. Learn how we collect, use, and protect
                                    your personal information when you use Corporate Prompt Master.
                                </p>
                                <div class="privacy-badges d-flex flex-wrap gap-3">
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-lock me-2"></i>GDPR Compliant
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-certificate me-2"></i>SOC 2 Certified
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-eye-slash me-2"></i>No Data Selling
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="privacy-stats text-center">
                                <div class="stat-item mb-4">
                                    <div class="stat-number display-6 fw-bold text-white">256</div>
                                    <div class="stat-label text-white-75">Bit Encryption</div>
                                </div>
                                <div class="stat-item mb-4">
                                    <div class="stat-number display-6 fw-bold text-white">100%</div>
                                    <div class="stat-label text-white-75">Transparent</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number display-6 fw-bold text-white">You</div>
                                    <div class="stat-label text-white-75">Own Your Data</div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="row mt-4">
                        <div class="col-12">
                            <div class="last-updated text-center">
                                <p class="mb-0 text-white-75">
                                    <i class="fas fa-calendar-alt me-2"></i>
                                    Last updated: {{ "now"|date:"F d, Y" }}
                                </p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Privacy Policy Content -->
<div class="container-fluid">
    <div class="row g-4">
        <!-- Main Content -->
        <div class="col-lg-8">
            <!-- Information Collection -->
            <div class="privacy-section card border-0 shadow-lg mb-4" id="information-collection">
                <div class="card-header bg-gradient-primary text-white border-0 py-4">
                    <h3 class="mb-0 fw-bold">
                        <i class="fas fa-database me-2"></i>Information We Collect
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <h5 class="fw-bold mb-3">Personal Information</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Name and email address
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Company information
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Profile preferences
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Communication preferences
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="fw-bold mb-3">Usage Information</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Platform usage patterns
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Feature interactions
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Performance metrics
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-check text-success me-2"></i>
                                    Error logs (anonymized)
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Data Usage -->
            <div class="privacy-section card border-0 shadow-lg mb-4" id="data-usage">
                <div class="card-header bg-gradient-success text-white border-0 py-4">
                    <h3 class="mb-0 fw-bold">
                        <i class="fas fa-cogs me-2"></i>How We Use Your Information
                    </h3>
                </div>
                <div class="card-body p-4">
                    <div class="row g-4">
                        <div class="col-md-6">
                            <h5 class="fw-bold mb-3">Service Delivery</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Provide platform access
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Personalize your experience
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Process transactions
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Provide customer support
                                </li>
                            </ul>
                        </div>
                        <div class="col-md-6">
                            <h5 class="fw-bold mb-3">Platform Improvement</h5>
                            <ul class="list-unstyled">
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Analyze usage patterns
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Improve AI models
                                </li>
                                <li class="mb-2">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Enhance security
                                </li>
                                <li class="mb-0">
                                    <i class="fas fa-arrow-right text-primary me-2"></i>
                                    Develop new features
                                </li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Contact Information -->
            <div class="privacy-section card border-0 shadow-lg mb-4" id="contact-info">
                <div class="card-header bg-gradient-dark text-white border-0 py-4">
                    <h3 class="mb-0 fw-bold">
                        <i class="fas fa-envelope me-2"></i>Contact Us
                    </h3>
                </div>
                <div class="card-body p-4 text-center">
                    <p class="mb-4">Have questions about our privacy practices? We're here to help.</p>
                    <div class="d-grid gap-2 d-md-flex justify-content-md-center">
                        <a href="mailto:<EMAIL>" class="btn btn-primary">
                            <i class="fas fa-envelope me-2"></i><EMAIL>
                        </a>
                        <a href="{% url 'contact' %}" class="btn btn-outline-primary">
                            <i class="fas fa-comments me-2"></i>Contact Form
                        </a>
                    </div>
                </div>
            </div>
        </div>

        <!-- Sidebar -->
        <div class="col-lg-4">
            <!-- Quick Navigation -->
            <div class="card border-0 shadow-lg mb-4 sticky-top">
                <div class="card-header bg-gradient-info text-white border-0 py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-list me-2"></i>Quick Navigation
                    </h5>
                </div>
                <div class="card-body p-3">
                    <ul class="list-unstyled mb-0">
                        <li class="mb-2">
                            <a href="#information-collection" class="text-decoration-none d-flex align-items-center">
                                <i class="fas fa-database text-primary me-2"></i>
                                Information Collection
                            </a>
                        </li>
                        <li class="mb-2">
                            <a href="#data-usage" class="text-decoration-none d-flex align-items-center">
                                <i class="fas fa-cogs text-success me-2"></i>
                                Data Usage
                            </a>
                        </li>
                        <li class="mb-0">
                            <a href="#contact-info" class="text-decoration-none d-flex align-items-center">
                                <i class="fas fa-envelope text-dark me-2"></i>
                                Contact Information
                            </a>
                        </li>
                    </ul>
                </div>
            </div>

            <!-- Related Links -->
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient-secondary text-white border-0 py-3">
                    <h5 class="mb-0 fw-bold">
                        <i class="fas fa-link me-2"></i>Related Policies
                    </h5>
                </div>
                <div class="card-body p-4">
                    <div class="d-grid gap-2">
                        <a href="{% url 'terms' %}" class="btn btn-outline-primary">
                            <i class="fas fa-file-contract me-2"></i>Terms of Service
                        </a>
                        <a href="{% url 'cookies' %}" class="btn btn-outline-primary">
                            <i class="fas fa-cookie-bite me-2"></i>Cookie Policy
                        </a>
                        <a href="{% url 'security' %}" class="btn btn-outline-primary">
                            <i class="fas fa-shield-alt me-2"></i>Security Information
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
/* Privacy Policy Hero Section */
.privacy-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Privacy Stats */
.privacy-stats .stat-item {
    transition: all 0.3s ease;
}

.privacy-stats .stat-item:hover {
    transform: translateY(-5px);
}

/* Privacy Sections */
.privacy-section {
    transition: all 0.3s ease;
    border-radius: 1rem;
    overflow: hidden;
}

.privacy-section:hover {
    transform: translateY(-5px);
    box-shadow: 0 15px 35px rgba(0,0,0,0.1) !important;
}

/* Smooth Scrolling */
html {
    scroll-behavior: smooth;
}

/* Sidebar Sticky */
.sticky-top {
    top: 2rem;
}

/* Links */
a {
    transition: all 0.3s ease;
}

a:hover {
    transform: translateX(5px);
}

/* Buttons */
.btn {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Cards */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .privacy-hero .hero-content {
        padding: 2rem !important;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .privacy-stats .stat-number {
        font-size: 2rem;
    }

    .sticky-top {
        position: relative !important;
        top: auto !important;
    }
}
</style>
{% endblock %}
{% endblock %}
