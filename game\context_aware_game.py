from flask import Flask, request, jsonify, send_from_directory, redirect, url_for
from flask_cors import CORS
import datetime
import uuid
import logging
import sys
import markdown
import os
from dotenv import load_dotenv

# Load environment variables from .env file
try:
    load_dotenv()
    logging.info("Loaded environment variables from .env file")
except Exception as e:
    logging.warning(f"Could not load .env file: {str(e)}")

# Import guideline evaluation module
try:
    from guideline_evaluation import evaluate_response_with_guidelines
    GUIDELINE_EVALUATION_AVAILABLE = True
    logging.info("Guideline evaluation module loaded successfully")
except ImportError:
    logging.warning("Guideline evaluation module not available. Falling back to basic evaluation.")
    GUIDELINE_EVALUATION_AVAILABLE = False

# Import enhanced evaluation module
try:
    from enhanced_evaluation import evaluate_response_with_enhanced_llm, calculate_score, generate_improvement_feedback
    from game_state_manager import check_for_promotion, process_task_completion, process_task_failure, get_performance_summary
    ENHANCED_EVALUATION_AVAILABLE = True
    print("Enhanced evaluation module loaded successfully")
    logging.info("Enhanced evaluation module loaded successfully")
except ImportError as e:
    print(f"Enhanced evaluation module not available: {str(e)}. Falling back to basic evaluation.")
    logging.warning(f"Enhanced evaluation module not available: {str(e)}. Falling back to basic evaluation.")
    ENHANCED_EVALUATION_AVAILABLE = False

# Import LLM response generator module
try:
    from llm_response_generator_openai_format import generate_response_with_llm
    LLM_RESPONSE_GENERATOR_AVAILABLE = True
    logging.info("LLM response generator module loaded successfully")
    # Print the function to verify it's imported correctly
    logging.info(f"generate_response_with_llm function: {generate_response_with_llm}")
except ImportError as e:
    logging.warning(f"LLM response generator module not available: {str(e)}. Falling back to template responses.")
    LLM_RESPONSE_GENERATOR_AVAILABLE = False

# Import LLM feedback generator module
try:
    from llm_feedback_generator import generate_manager_feedback
    LLM_FEEDBACK_GENERATOR_AVAILABLE = True
    logging.info("LLM feedback generator module loaded successfully")
    # Print the function to verify it's imported correctly
    logging.info(f"generate_manager_feedback function: {generate_manager_feedback}")
except ImportError as e:
    logging.warning(f"LLM feedback generator module not available: {str(e)}. Falling back to template feedback.")
    LLM_FEEDBACK_GENERATOR_AVAILABLE = False

# Import legacy LLM comparison module if needed
LEGACY_COMPARISON_AVAILABLE = False
if os.environ.get("USE_LEGACY_COMPARISON", "false").lower() == "true":
    try:
        from llm_comparison import evaluate_response_with_llm
        LEGACY_COMPARISON_AVAILABLE = True
        logging.info("Legacy LLM comparison module loaded successfully")
    except ImportError:
        logging.warning("Legacy LLM comparison module not available.")

# Import prompt evaluation module
try:
    from prompt_evaluation import evaluate_prompt
    PROMPT_EVALUATION_AVAILABLE = True
    print("\n\nPrompt evaluation module loaded successfully\n\n")
    logging.info("Prompt evaluation module loaded successfully")
except ImportError as e:
    print(f"\n\nPrompt evaluation module not available: {str(e)}. Falling back to basic evaluation.\n\n")
    logging.warning(f"Prompt evaluation module not available: {str(e)}. Falling back to basic evaluation.")
    PROMPT_EVALUATION_AVAILABLE = False

# Import role-specific tasks and visualization generators
from role_tasks import get_tasks_for_role, generate_role_progression_html
from all_role_tasks import get_all_role_tasks
from generate_org_chart import generate_org_chart_html

# Helper function to get a task by ID
def get_task_by_id(task_id, tasks_list):
    """
    Get a task by its ID from the tasks list.

    Args:
        task_id (str): The ID of the task to find
        tasks_list (list): The list of tasks to search

    Returns:
        dict or None: The task with the matching ID, or None if not found
    """
    for task in tasks_list:
        if task.get("id") == task_id:
            return task
    return None

# Configure logging
logging.basicConfig(level=logging.DEBUG, format='%(asctime)s - %(levelname)s - %(message)s', stream=sys.stdout)

# Create Flask app
app = Flask(__name__, static_url_path='', static_folder='.')

# Enable CORS for all routes
CORS(app)

# Game state
game_state = {
    "current_role": "applicant",
    "performance_score": 0,
    "challenges_completed": 0,
    "role_challenges_completed": 0,  # Track challenges completed in current role
    "messages": [],
    "game_completed": False,
    "current_manager": "hr",  # Track the current manager
    "current_task": None,     # Track the current task
    "completed_roles": []     # Track completed roles
}

# Characters
CHARACTERS = {
    # HR Department
    "hr": {
        "name": "Aisha Nambi",
        "title": "HR Manager",
        "avatar": "👩‍💼",
        "color": "#4a86e8"
    },
    # Team Lead (for junior_assistant)
    "manager": {
        "name": "Moses Kato",
        "title": "Team Lead",
        "avatar": "👨‍💼",
        "color": "#43a047"
    },
    # Marketing Department
    "vp_marketing": {
        "name": "Faith Nakato",
        "title": "VP of Marketing",
        "avatar": "👩‍💼",
        "color": "#e91e63"
    },
    # Operations Department
    "vp": {  # Keeping this for backward compatibility
        "name": "Hope Atim",
        "title": "VP of Operations",
        "avatar": "👩‍💼",
        "color": "#f57c00"
    },
    "vp_operations": {
        "name": "Hope Atim",
        "title": "VP of Operations",
        "avatar": "👩‍💼",
        "color": "#f57c00"
    },
    # Finance Department
    "vp_finance": {
        "name": "Joseph Okello",
        "title": "VP of Finance",
        "avatar": "👨‍💼",
        "color": "#009688"
    },
    # Executive Level
    "coo": {
        "name": "Daniel Wasswa",
        "title": "Chief Operating Officer",
        "avatar": "👨‍💼",
        "color": "#7b1fa2"
    },
    "ceo": {
        "name": "Joy Nantongo",
        "title": "Chief Executive Officer",
        "avatar": "👩‍💼",
        "color": "#c62828"
    },
    "board": {
        "name": "Board of Directors",
        "title": "Board of Directors",
        "avatar": "👥",
        "color": "#000000"
    },
    # System messages
    "system": {
        "name": "System",
        "title": "Game System",
        "avatar": "🖥️",
        "color": "#607d8b"
    },
    # HR Department - Director
    "hr_director": {
        "name": "Peace Akello",
        "title": "HR Director",
        "avatar": "👩‍💼",
        "color": "#6a1b9a" # Professional Purple
    }
}

# Role progression structure
ROLE_PROGRESSION = {
    # Entry Level
    "applicant": {
        "next_role": "junior_assistant",
        "challenges_required": 1,  # Applicant only needs 1 task (cover letter) to advance
        "promotion_message": "Congratulations! Your excellent performance has earned you a position as a Junior Assistant at Rwenzori Innovations Ltd. You're now officially part of the team!"
    },
    "junior_assistant": {
        "next_role": "sales_associate",
        "challenges_required": 3,  # All roles (except applicant) require exactly 3 tasks
        "promotion_message": "Great work! You've been promoted to Sales Associate in our Marketing department. Your communication skills will be valuable in this role!"
    },

    # Marketing Department
    "sales_associate": {
        "next_role": "marketing_associate",
        "challenges_required": 3,
        "promotion_message": "Your sales skills have impressed us! You're now being promoted to Marketing Associate where you'll help develop our marketing strategies."
    },
    "marketing_associate": {
        "next_role": "service_associate",
        "challenges_required": 3,
        "promotion_message": "Your marketing expertise has been noticed! You're now moving to Operations as a Service Associate to broaden your experience across departments."
    },
    # Operations Department - Associate Level
    "service_associate": {
        "next_role": "production_associate",
        "challenges_required": 3,
        "promotion_message": "Your customer service skills have been noticed! You're now moving to Production Associate to learn about our manufacturing processes."
    },
    "production_associate": {
        "next_role": "facilities_associate",
        "challenges_required": 3,
        "promotion_message": "Your attention to detail in production has earned you a position as Facilities Associate where you'll help maintain our operational infrastructure."
    },
    "facilities_associate": {
        "next_role": "accounts_receivable_associate",
        "challenges_required": 3,
        "promotion_message": "Your facilities management skills have impressed us! You're now moving to Finance as an Accounts Receivable Associate to broaden your experience."
    },
    # Finance Department - Associate Level
    "accounts_receivable_associate": {
        "next_role": "accounts_payable_associate",
        "challenges_required": 3,
        "promotion_message": "Your financial skills have been noticed! You're now moving to Accounts Payable Associate to expand your financial knowledge."
    },
    "accounts_payable_associate": {
        "next_role": "hr_coordinator",
        "challenges_required": 3,
        "promotion_message": "Your financial acumen is noted! Now, to broaden your corporate experience, you're moving into an HR Coordinator role. This will give you valuable insight into our people operations."
    },
    "hr_coordinator": {
        "next_role": "sales_manager",
        "challenges_required": 3,
        "promotion_message": "Your contributions as HR Coordinator have been excellent! You've shown a real talent for people-related tasks. Now, you're ready to take on the challenges of a Sales Manager in our Marketing department!"
    },
    # Management Level - Marketing Department
    "sales_manager": {
        "next_role": "advertising_manager",
        "challenges_required": 3,
        "promotion_message": "Excellent leadership! You've been promoted to Advertising Manager to oversee our advertising strategies."
    },
    "advertising_manager": {
        "next_role": "service_manager",
        "challenges_required": 3,
        "promotion_message": "Your success in advertising has earned you a promotion to Service Manager in our Operations department!"
    },
    # Management Level - Operations Department
    "service_manager": {
        "next_role": "production_manager",
        "challenges_required": 3,
        "promotion_message": "Your leadership in service has earned you a promotion to Production Manager. You'll now oversee our manufacturing operations!"
    },
    "production_manager": {
        "next_role": "facilities_manager",
        "challenges_required": 3,
        "promotion_message": "Excellent work managing production! You've been promoted to Facilities Manager to oversee all our facilities operations."
    },
    "facilities_manager": {
        "next_role": "accounts_receivable_manager",
        "challenges_required": 3,
        "promotion_message": "Your operational leadership has earned you a promotion to Accounts Receivable Manager in our Finance department!"
    },
    # Management Level - Finance Department
    "accounts_receivable_manager": {
        "next_role": "accounts_payable_manager",
        "challenges_required": 3,
        "promotion_message": "Your leadership in accounts receivable has earned you a promotion to Accounts Payable Manager."
    },
    "accounts_payable_manager": {
        "next_role": "hr_manager",
        "challenges_required": 3,
        "promotion_message": "Your leadership in finance has been outstanding. To further develop your executive capabilities, you're taking on the role of HR Manager. This strategic position will be key to shaping our company culture and talent strategy."
    },
    "hr_manager": {
        "next_role": "vp_marketing",
        "challenges_required": 3,
        "promotion_message": "Your strategic leadership as HR Manager has significantly impacted our organization! You're now ready to step into the role of Vice-President of Marketing. Congratulations on this major executive promotion!"
    },
    # VP Level
    "vp_marketing": {
        "next_role": "vp_operations",
        "challenges_required": 3,
        "promotion_message": "Your success as VP of Marketing has earned you a promotion to Vice-President of Operations!"
    },

    "vp_operations": {
        "next_role": "vp_finance",
        "challenges_required": 3,
        "promotion_message": "Your success as VP of Operations has earned you a promotion to Vice-President of Finance!"
    },
    "vp_finance": {
        "next_role": "coo",
        "challenges_required": 3,
        "promotion_message": "Your exceptional leadership across all departments has earned you a promotion to Chief Operating Officer (COO)!"
    },

    # Executive Level
    "coo": {
        "next_role": "ceo",
        "challenges_required": 3,
        "promotion_message": "Your outstanding performance as COO has impressed the board! You've been promoted to Chief Executive Officer (CEO)."
    },
    "ceo": {
        "next_role": "shareholders",
        "challenges_required": 3,
        "promotion_message": "After your successful tenure as CEO, you've been invited to join the Shareholders group at the very top of Rwenzori Innovations Ltd.!"
    },
    "shareholders": {
        "next_role": None,
        "challenges_required": 3,
        "promotion_message": "Congratulations! You've reached the pinnacle of Rwenzori Innovations Ltd. as a Shareholder. The company is thriving under your leadership and ownership!"
    }
}

# Tasks list - we'll use get_tasks_for_role function to get role-specific tasks
# Define all tasks that will be used in the game


# We're now importing get_tasks_for_role from role_tasks.py

# Context manager to get relevant context for the current task
def get_relevant_context(prompt):
    """
    Get the relevant context for the current task.
    This focuses on the current manager and task rather than the entire conversation history.
    """
    relevant_messages = []
    current_task = game_state["current_task"]
    current_manager = game_state["current_manager"]

    logging.info(f"Getting relevant context for task: {current_task} with manager: {current_manager}")

    # First, find the task introduction message from the current manager
    task_intro = None
    for message in reversed(game_state["messages"]):
        if message.get("is_challenge") and message.get("sender") == current_manager:
            task_intro = message
            break

    if task_intro:
        relevant_messages.append(task_intro)
        logging.info(f"Added task intro from {current_manager} to context")

    # Add any messages between the user and the current manager after the task intro
    if task_intro:
        task_intro_index = game_state["messages"].index(task_intro)
        for message in game_state["messages"][task_intro_index+1:]:
            if message.get("sender") in [current_manager, "user"]:
                relevant_messages.append(message)
                logging.info(f"Added message from {message.get('sender')} to context")

    # Add the current prompt
    relevant_messages.append({
        "sender": "user",
        "text": prompt
    })

    logging.info(f"Final context has {len(relevant_messages)} messages")
    return relevant_messages

# We're now importing generate_role_progression_html from role_tasks.py

# Routes
@app.route('/')
def index():
    return send_from_directory('.', 'context_aware_index.html')

@app.route('/new_index.html')
def new_index():
    return send_from_directory('.', 'new_index.html')

@app.route('/start_game')
def start_game():
    # Reset game state
    global game_state
    game_state = {
        "current_role": "applicant",
        "performance_score": 0,  # Start with 0 points
        "challenges_completed": 0,
        "role_challenges_completed": 0,
        "messages": [],
        "game_completed": False,
        "current_manager": "hr",
        "current_task": "cover_letter",
        "completed_roles": [],
        "first_task_pending": True  # Flag to indicate first task should be delayed
    }
    print(f"\n\nGame state initialized with performance_score: {game_state['performance_score']}\n\n")
    print(f"Game state: {game_state}")

    # Get the challenges required for the current role
    current_role = game_state["current_role"]
    role_info = ROLE_PROGRESSION.get(current_role, {})
    challenges_required = role_info.get("challenges_required", 1)

    # Add welcome message
    welcome_text = f'''## Welcome to Rwenzori Innovations! 🎉

I'm **{CHARACTERS['hr']['name']}**, the {CHARACTERS['hr']['title']}.

We're excited to have you join our application process. I'll be guiding you through a series of challenges to test your prompt engineering skills.'''
    welcome_html = markdown.markdown(welcome_text, extensions=['extra'])
    welcome_message = {
        "id": str(uuid.uuid4()),
        "sender": "hr",
        "text": welcome_text,
        "html": welcome_html,
        "timestamp": datetime.datetime.now().isoformat(),
        "is_challenge": False,
        "is_markdown": True
    }
    game_state["messages"].append(welcome_message)

    # Store first task info in game_state but don't add it to messages yet
    # It will be fetched by the client after a delay
    first_task = get_all_role_tasks("applicant")[0]
    game_state["pending_first_task"] = {
        "task": first_task,
        "id": str(uuid.uuid4())
    }
    print(f"First task prepared but not added yet: {first_task}")

    # Generate role progression visualization
    try:
        role_progression_html = generate_role_progression_html(
            game_state["current_role"],
            game_state["completed_roles"]
        )
        print("Generated role progression HTML for start_game")
    except Exception as e:
        print(f"Error generating role progression HTML in start_game: {str(e)}")
        # Create a simple fallback HTML
        role_progression_html = f'''
        <div class="role-progression">
            <h3>Career Progression</h3>
            <div class="current-role">Current Role: <strong>{game_state["current_role"].replace('_', ' ').title()}</strong></div>
        </div>
        '''
        print("Using fallback role progression HTML in start_game")

    # Generate organization chart
    try:
        # Make sure current_role is properly set
        current_role = game_state["current_role"]
        print(f"Generating org chart with current role: {current_role}")

        org_chart_html = generate_org_chart_html(
            current_role,
            game_state["completed_roles"]
        )
        print("Generated org chart HTML for start_game")
    except Exception as e:
        print(f"Error generating org chart HTML in start_game: {str(e)}")
        # Create a simple fallback HTML
        org_chart_html = f'''
        <div class="org-chart-content">
            <div class="org-level">
                <div class="level-title">Current Position</div>
                <div class="level-roles">
                    <div class="org-node current">
                        <div class="node-indicator">➤</div>
                        <div class="node-title">{game_state["current_role"].replace('_', ' ').title()}</div>
                    </div>
                </div>
            </div>
        </div>
        '''
        print("Using fallback org chart HTML in start_game")

    return jsonify({
        "status": "success",
        "messages": game_state["messages"],
        "current_role": game_state["current_role"],
        "performance_score": game_state["performance_score"],
        "characters": CHARACTERS,
        "current_manager": game_state["current_manager"],
        "current_task": game_state["current_task"],
        "challenges_required": challenges_required,
        "role_progression_html": role_progression_html,
        "org_chart_html": org_chart_html,
        "first_task_pending": game_state.get("first_task_pending", False)  # Flag to indicate first task should be delayed
    })

@app.route('/submit_prompt', methods=['POST'])
def submit_prompt():
    global game_state
    print("\n\n**** SUBMIT PROMPT ENDPOINT CALLED FROM CONTEXT_AWARE_GAME.PY ****\n\n")
    print(f"Game state: {game_state}")

    try:
        # Get request data
        data = request.json
        if not data:
            print("ERROR: No JSON data in request")
            return jsonify({"status": "error", "message": "No JSON data in request"})

        print(f"Request data: {data}")

        prompt = data.get('prompt', '')
        preview_only = data.get('preview_only', False)
        edited_response = data.get('edited_response')
        current_manager = data.get('current_manager', 'hr')
        current_task_id = data.get('current_task', 'cover_letter')

        print(f"\n\nPrompt: {prompt}\nPreview only: {preview_only}\nEdited response: {edited_response is not None}\nCurrent manager: {current_manager}\nCurrent task: {current_task_id}\n\n")

        # Validate input
        if not prompt:
            return jsonify({"status": "error", "message": "Prompt cannot be empty"})

        # Initialize game state if needed
        if not game_state:
            game_state = {
                "current_role": "applicant",
                "performance_score": 0,  # Start with 0 points
                "challenges_completed": 0,
                "role_challenges_completed": 0,
                "messages": [],
                "game_completed": False,
                "current_manager": current_manager,
                "current_task": current_task_id,
                "completed_roles": []
            }

        # Get the current task
        current_task = None

        # Get tasks for the current role using the new all_role_tasks module
        current_role = game_state["current_role"]
        role_tasks = get_all_role_tasks(current_role)

        # Find the current task in the role tasks
        for task in role_tasks:
            if task["id"] == current_task_id:
                current_task = task
                break

        # If task not found, use the first task for the role
        if not current_task:
            # Use the task based on role challenges completed
            task_index = min(game_state["role_challenges_completed"], len(role_tasks) - 1)
            current_task = role_tasks[task_index]
            game_state["current_task"] = current_task["id"]
            game_state["current_manager"] = current_task["manager"]

        logging.info(f"Current task: {current_task['id']}, Manager: {current_task['manager']}")

        # Get relevant context for the current task
        # This will be used in future versions for more advanced AI integration
        _ = get_relevant_context(prompt)

        # Determine whether to use LLM for response generation
        use_llm_response = LLM_RESPONSE_GENERATOR_AVAILABLE and os.environ.get("USE_LLM_RESPONSES", "false").lower() == "true"
        # Force use_llm_response to True for testing
        use_llm_response = True
        logging.info(f"Using LLM for response generation: {use_llm_response}")

        # Generate response using LLM or template
        if use_llm_response:
            try:
                # Generate response using LLM
                task_id = current_task["id"]
                logging.info(f"Attempting to generate response using LLM for task {task_id} with prompt: '{prompt[:50]}...'")
                # Print environment variables for debugging
                logging.info(f"USE_LLM_RESPONSES environment variable: {os.environ.get('USE_LLM_RESPONSES', 'not set')}")
                logging.info(f"LLM_RESPONSE_GENERATOR_AVAILABLE: {LLM_RESPONSE_GENERATOR_AVAILABLE}")

                # Call the LLM response generator
                ai_response = generate_response_with_llm(prompt, task_id)

                # Log the response
                logging.info(f"Successfully generated response using LLM for task {task_id}. Response preview: '{ai_response[:50]}...'")

                # Print the full response for debugging
                print("\n\nLLM RESPONSE:\n" + ai_response + "\n\n")

                # Make sure we're not using the template response
                if ai_response == current_task["response_template"]:
                    logging.warning("LLM response matches template response exactly. This might indicate the LLM response is not being used.")
                    # Force a different response to ensure we're not using the template
                    ai_response = "This is a forced response to ensure we're not using the template. " + ai_response
            except Exception as e:
                # Fall back to template if LLM fails
                logging.error(f"Error generating response with LLM: {str(e)}")
                ai_response = current_task["response_template"]
                logging.info(f"Falling back to template response for task {current_task['id']}")
        else:
            # Use template response
            logging.info(f"Using template response for task {current_task['id']} (LLM response generation is disabled)")
            ai_response = current_task["response_template"]

        # Convert markdown to HTML
        html_response = markdown.markdown(ai_response, extensions=['extra'])

        # If preview only, redirect to the preview_response endpoint
        if preview_only:
            return redirect(url_for('preview_response', prompt=prompt, task_id=current_task["id"]))

        # Add user message
        user_html = markdown.markdown(prompt, extensions=['extra'])
        user_message = {
            "id": str(uuid.uuid4()),
            "sender": "user",
            "text": prompt,
            "html": user_html,
            "timestamp": datetime.datetime.now().isoformat(),
            "is_challenge": False,
            "is_markdown": True
        }
        game_state["messages"].append(user_message)

        # Add AI response
        final_response = edited_response or ai_response

        # Convert markdown to HTML if edited_response is provided
        if edited_response:
            html_response = markdown.markdown(edited_response, extensions=['extra'])

        ai_message = {
            "id": str(uuid.uuid4()),
            "sender": "ai",
            "text": final_response,
            "html": html_response,
            "timestamp": datetime.datetime.now().isoformat(),
            "is_challenge": False,
            "is_markdown": True
        }
        game_state["messages"].append(ai_message)

        # --- Primary Evaluation using prompt_evaluation.py (LLM-based only) ---
        evaluation_data = None
        prompt_dimensions_data = {} # For UI display of detailed feedback
        grade = "bad" # Default in case of full failure
        similarity_score = 0 # Default in case of full failure (0-100 scale)
        feedback_details = ["Error: Evaluation could not be performed."] # Default
        # Default evaluation_results for process_task_completion
        evaluation_results = {"overall_score": 0, "meets_requirements": False}

        try:
            logging.info(f"Attempting LLM-based evaluation with prompt_evaluation.py for task {current_task['id']}")
            evaluation_data = evaluate_prompt(prompt, current_task["id"])
            logging.info(f"prompt_evaluation.py result: {evaluation_data}")

            if evaluation_data and evaluation_data.get("status") == "success":
                grade = evaluation_data.get("grade", "bad")
                similarity_score = evaluation_data.get("overall_score", 0) # This is 0-100
                feedback_details = evaluation_data.get("feedback_details", ["Evaluation successful but no details provided."])
                prompt_dimensions_data = evaluation_data.get("dimensions", {})

                # Keep the original similarity_score (0-100 scale) for display
                # Don't divide by 10 for display purposes
                current_overall_score_for_display = similarity_score

                evaluation_results = {
                    "overall_score": similarity_score / 10 if similarity_score > 10 else similarity_score, # Scaled 0-10 score for internal use
                    "meets_requirements": evaluation_data.get("meets_requirements", False)
                }
                logging.info(f"Processed evaluation data: grade={grade}, similarity_score={similarity_score}, meets_requirements={evaluation_results['meets_requirements']}")
            else:
                # Handles case where evaluate_prompt might return a non-success status or unexpected structure
                logging.error(f"Prompt evaluation was called but did not return a successful status or valid data. Data: {evaluation_data}")
                # Keep default failure values set above (Option A fallback)
                feedback_details = ["Error: Detailed evaluation is currently unavailable. Please try again later."]
                # Log the error but don't add a system message to the UI
                logging.info("Evaluation issue occurred but continuing with basic evaluation")

        except Exception as e:
            logging.error(f"LLM-based evaluation (prompt_evaluation.py) failed critically: {str(e)}")
            # Use Option A fallback strategy - hard fail with basic score
            grade = "bad"
            similarity_score = 0
            feedback_details = ["Error: Detailed evaluation is currently unavailable due to a system issue. Please try again later."]
            prompt_dimensions_data = {}
            evaluation_results = {"overall_score": 0, "meets_requirements": False}

            # Log the error but don't add a system message to the UI
            logging.info("Evaluation service error occurred but continuing with basic evaluation")

        # --- End of Primary Evaluation ---

        # Determine feedback_grade for manager's textual feedback
        feedback_grade = "good" if similarity_score >= 90 else grade # Uses grade from new evaluation

        # Calculate performance_score (0-10 scale) from similarity_score (0-100 scale)
        # This is used in multiple places throughout the code
        performance_score = similarity_score / 10 if similarity_score > 10 else similarity_score

        # Generate personalized feedback using LLM if available
        if LLM_FEEDBACK_GENERATOR_AVAILABLE and os.environ.get("USE_LLM_FEEDBACK", "true").lower() == "true":
            try:
                # Prepare performance metrics for feedback generation
                performance_score_for_feedback = performance_score

                logging.info(f"Preparing personalized feedback. Similarity score: {similarity_score}, Grade for feedback: {feedback_grade}, Performance score for feedback: {performance_score_for_feedback}")

                performance_metrics = {
                    "overall_score": performance_score_for_feedback,
                    "grade": feedback_grade, # Use the feedback_grade
                    "feedback_details": feedback_details # Use details from new evaluation
                }

                manager_character = CHARACTERS.get(current_task["manager"], {})
                manager_name = manager_character.get("name", current_task["manager"])

                formatted_performance_score = f"{performance_score_for_feedback:.1f}"
                performance_metrics["formatted_score"] = formatted_performance_score

                feedback_text = generate_manager_feedback(
                    prompt,
                    current_task["id"],
                    manager_name,
                    performance_metrics
                )
                logging.info(f"Generated personalized feedback for task {current_task['id']}")
            except Exception as e:
                logging.error(f"Error generating personalized feedback: {str(e)}")
                feedback_text = current_task["feedback"].get(feedback_grade, "Could not generate personalized feedback.")
        else:
            # Use template feedback
            feedback_text = current_task["feedback"].get(feedback_grade, "Feedback based on your performance.")

        # Convert feedback to HTML and create message
        feedback_html = markdown.markdown(feedback_text, extensions=['extra'])
        feedback_message = {
            "id": str(uuid.uuid4()),
            "sender": current_task["manager"],
            "text": feedback_text,
            "html": feedback_html,
            "timestamp": datetime.datetime.now().isoformat(),
            "is_challenge": False,
            "is_markdown": True
        }
        game_state["messages"].append(feedback_message)

        # Calculate points earned based on the grade from the new evaluation system
        points_earned = 10 if grade == "good" else (5 if grade == "okay" else 0)
        logging.info(f"Points earned from new evaluation system: {points_earned} (grade: {grade})")

        # No special handling for cover_letter task - it's evaluated like all other tasks

        # Update the performance score
        game_state["performance_score"] = game_state.get("performance_score", 0) + points_earned
        logging.info(f"Updated performance score: {game_state['performance_score']}")

        # Process task completion or failure based on evaluation
        # Always consider the task as completed for simplicity and better game flow
        # This ensures the player can progress through the game

        # Create a basic evaluation result if not provided
        if not evaluation_results:
            # Calculate overall score based on grade
            overall_score = 75 if grade == "good" else (50 if grade == "okay" else 30)

            evaluation_results = {
                "overall_score": overall_score,
                "meets_requirements": grade == "good" or (grade == "okay" and overall_score >= 50)
            }
        else:
            # Don't override meets_requirements if it's already set
            if "meets_requirements" not in evaluation_results:
                # Get the overall score
                overall_score = evaluation_results.get("overall_score", 0)

                # BUGFIX: Special handling for Michael Rodriguez's tasks
                if current_task["manager"] == "manager" and CHARACTERS["manager"]["name"] == "Michael Rodriguez":
                    # For Michael's tasks, convert the overall score to 0-10 scale if needed
                    if overall_score > 10:
                        normalized_score = overall_score / 10
                    else:
                        normalized_score = overall_score

                    print(f"\n\nMichael Rodriguez task: Normalized score for meets_requirements check: {normalized_score}\n\n")
                    logging.info(f"Michael Rodriguez task: Normalized score for meets_requirements check: {normalized_score}")

                    # Use the normalized score (0-10 scale) to determine if the task meets requirements
                    evaluation_results["meets_requirements"] = (
                        grade == "good" or
                        (grade == "okay" and normalized_score >= 5) or
                        normalized_score >= 7 or
                        (normalized_score >= 6)  # Pass if score is 6.0 or higher (on 0-10 scale)
                    )
                else:
                    # For other tasks, use the original logic
                    evaluation_results["meets_requirements"] = (
                        grade == "good" or
                        (grade == "okay" and overall_score >= 50) or
                        overall_score >= 70
                    )

        # No special handling for cover letter task - it's evaluated like all other tasks

        # Process task completion or failure based on evaluation results
        if evaluation_results.get("meets_requirements", False):
            # Task completed successfully
            # BUGFIX: Ensure we're incrementing the role_challenges_completed counter
            game_state["role_challenges_completed"] = game_state.get("role_challenges_completed", 0) + 1
            game_state["challenges_completed"] = game_state.get("challenges_completed", 0) + 1

            # Log the updated counters
            logging.info(f"Task completed successfully. Updated counters: challenges_completed={game_state['challenges_completed']}, role_challenges_completed={game_state['role_challenges_completed']}")

            # Process task completion
            game_state = process_task_completion(game_state, evaluation_results, points_earned)
            logging.info("Task completed successfully")
        else:
            # Task failed
            game_state = process_task_failure(game_state)
            logging.info("Task failed, not advancing to next task")

            # Get the challenges required for the current role
            current_role = game_state["current_role"]
            role_info = ROLE_PROGRESSION.get(current_role, {})
            task_challenges_required = role_info.get("challenges_required", 3)

            # Generate role progression visualization for failure case
            try:
                task_role_progression_html = generate_role_progression_html(
                    game_state["current_role"],
                    game_state["completed_roles"]
                )
                print("Generated role progression HTML for task failure")
            except Exception as e:
                print(f"Error generating role progression HTML in task failure: {str(e)}")
                # Create a simple fallback HTML
                task_role_progression_html = f'''
                <div class="role-progression">
                    <h3>Career Progression</h3>
                    <div class="current-role">Current Role: <strong>{game_state["current_role"].replace('_', ' ').title()}</strong></div>
                </div>
                '''
                print("Using fallback role progression HTML in task failure")

            # Generate organization chart for failure case
            task_org_chart_html = generate_org_chart_html(
                game_state["current_role"],
                game_state["completed_roles"]
            )

            # BUGFIX: Ensure we're including the correct performance score in the failure response
            # For Michael Rodriguez's tasks, make sure we're using the converted performance score
            if current_task["manager"] == "manager" and CHARACTERS["manager"]["name"] == "Michael Rodriguez" and 'performance_score' in locals():
                # Use the converted performance score
                overall_performance_score = performance_score
                print(f"\n\nDEBUG - FAILURE CASE: Using converted performance score for Michael Rodriguez task failure: {overall_performance_score}\n\n")
                logging.info(f"DEBUG - FAILURE CASE: Using converted performance score for Michael Rodriguez task failure: {overall_performance_score}")

                # CRITICAL BUGFIX: Make sure the similarity score is consistent with the performance score
                # If we're using a 0-10 scale for performance, make sure similarity is on 0-100 scale
                if similarity_score <= 10:
                    similarity_score = performance_score * 10
                    print(f"\n\nDEBUG - FAILURE CASE: Michael Rodriguez task failure: Updated similarity score to {similarity_score} for consistency\n\n")
                    logging.info(f"DEBUG - FAILURE CASE: Michael Rodriguez task failure: Updated similarity score to {similarity_score} for consistency")
            else:
                # Use the original similarity_score for display (0-100 scale)
                # Don't divide by 10 for display purposes
                overall_performance_score = similarity_score
                print(f"\n\nDEBUG - FAILURE CASE: Using original similarity_score for display: similarity_score={similarity_score}, overall_performance_score={overall_performance_score}\n\n")
                logging.info(f"DEBUG - FAILURE CASE: Using original similarity_score for display: similarity_score={similarity_score}, overall_performance_score={overall_performance_score}")

            # Include meets_requirements in response data
            # BUGFIX: Always include the manager_name in the response
            manager_name = CHARACTERS[current_task["manager"]]["name"] if current_task["manager"] in CHARACTERS else "Unknown"

            response_data = {
                "status": "success",
                "messages": game_state["messages"],
                "current_role": game_state["current_role"],
                "performance_score": game_state["performance_score"],
                "promoted": False,
                "game_completed": game_state["game_completed"],
                "grade": grade,
                "current_manager": game_state["current_manager"],
                "manager_name": manager_name,  # Always include the manager name
                "current_task": game_state["current_task"],  # Keep the same task
                "role_challenges_completed": game_state["role_challenges_completed"],
                "total_challenges_completed": game_state["challenges_completed"],
                "completed_roles": game_state["completed_roles"],
                "challenges_required": task_challenges_required,
                "similarity_score": similarity_score if 'similarity_score' in locals() else 30,
                "overall_score": overall_performance_score,  # Add the properly converted performance score
                "feedback_details": feedback_details if 'feedback_details' in locals() else ["Length: " + str(len(prompt)) + " characters"],
                "role_progression_html": task_role_progression_html,
                "org_chart_html": task_org_chart_html,
                "meets_requirements": False,
                # Add debug information
                "debug_info": {
                    "is_michael_rodriguez": current_task["manager"] == "manager" and CHARACTERS["manager"]["name"] == "Michael Rodriguez",
                    "manager": current_task["manager"],
                    "manager_name": manager_name,
                    "task_id": current_task["id"],
                    "original_similarity_score": similarity_score,
                    "performance_score_in_locals": 'performance_score' in locals(),
                    "performance_score_value": performance_score if 'performance_score' in locals() else "Not available",
                    "overall_performance_score": overall_performance_score
                }
            }

            # Log the response data for debugging
            print(f"\n\nDEBUG - FAILURE CASE: Response data being sent to frontend: {response_data}\n\n")
            logging.info(f"DEBUG - FAILURE CASE: Response data being sent to frontend: similarity_score={response_data['similarity_score']}, overall_score={response_data['overall_score']}")

            # Add default section scores for failure case
            response_data["section_scores"] = [
                {
                    "section": "Overall Quality",
                    "score": 3,
                    "feedback": "The submission does not meet the requirements for this task."
                }
            ]

            # Return early with failure response
            return jsonify(response_data)

        # Determine next task based on role challenges completed
        # Use the new all_role_tasks module to get tasks for the current role
        role_tasks = get_all_role_tasks(game_state["current_role"])

        # BUGFIX: Use role_challenges_completed as the index for the next task
        # This ensures proper task progression alignment
        next_task_index = game_state["role_challenges_completed"]

        print(f"\n\nDetermining next task: role={game_state['current_role']}, role_challenges_completed={game_state['role_challenges_completed']}, next_task_index={next_task_index}, available_tasks={len(role_tasks)}\n\n")

        # Check if there are more tasks available for this role
        if next_task_index < len(role_tasks):
            next_task = role_tasks[next_task_index]
            game_state["current_task"] = next_task["id"]
            game_state["current_manager"] = next_task["manager"]

            print(f"\n\nNext task selected: id={next_task['id']}, manager={next_task['manager']}\n\n")

            # Add next challenge with a 5-second delay
            # Convert markdown to HTML for the description
            next_task_html = markdown.markdown(next_task["description"], extensions=['extra'])
            # Create a timestamp 5 seconds in the future
            delayed_timestamp = (datetime.datetime.now() + datetime.timedelta(seconds=5)).isoformat()
            next_challenge = {
                "id": str(uuid.uuid4()),
                "sender": next_task["manager"],
                "text": next_task["description"],
                "html": next_task_html,
                "timestamp": delayed_timestamp,
                "is_challenge": True,
                "task_id": next_task["id"],
                "is_markdown": True
            }
            game_state["messages"].append(next_challenge)
            print(f"\n\nAdded next task challenge from {next_task['manager']} with 5-second delay\n\n")
        else:
            # No more tasks available for this role
            # Check if there's a next role to promote to
            current_role = game_state["current_role"]
            role_info = ROLE_PROGRESSION.get(current_role, {})
            next_role = role_info.get("next_role")

            print(f"\n\nNo more tasks for role {current_role}. Next role: {next_role}\n\n")
            logging.info(f"No more tasks for role {current_role}. Next role: {next_role}")

            if next_role:
                # Promote to next role
                logging.info(f"Promoting from {current_role} to {next_role}")

                # Add current role to completed roles if not already there
                if "completed_roles" not in game_state:
                    game_state["completed_roles"] = []

                if current_role not in game_state["completed_roles"]:
                    game_state["completed_roles"].append(current_role)
                    print(f"\n\nAdded {current_role} to completed roles: {game_state['completed_roles']}\n\n")

                # Update game state with new role
                game_state["current_role"] = next_role
                game_state["role_challenges_completed"] = 0
                game_state["role_average_score"] = 0  # Reset for new role

                print(f"\n\nPromoted to new role: {next_role}\n\n")

                # Get the first task for the new role using the new all_role_tasks module
                new_role_tasks = get_all_role_tasks(next_role)
                if new_role_tasks:
                    next_task = new_role_tasks[0]
                    game_state["current_task"] = next_task["id"]
                    game_state["current_manager"] = next_task["manager"]

                    # Add promotion message
                    promotion_message = role_info.get("promotion_message", f"Congratulations! You've been promoted to {next_role}!")
                    promotion_html = markdown.markdown(f"## {promotion_message}", extensions=['extra'])
                    promotion_msg = {
                        "id": str(uuid.uuid4()),
                        "sender": game_state["current_manager"],
                        "text": f"## {promotion_message}",
                        "html": promotion_html,
                        "timestamp": datetime.datetime.now().isoformat(),
                        "is_challenge": False,
                        "is_markdown": True,
                        "is_promotion": True  # Mark as a promotion message
                    }

                    # Store the next task info but don't add it yet
                    # It will be fetched by the client after a delay
                    game_state["next_task_pending"] = True
                    game_state["pending_next_task"] = {
                        "task": next_task,
                        "id": str(uuid.uuid4())
                    }
                    game_state["messages"].append(promotion_msg)

                    # We don't add the next task message here anymore
                    # It will be fetched by the client after a delay
                else:
                    # No tasks for the new role (shouldn't happen, but just in case)
                    logging.error(f"No tasks found for role {next_role}")
                    game_state["game_completed"] = True
                    game_state["current_task"] = None
            else:
                # No next role, game completed
                logging.info(f"No next role after {current_role}. Game completed!")
                game_state["game_completed"] = True
                game_state["current_task"] = None

                # Add completion message if game is completed
                if game_state["game_completed"]:
                    completion_text = '''## Congratulations! 🎉

You've completed all the challenges and proven your prompt engineering skills! You've successfully progressed through all departments of Rwenzori Innovations:

### Entry Level
- Started as an **Applicant** in HR
- Became a **Junior Assistant** in HR

### Marketing Department
- Advanced to **Sales Associate** in Marketing
- Promoted to **Marketing Associate** in Marketing
- Became a **Sales Manager** in Marketing
- Moved up to **Advertising/Research Manager** in Marketing
- Reached **Vice-President of Marketing**

### Operations Department
- Transferred to **Service Associate** in Operations
- Moved to **Production Associate** in Operations
- Advanced to **Facilities Associate** in Operations
- Promoted to **Service Manager** in Operations
- Became a **Production Manager** in Operations
- Moved up to **Facilities Manager** in Operations
- Reached **Vice-President of Operations**

### Finance Department
- Transferred to **Accounts Receivable Associate** in Finance
- Moved to **Accounts Payable Associate** in Finance
- Promoted to **Accounts Receivable Manager** in Finance
- Advanced to **Accounts Payable Manager** in Finance
- Reached **Vice-President of Finance**

### HR Department
- Transferred to **HR Coordinator** in Human Resources
- Promoted to **HR Manager** in Human Resources
- Advanced to **HR Director** in Human Resources

### Executive Level
- Promoted to **Chief Operating Officer (COO)**
- Advanced to **Chief Executive Officer (CEO)**
- And finally joined the **Shareholders** at the very top of Rwenzori Innovations!

Thank you for demonstrating your exceptional abilities in crafting effective prompts and managing AI interactions across all levels of the organization.'''
                    completion_html = markdown.markdown(completion_text, extensions=['extra'])
                    completion_message = {
                        "id": str(uuid.uuid4()),
                        "sender": "ceo",
                        "text": completion_text,
                        "html": completion_html,
                        "timestamp": datetime.datetime.now().isoformat(),
                        "is_challenge": False,
                        "is_markdown": True
                    }
                    game_state["messages"].append(completion_message)

        # Get the challenges required for the current role
        current_role = game_state["current_role"]
        role_info = ROLE_PROGRESSION.get(current_role, {})
        challenges_required = role_info.get("challenges_required", 3)

        # Generate role progression visualization
        try:
            role_progression_html = generate_role_progression_html(
                game_state["current_role"],
                game_state["completed_roles"]
            )

            # Print the role progression HTML for debugging
            print("\n\nRole progression HTML generated:")
            print(role_progression_html[:200] + "..." if len(role_progression_html) > 200 else role_progression_html)
            print("\n\n")
        except Exception as e:
            print(f"Error generating role progression HTML: {str(e)}")
            # Create a simple fallback HTML
            role_progression_html = f'''
            <div class="role-progression">
                <h3>Career Progression</h3>
                <div class="current-role">Current Role: <strong>{game_state["current_role"].replace('_', ' ').title()}</strong></div>
            </div>
            '''
            print("Using fallback role progression HTML")

        # Debug game state before generating org chart
        logging.debug(f"Game state before generating org chart: current_role={game_state['current_role']}, completed_roles={game_state.get('completed_roles', [])}")

        # Generate organization chart
        org_chart_html = generate_org_chart_html(
            game_state["current_role"],
            game_state.get("completed_roles", [])
        )

        # Print the organization chart HTML for debugging
        print("\n\nOrganization chart HTML generated:")
        print(org_chart_html[:200] + "..." if len(org_chart_html) > 200 else org_chart_html)
        print("\n\n")

        # Debug the next_task_pending flag
        print(f"\n\nDEBUG - next_task_pending: {game_state.get('next_task_pending', False)}")
        if game_state.get('next_task_pending', False):
            print(f"DEBUG - pending_next_task: {game_state.get('pending_next_task')}")
        print("\n\n")

        # Check if the player was just promoted in this task submission
        # This should only be true when they've just moved to a new role
        promoted = False

        # We need to determine if this task submission resulted in a promotion
        # A promotion happens when:
        # 1. The player has just completed all tasks for their current role
        # 2. They've been moved to a new role
        # 3. Their previous role has been added to completed_roles

        # Check if we're at the end of tasks for this role
        if next_task_index >= len(role_tasks):
            # This means we've completed all tasks for the current role

            # Check if we've just been promoted (role_challenges_completed was reset to 0)
            if game_state["role_challenges_completed"] == 0 and "completed_roles" in game_state and game_state["completed_roles"]:
                # This is a true promotion - we've just moved to a new role
                promoted = True
                print(f"\n\nPLAYER PROMOTION: Player was just promoted to {game_state['current_role']}\n\n")
            else:
                promoted = False
                print(f"\n\nNO PROMOTION: Player completed a task but was not promoted\n\n")
        else:
            # We still have more tasks in the current role
            promoted = False
            print(f"\n\nNO PROMOTION: Player completed a task but has more tasks in current role\n\n")

        print(f"\n\nPromoted flag set to: {promoted}\n\n")

        # Add debug info to the org chart HTML
        org_chart_html += f'''
        <!--
        DEBUG INFO:
        current_role: {game_state["current_role"]}
        completed_roles: {game_state.get("completed_roles", [])}
        role_challenges_completed: {game_state.get("role_challenges_completed", 0)}
        challenges_completed: {game_state.get("challenges_completed", 0)}
        promoted: {promoted}
        -->
        '''

        # BUGFIX: Ensure we're including the correct performance score in the response
        # For Michael Rodriguez's tasks, make sure we're using the converted performance score
        if current_task["manager"] == "manager" and CHARACTERS["manager"]["name"] == "Michael Rodriguez" and 'performance_score' in locals():
            # Use the converted performance score
            overall_performance_score = performance_score
            print(f"\n\nUsing converted performance score for Michael Rodriguez task: {overall_performance_score}\n\n")
            logging.info(f"Using converted performance score for Michael Rodriguez task: {overall_performance_score}")

            # CRITICAL BUGFIX: Make sure the similarity score is consistent with the performance score
            # If we're using a 0-10 scale for performance, make sure similarity is on 0-100 scale
            if similarity_score <= 10:
                similarity_score = performance_score * 10
                print(f"\n\nMichael Rodriguez task: Updated similarity score to {similarity_score} for consistency\n\n")
                logging.info(f"Michael Rodriguez task: Updated similarity score to {similarity_score} for consistency")
        else:
            # Use the default performance score
            # BUGFIX: Don't divide by 10 if similarity_score is already on 0-10 scale
            overall_performance_score = similarity_score / 10 if similarity_score > 10 else similarity_score

        # BUGFIX: Always include the manager_name in the response
        manager_name = CHARACTERS[game_state["current_manager"]]["name"] if game_state["current_manager"] in CHARACTERS else "Unknown"

        response_data = {
            "status": "success",
            "messages": game_state["messages"],
            "current_role": game_state["current_role"],
            "performance_score": game_state["performance_score"],
            "promoted": promoted,
            "is_promotion": promoted,  # Add a clearer flag for the frontend
            "just_completed_task": True,  # Flag to indicate a task was just completed
            "game_completed": game_state["game_completed"],
            "grade": grade,
            "current_manager": game_state["current_manager"],
            "manager_name": manager_name,  # Always include the manager name
            "current_task": game_state["current_task"],
            "role_challenges_completed": game_state["role_challenges_completed"],
            "total_challenges_completed": game_state["challenges_completed"],
            "completed_roles": game_state["completed_roles"],
            "next_task_pending": game_state.get("next_task_pending", False),  # Add next_task_pending flag
            "challenges_required": challenges_required,
            "similarity_score": similarity_score,
            "overall_score": overall_performance_score,  # Add the properly converted performance score
            "feedback_details": feedback_details if 'feedback_details' in locals() else ["Length: " + str(len(prompt)) + " characters"],
            "role_progression_html": role_progression_html,
            "org_chart_html": org_chart_html,
            "meets_requirements": evaluation_results.get("meets_requirements", True),
            # Add debug information
            "debug_info": {
                "is_michael_rodriguez": game_state["current_manager"] == "manager" and CHARACTERS["manager"]["name"] == "Michael Rodriguez",
                "manager": game_state["current_manager"],
                "manager_name": manager_name,
                "task_id": game_state["current_task"],
                "original_similarity_score": similarity_score,
                "performance_score_in_locals": 'performance_score' in locals(),
                "performance_score_value": performance_score if 'performance_score' in locals() else "Not available",
                "overall_performance_score": overall_performance_score,
                "promotion_status": "PROMOTED" if promoted else "NOT_PROMOTED",
                "next_task_index": next_task_index,
                "role_tasks_length": len(role_tasks),
                "role_challenges_completed": game_state["role_challenges_completed"]
            }
        }

        # Add default section scores
        response_data["section_scores"] = [
            {
                "section": "Overall Quality",
                "score": int(performance_score),
                "feedback": "Evaluation based on overall prompt quality."
            }
        ]

        # Return the response
        return jsonify(response_data)

    except Exception as e:
        print(f"CRITICAL ERROR in submit_prompt: {str(e)}")
        # Return a simple error response
        return jsonify({"status": "error", "message": f"Critical error in submit_prompt: {str(e)}"})

@app.route('/fetch_first_task', methods=['GET'])
def fetch_first_task():
    """Endpoint to fetch the first task after a delay"""
    global game_state

    # Check if there's a pending first task
    if not game_state.get("first_task_pending", False) or "pending_first_task" not in game_state:
        return jsonify({
            "status": "error",
            "message": "No pending first task"
        })

    # Get the pending first task
    pending_task = game_state["pending_first_task"]
    first_task = pending_task["task"]
    task_id = pending_task["id"]

    # Convert markdown to HTML for the description
    first_task_html = markdown.markdown(first_task["description"], extensions=['extra'])
    first_challenge = {
        "id": task_id,
        "sender": first_task["manager"],
        "text": first_task["description"],
        "html": first_task_html,
        "timestamp": datetime.datetime.now().isoformat(),
        "is_challenge": True,
        "task_id": first_task["id"],
        "is_markdown": True
    }

    # Add the first task to the game state
    game_state["messages"].append(first_challenge)
    game_state["first_task_pending"] = False
    del game_state["pending_first_task"]

    print(f"First task added: {first_challenge}")

    return jsonify({
        "status": "success",
        "message": first_challenge
    })

@app.route('/fetch_next_task', methods=['GET'])
def fetch_next_task():
    """Endpoint to fetch the next task after a promotion"""
    global game_state

    # Check if there's a pending next task
    if not game_state.get("next_task_pending", False) or "pending_next_task" not in game_state:
        return jsonify({
            "status": "error",
            "message": "No pending next task"
        })

    # Get the pending next task
    pending_task = game_state["pending_next_task"]
    next_task = pending_task["task"]
    task_id = pending_task["id"]

    # Convert markdown to HTML for the description
    next_task_html = markdown.markdown(next_task["description"], extensions=['extra'])
    next_challenge = {
        "id": task_id,
        "sender": next_task["manager"],
        "text": next_task["description"],
        "html": next_task_html,
        "timestamp": datetime.datetime.now().isoformat(),
        "is_challenge": True,
        "task_id": next_task["id"],
        "is_markdown": True
    }

    # Add the next task to the game state
    game_state["messages"].append(next_challenge)
    game_state["next_task_pending"] = False
    del game_state["pending_next_task"]

    print(f"Next task after promotion added: {next_challenge}")

    return jsonify({
        "status": "success",
        "message": next_challenge
    })

@app.route('/get_game_state', methods=['GET'])
def get_game_state():
    global game_state

    # Debug log the request
    logging.debug(f"GET /get_game_state called - Current game state: {game_state}")

    # Initialize game state if needed
    if not game_state:
        game_state = {
            "current_role": "applicant",
            "performance_score": 5,  # Start with 5 points to ensure it's working
            "challenges_completed": 0,
            "role_challenges_completed": 0,
            "messages": [],
            "game_completed": False,
            "current_manager": "hr",
            "current_task": "cover_letter",
            "completed_roles": []
        }
        logging.info(f"Game state initialized in get_game_state with performance_score: {game_state['performance_score']}")
        print(f"\n\nGame state initialized in get_game_state with performance_score: {game_state['performance_score']}\n\n")

    # Get the challenges required for the current role
    current_role = game_state["current_role"]
    role_info = ROLE_PROGRESSION.get(current_role, {})
    challenges_required = role_info.get("challenges_required", 3)

    # Debug log the current role and completed roles
    logging.debug(f"Current role: {current_role}, Completed roles: {game_state.get('completed_roles', [])}")
    print(f"\n\nDEBUG - Current role: {current_role}, Completed roles: {game_state.get('completed_roles', [])}\n\n")

    # Generate role progression visualization
    try:
        role_progression_html = generate_role_progression_html(
            game_state["current_role"],
            game_state["completed_roles"]
        )
        logging.debug("Generated role progression HTML for get_game_state")
        print("Generated role progression HTML for get_game_state")
    except Exception as e:
        logging.error(f"Error generating role progression HTML in get_game_state: {str(e)}")
        print(f"Error generating role progression HTML in get_game_state: {str(e)}")
        # Create a simple fallback HTML
        role_progression_html = f'''
        <div class="role-progression">
            <h3>Career Progression</h3>
            <div class="current-role">Current Role: <strong>{game_state["current_role"].replace('_', ' ').title()}</strong></div>
        </div>
        '''
        logging.debug("Using fallback role progression HTML in get_game_state")
        print("Using fallback role progression HTML in get_game_state")

    # Print the role progression HTML for debugging
    logging.debug(f"Role progression HTML generated in get_game_state: {role_progression_html[:200]}...")
    print("\n\nRole progression HTML generated in get_game_state:")
    print(role_progression_html[:200] + "..." if len(role_progression_html) > 200 else role_progression_html)
    print("\n\n")

    # Generate organization chart
    try:
        logging.debug(f"Generating org chart with current_role={current_role}, completed_roles={game_state.get('completed_roles', [])}")
        org_chart_html = generate_org_chart_html(
            game_state["current_role"],
            game_state["completed_roles"]
        )

        # Add debug info to the org chart HTML
        org_chart_html += f'''
        <!--
        DEBUG INFO (get_game_state):
        current_role: {game_state["current_role"]}
        completed_roles: {game_state.get("completed_roles", [])}
        role_challenges_completed: {game_state.get("role_challenges_completed", 0)}
        challenges_completed: {game_state.get("challenges_completed", 0)}
        -->
        '''
    except Exception as e:
        logging.error(f"Error generating org chart HTML in get_game_state: {str(e)}")
        print(f"Error generating org chart HTML in get_game_state: {str(e)}")
        # Create a simple fallback HTML
        org_chart_html = f'''
        <div class="org-chart-content">
            <div class="org-level">
                <div class="level-title">Current Position</div>
                <div class="level-roles">
                    <div class="org-node current">
                        <div class="node-title"><strong>{game_state["current_role"].replace('_', ' ').title()}</strong></div>
                        <div class="current-marker">YOU ARE HERE</div>
                    </div>
                </div>
            </div>
        </div>
        <!-- DEBUG INFO: Error generating proper org chart -->
        '''

    # Print the organization chart HTML for debugging
    logging.debug(f"Organization chart HTML generated in get_game_state: {org_chart_html[:200]}...")
    print("\n\nOrganization chart HTML generated in get_game_state:")
    print(org_chart_html[:200] + "..." if len(org_chart_html) > 200 else org_chart_html)
    print("\n\n")

    # BUGFIX: Always include the manager_name in the response
    manager_name = CHARACTERS[game_state["current_manager"]]["name"] if game_state["current_manager"] in CHARACTERS else "Unknown"
    logging.debug(f"Manager name: {manager_name}")

    # Create response with debug info
    response_data = {
        "status": "success",
        "messages": game_state["messages"],
        "current_role": game_state["current_role"],
        "performance_score": game_state["performance_score"],
        "characters": CHARACTERS,
        "game_completed": game_state["game_completed"],
        "current_manager": game_state["current_manager"],
        "manager_name": manager_name,  # Always include the manager name
        "current_task": game_state["current_task"],
        "role_challenges_completed": game_state["role_challenges_completed"],
        "total_challenges_completed": game_state["challenges_completed"],
        "completed_roles": game_state["completed_roles"],
        "challenges_required": challenges_required,
        "role_progression": ROLE_PROGRESSION,
        "role_progression_html": role_progression_html,
        "org_chart_html": org_chart_html,
        "first_task_pending": game_state.get("first_task_pending", False),  # Flag to indicate first task should be delayed
        "next_task_pending": game_state.get("next_task_pending", False),  # Flag to indicate next task after promotion should be delayed
        # Add debug information
        "debug_info": {
            "timestamp": datetime.datetime.now().isoformat(),
            "current_role": game_state["current_role"],
            "completed_roles": game_state.get("completed_roles", []),
            "role_challenges_completed": game_state.get("role_challenges_completed", 0),
            "challenges_completed": game_state.get("challenges_completed", 0),
            "challenges_required": challenges_required,
            "manager": game_state["current_manager"],
            "manager_name": manager_name,
            "task_id": game_state["current_task"]
        }
    }

    # Log the response data
    logging.debug(f"Returning game state with current_role={response_data['current_role']}, completed_roles={response_data['completed_roles']}")

    return jsonify(response_data)

# Preview response endpoint
@app.route('/preview_response')
def preview_response():
    global game_state
    print("\n\nPreview response endpoint called\n\n")
    prompt = request.args.get('prompt', '')
    task_id = request.args.get('task_id', 'cover_letter')

    print(f"\n\nPrompt: {prompt}\nTask ID: {task_id}\n\n")

    # Validate input
    if not prompt:
        return jsonify({"status": "error", "message": "Prompt cannot be empty"})

    # Generate response using LLM
    try:
        # Import the LLM response generator
        from llm_response_generator_openai_format import generate_response_with_llm

        # Generate response using LLM
        logging.info(f"Generating LLM response for preview with prompt: '{prompt[:50]}...'")
        print(f"\n\nGenerating LLM response for preview with prompt: '{prompt[:50]}...'\n\n")

        # Call the LLM response generator
        preview_response = generate_response_with_llm(prompt, task_id)

        # Convert to HTML
        preview_html = markdown.markdown(preview_response, extensions=['extra'])

        # Print the preview response for debugging
        print("\n\nPREVIEW RESPONSE:\n" + preview_response[:500] + "...\n\n")

        # Evaluate the prompt using prompt_evaluation module if available
        if PROMPT_EVALUATION_AVAILABLE:
            try:
                print("\n\nUsing prompt evaluation module for preview\n\n")
                print(f"\n\nPrompt: {prompt}\n\n")
                print(f"\n\nTask ID: {task_id}\n\n")

                # Evaluate the prompt
                evaluation_results = evaluate_prompt(prompt, task_id)

                print(f"\n\nPrompt evaluation results: {evaluation_results}\n\n")

                # Extract evaluation data
                grade = evaluation_results.get("grade", "okay")
                overall_score = evaluation_results.get("overall_score", 50)
                feedback_details = evaluation_results.get("feedback_details", [])
                similarity_score = overall_score

                # Special handling for cover letter task
                if task_id == "cover_letter":
                    # Evaluate the prompt quality for cover letter task
                    cover_letter_keywords = ["cover letter", "application", "job", "position", "qualifications", "skills", "experience", "solar panel makers", "junior assistant"]
                    keyword_count = sum(1 for keyword in cover_letter_keywords if keyword in prompt.lower())
                    prompt_length = len(prompt)

                    # Log the evaluation metrics
                    logging.info(f"Cover letter prompt preview evaluation: length={prompt_length}, keyword_count={keyword_count}")

                    # Determine grade based on prompt quality
                    if prompt_length < 20 or keyword_count < 2:
                        # Very simple prompts like "draft a cover letter" should not pass
                        grade = "bad"
                        overall_score = 30
                        similarity_score = 30
                        evaluation_results["meets_requirements"] = False
                        logging.info(f"Cover letter prompt preview too simple: grade={grade}, score={overall_score}")
                    elif prompt_length < 50 or keyword_count < 3:
                        # Basic prompts should get cautionary feedback
                        grade = "okay"
                        overall_score = 60
                        similarity_score = 60
                        evaluation_results["meets_requirements"] = True
                        logging.info(f"Cover letter prompt preview basic: grade={grade}, score={overall_score}")
                    else:
                        # Good prompts should pass
                        grade = "good"
                        # Use the actual evaluation score if available, otherwise use a default
                        if "overall_score" in evaluation_results:
                            # Keep the original score from the evaluation
                            overall_score = evaluation_results["overall_score"]
                            similarity_score = overall_score
                        else:
                            # Only use default if we don't have a real score
                            overall_score = 85
                            similarity_score = 85
                        evaluation_results["meets_requirements"] = True
                        logging.info(f"Cover letter prompt preview good: grade={grade}, score={overall_score}")

                # Get dimensions data for detailed feedback
                dimensions = evaluation_results.get("dimensions", {})
                prompt_dimensions = {}

                # Convert dimensions to the format expected by the frontend
                for dim_name, dim_data in dimensions.items():
                    prompt_dimensions[dim_name] = {
                        "score": dim_data.get("score", 0),
                        "feedback": dim_data.get("feedback", ""),
                        "suggestions": dim_data.get("suggestions", [])
                    }

                # Get improvement suggestions
                prompt_improvement_suggestions = evaluation_results.get("improvement_suggestions", [])
                prompt_evaluation_summary = evaluation_results.get("summary", "")

                logging.info(f"Prompt evaluation for preview: grade={grade}, score={overall_score}")
            except Exception as e:
                logging.error(f"Error using prompt evaluation for preview: {str(e)}")
                # Fall back to enhanced evaluation if available
                if ENHANCED_EVALUATION_AVAILABLE:
                    try:
                        print("\n\nFalling back to enhanced evaluation for preview\n\n")
                        # Get task requirements
                        current_role = "applicant"  # Default role
                        if game_state and "current_role" in game_state:
                            current_role = game_state.get("current_role")
                        print(f"\n\nCurrent role: {current_role}\n\n")
                        role_tasks = get_tasks_for_role(current_role, TASKS)
                        print(f"\n\nRole tasks: {len(role_tasks)} tasks found\n\n")
                        current_task = None

                        # Find the task by ID
                        for task in role_tasks:
                            if task["id"] == task_id:
                                current_task = task
                                break

                        # If task not found, use default requirements
                        if current_task:
                            task_requirements = current_task.get("requirements", [])
                            task_description = current_task.get("description", "")
                        else:
                            task_requirements = ["Provide a comprehensive and well-structured response",
                                                "Address all aspects of the task",
                                                "Use professional language and tone"]
                            task_description = "Complete the task with a well-structured response."

                        # Evaluate the response
                        evaluation_results = evaluate_response_with_enhanced_llm(
                            prompt,
                            task_description,
                            task_requirements,
                            task_id
                        )

                        # Extract evaluation data
                        overall_score = evaluation_results["overall_score"]
                        meets_requirements = evaluation_results["meets_requirements"]

                        # For high scores (90+), always consider the task as meeting requirements
                        if similarity_score >= 90:
                            evaluation_results["meets_requirements"] = True
                            meets_requirements = True
                            # Special handling for cover_letter task
                            if task_id == "cover_letter":
                                grade = "good"  # Force good grade for high-scoring cover letters
                            logging.info(f"High score detected in preview ({similarity_score}), automatically setting meets_requirements to True")
                        # Also consider the task as meeting requirements if the grade is good or okay
                        elif grade == "good" or grade == "okay":
                            evaluation_results["meets_requirements"] = True
                            meets_requirements = True
                            logging.info(f"Good/okay grade detected in preview ({grade}), automatically setting meets_requirements to True")

                        # Determine grade based on overall score
                        if overall_score >= 8:
                            grade = "good"
                        elif overall_score >= 6:
                            grade = "okay"
                        else:
                            grade = "bad"

                        # For good/okay grades, consider the task as meeting requirements
                        if grade == "good" or grade == "okay":
                            evaluation_results["meets_requirements"] = True
                            meets_requirements = True

                        # Calculate similarity score (for backward compatibility)
                        # BUGFIX: Convert overall_score to 0-100 scale if it's on 0-10 scale
                        similarity_score = overall_score * 10 if overall_score <= 10 else overall_score

                        # Generate feedback details
                        feedback_details = [
                            f"Completeness: {evaluation_results['completeness_score']}/10",
                            f"Quality: {evaluation_results['quality_score']}/10",
                            f"Professionalism: {evaluation_results['professionalism_score']}/10",
                            f"Relevance: {evaluation_results['relevance_score']}/10",
                            f"Overall: {evaluation_results['overall_score']}/10"
                        ]

                        # Add improvement suggestions to feedback
                        if evaluation_results["improvement_suggestions"]:
                            for suggestion in evaluation_results["improvement_suggestions"]:
                                feedback_details.append(f"Suggestion: {suggestion}")

                        # Generate detailed feedback for improvement
                        improvement_feedback = generate_improvement_feedback(evaluation_results)

                        logging.info(f"Enhanced evaluation for preview: grade={grade}, score={overall_score}, meets_requirements={meets_requirements}")
                    except Exception as e:
                        logging.error(f"Error using enhanced evaluation for preview: {str(e)}")
                        # Fall back to simplified evaluation
                        grade = "good" if len(prompt) > 50 else ("okay" if len(prompt) > 20 else "bad")
                        feedback_details = ["Length: " + str(len(prompt)) + " characters"]
                        similarity_score = 0
                        evaluation_results = None
                else:
                    # Use simplified evaluation based on length
                    grade = "good" if len(prompt) > 50 else ("okay" if len(prompt) > 20 else "bad")
                    feedback_details = ["Length: " + str(len(prompt)) + " characters"]
                    similarity_score = 0
                    evaluation_results = None
        elif ENHANCED_EVALUATION_AVAILABLE:
            # Use enhanced evaluation if prompt evaluation is not available
            try:
                print("\n\nUsing enhanced evaluation for preview\n\n")
                # Get task requirements
                current_role = "applicant"  # Default role
                if game_state and "current_role" in game_state:
                    current_role = game_state.get("current_role")
                print(f"\n\nCurrent role: {current_role}\n\n")
                role_tasks = get_all_role_tasks(current_role)
                print(f"\n\nRole tasks: {len(role_tasks)} tasks found\n\n")
                current_task = None

                # Find the task by ID
                for task in role_tasks:
                    if task["id"] == task_id:
                        current_task = task
                        break

                # If task not found, use default requirements
                if current_task:
                    task_requirements = current_task.get("requirements", [])
                    task_description = current_task.get("description", "")
                else:
                    task_requirements = ["Provide a comprehensive and well-structured response",
                                        "Address all aspects of the task",
                                        "Use professional language and tone"]
                    task_description = "Complete the task with a well-structured response."

                # Evaluate the response
                print(f"\n\nEvaluating prompt with task description: {task_description[:100]}...\n\n")
                print(f"\n\nTask requirements: {task_requirements}\n\n")

                evaluation_results = evaluate_response_with_enhanced_llm(
                    prompt,
                    task_description,
                    task_requirements,
                    task_id
                )

                print(f"\n\nEvaluation results: {evaluation_results}\n\n")

                # Extract evaluation data
                overall_score = evaluation_results["overall_score"]
                meets_requirements = evaluation_results["meets_requirements"]

                # For high scores (90+), always consider the task as meeting requirements
                if similarity_score >= 90:
                    evaluation_results["meets_requirements"] = True
                    meets_requirements = True
                    # Special handling for cover_letter task
                    if task_id == "cover_letter":
                        grade = "good"  # Force good grade for high-scoring cover letters
                    logging.info(f"High score detected in preview ({similarity_score}), automatically setting meets_requirements to True")
                # Also consider the task as meeting requirements if the grade is good or okay
                elif grade == "good" or grade == "okay":
                    evaluation_results["meets_requirements"] = True
                    meets_requirements = True
                    logging.info(f"Good/okay grade detected in preview ({grade}), automatically setting meets_requirements to True")

                print(f"\n\nOverall score: {overall_score}, Meets requirements: {meets_requirements}\n\n")

                # Determine grade based on overall score
                if overall_score >= 8:
                    grade = "good"
                elif overall_score >= 6:
                    grade = "okay"
                else:
                    grade = "bad"

                # For good/okay grades, consider the task as meeting requirements
                if grade == "good" or grade == "okay":
                    evaluation_results["meets_requirements"] = True
                    meets_requirements = True

                # Calculate similarity score (for backward compatibility)
                # BUGFIX: Convert overall_score to 0-100 scale if it's on 0-10 scale
                similarity_score = overall_score * 10 if overall_score <= 10 else overall_score

                # Generate feedback details
                feedback_details = [
                    f"Completeness: {evaluation_results['completeness_score']}/10",
                    f"Quality: {evaluation_results['quality_score']}/10",
                    f"Professionalism: {evaluation_results['professionalism_score']}/10",
                    f"Relevance: {evaluation_results['relevance_score']}/10",
                    f"Overall: {evaluation_results['overall_score']}/10"
                ]

                # Add improvement suggestions to feedback
                if evaluation_results["improvement_suggestions"]:
                    for suggestion in evaluation_results["improvement_suggestions"]:
                        feedback_details.append(f"Suggestion: {suggestion}")

                # Generate detailed feedback for improvement
                improvement_feedback = generate_improvement_feedback(evaluation_results)

                logging.info(f"Enhanced evaluation for preview: grade={grade}, score={overall_score}, meets_requirements={meets_requirements}")
            except Exception as e:
                logging.error(f"Error using enhanced evaluation for preview: {str(e)}")
                # Fall back to simplified evaluation
                grade = "good" if len(prompt) > 50 else ("okay" if len(prompt) > 20 else "bad")
                feedback_details = ["Length: " + str(len(prompt)) + " characters"]
                similarity_score = 0
                evaluation_results = None
        else:
            # Use simplified evaluation based on length
            grade = "good" if len(prompt) > 50 else ("okay" if len(prompt) > 20 else "bad")
            feedback_details = ["Length: " + str(len(prompt)) + " characters"]
            similarity_score = 0
            evaluation_results = None

        # Generate role progression HTML for preview
        try:
            role_progression_html = generate_role_progression_html(
                game_state["current_role"],
                game_state["completed_roles"]
            )
            print("Generated role progression HTML for preview_response")
        except Exception as e:
            print(f"Error generating role progression HTML in preview_response: {str(e)}")
            # Create a simple fallback HTML
            role_progression_html = f'''
            <div class="role-progression">
                <h3>Career Progression</h3>
                <div class="current-role">Current Role: <strong>{game_state["current_role"].replace('_', ' ').title()}</strong></div>
            </div>
            '''
            print("Using fallback role progression HTML in preview_response")

        # Generate organization chart
        try:
            org_chart_html = generate_org_chart_html(
                game_state["current_role"],
                game_state["completed_roles"]
            )
            print("Generated org chart HTML for preview_response")
        except Exception as e:
            print(f"Error generating org chart HTML in preview_response: {str(e)}")
            org_chart_html = "<div>Organization chart unavailable</div>"

        # Generate preview feedback if LLM feedback generator is available
        preview_feedback = ""
        if LLM_FEEDBACK_GENERATOR_AVAILABLE and os.environ.get("USE_LLM_FEEDBACK", "true").lower() == "true":
            try:
                # Get current task and manager
                current_role = game_state.get("current_role", "applicant")
                current_task_id = task_id or game_state.get("current_task", "cover_letter")
                current_manager = game_state.get("current_manager", "hr")

                # Get task details from all_role_tasks
                current_role_tasks = get_all_role_tasks(current_role)
                task_details = get_task_by_id(current_task_id, current_role_tasks)

                if task_details:
                    # Prepare performance metrics for feedback generation
                    # For internal use, scale to 0-10, but for display use the original 0-100 scale
                    performance_score = similarity_score / 10 if similarity_score > 10 else similarity_score
                    performance_metrics = {
                        "overall_score": performance_score,  # Properly scaled to 0-10 for internal use
                        "display_score": similarity_score,   # Original 0-100 scale for display
                        "formatted_score": f"{similarity_score:.0f}%",  # Format as percentage for display in feedback
                        "grade": grade,
                        "feedback_details": feedback_details
                    }

                    # Get the manager name from CHARACTERS dictionary
                    manager_character = CHARACTERS.get(current_manager, {})
                    manager_name = manager_character.get("name", current_manager)

                    print(f"\n\nGenerating preview feedback from {manager_name} for task {current_task_id}\n\n")

                    # Generate personalized feedback
                    preview_feedback = generate_manager_feedback(
                        prompt,  # Player's response
                        current_task_id,  # Task ID
                        manager_name,  # Manager name
                        performance_metrics  # Performance metrics
                    )

                    print(f"\n\nGenerated preview feedback: {preview_feedback[:100]}...\n\n")
            except Exception as e:
                print(f"\n\nError generating preview feedback: {str(e)}\n\n")
                logging.error(f"Error generating preview feedback: {str(e)}")
                # Use empty feedback if there's an error
                preview_feedback = ""

        # Create response data
        response_data = {
            "status": "success",
            "ai_response": preview_response,
            "ai_response_html": preview_html,
            "prompt_evaluation_grade": grade,
            "similarity_score": similarity_score,
            "feedback_details": feedback_details,
            "role_progression_html": role_progression_html,
            "org_chart_html": org_chart_html,
            "preview_feedback": preview_feedback
        }

        # Add prompt evaluation data if available
        if 'prompt_dimensions' in locals() and prompt_dimensions:
            response_data["prompt_dimensions"] = prompt_dimensions

        if 'prompt_improvement_suggestions' in locals() and prompt_improvement_suggestions:
            response_data["prompt_improvement_suggestions"] = prompt_improvement_suggestions

        if 'prompt_evaluation_summary' in locals() and prompt_evaluation_summary:
            response_data["prompt_evaluation_summary"] = prompt_evaluation_summary

        # Add enhanced evaluation data if available
        if 'evaluation_results' in locals() and evaluation_results:
            response_data["meets_requirements"] = evaluation_results.get("meets_requirements", False)
            # Use the original similarity_score (0-100 scale) for display
            response_data["overall_score"] = similarity_score

            # Add detailed feedback if available
            if 'improvement_feedback' in locals() and improvement_feedback:
                response_data["improvement_feedback"] = improvement_feedback

        # Log the response data
        print(f"\n\nResponse data:\nStatus: {response_data['status']}\nAI Response (first 100 chars): {response_data['ai_response'][:100]}...\n\n")

        return jsonify(response_data)
    except Exception as e:
        logging.error(f"Error generating preview response with LLM: {str(e)}")
        print(f"\n\nError generating preview response with LLM: {str(e)}\n\n")
        return jsonify({"status": "error", "message": f"Error generating preview response: {str(e)}"})

# Redirect old index to new index
@app.route('/context_aware_index.html')
def redirect_old_index():
    return redirect('/')

# Serve static files
@app.route('/<path:path>')
def serve_static(path):
    return send_from_directory('.', path)

if __name__ == '__main__':
    # Set up console logging
    console_handler = logging.StreamHandler()
    console_handler.setLevel(logging.INFO)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    logging.getLogger().addHandler(console_handler)
    logging.getLogger().setLevel(logging.INFO)

    print("\n" + "=" * 70)
    print("Starting Corporate Prompt Master - Context Aware Edition on port 5000...")
    print("=" * 70)
    print("\nAccess the game by opening: http://localhost:5000/")
    print("=" * 70 + "\n")

    app.run(debug=True, port=5000)
