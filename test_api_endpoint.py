#!/usr/bin/env python
"""
Script to test the GET_GAME_STATE API endpoint directly.
"""
import os
import django
import sys
import json

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

from django.test import Client
from django.contrib.auth.models import AnonymousUser
from game.models import GameSession

def test_api_endpoint():
    """Test the GET_GAME_STATE API endpoint"""
    print("Testing GET_GAME_STATE API endpoint...")
    
    # Get the most recent game session
    session = GameSession.objects.last()
    if not session:
        print("No game sessions found")
        return
    
    print(f"Session ID: {session.id}")
    print(f"Session key: {session.session_id}")
    
    # Create a test client
    client = Client()
    
    # Set the session key to match the game session
    client.session['session_key'] = session.session_id
    client.session.save()
    
    # Make the API request
    response = client.get('/game/api/get_game_state/')
    
    print(f"\nAPI Response:")
    print(f"Status code: {response.status_code}")
    print(f"Content type: {response.get('Content-Type')}")
    
    if response.status_code == 200:
        try:
            data = response.json()
            print(f"Response status: {data.get('status')}")
            
            if data.get('status') == 'success':
                print(f"\nGame state from API:")
                print(f"  current_role: {data.get('current_role')}")
                print(f"  current_task: {data.get('current_task')}")
                print(f"  current_manager: {data.get('current_manager')}")
                print(f"  role_challenges_completed: {data.get('role_challenges_completed')}")
                print(f"  messages count: {len(data.get('messages', []))}")
            else:
                print(f"API returned error: {data.get('message')}")
        except Exception as e:
            print(f"Error parsing JSON: {e}")
            print(f"Raw response: {response.content}")
    else:
        print(f"HTTP error: {response.content}")

if __name__ == "__main__":
    test_api_endpoint()
