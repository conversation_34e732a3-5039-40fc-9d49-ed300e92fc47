{% extends 'corporate/base.html' %}

{% block title %}Manage Users - {{ company.name }}{% endblock %}

{% block content %}
<!-- Enhanced User Management Hero Section -->
<section class="user-management-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <h1 class="display-5 fw-bold mb-3 text-shadow">
                                    <i class="fas fa-users-cog me-3"></i>
                                    User <span class="text-gradient">Management</span>
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Manage your team members, approve registrations, and control access permissions.
                                </p>
                                <div class="management-badges d-flex flex-wrap gap-3">
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-user-check me-2"></i>Approval System
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-shield-alt me-2"></i>Role Management
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-chart-line me-2"></i>Progress Tracking
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="management-stats text-center">
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ pending_users|length|default:"0" }}</div>
                                            <small class="text-white-75">Pending</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ active_users|length|default:"0" }}</div>
                                            <small class="text-white-75">Active</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Content Container -->
<div class="container-fluid">

    {% if pending_users %}
    <!-- Enhanced Pending Users Section -->
    <div class="card border-0 shadow-lg mb-5">
        <div class="card-header bg-gradient-warning text-dark border-0 py-4">
            <div class="d-flex justify-content-between align-items-center">
                <div class="d-flex align-items-center">
                    <div class="bg-dark bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="fas fa-user-clock fa-lg text-dark"></i>
                    </div>
                    <div>
                        <h4 class="mb-1 fw-bold">Pending Approval Requests</h4>
                        <p class="mb-0">
                            <span class="badge bg-danger rounded-pill px-3 py-2">{{ pending_users|length }} Waiting</span>
                        </p>
                    </div>
                </div>
                <button type="button" class="btn btn-dark btn-lg" data-bs-toggle="collapse"
                        data-bs-target="#pendingUsersCollapse" aria-expanded="true">
                    <i class="fas fa-chevron-down"></i>
                </button>
            </div>
        </div>

        <div class="collapse show" id="pendingUsersCollapse">
            <div class="card-body p-4">
                <div class="alert alert-info border-0 rounded-3 mb-4">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-info-circle me-3 fa-lg"></i>
                        <div>
                            <strong>Review Required</strong>
                            <p class="mb-0 mt-1">These users have registered and are waiting for admin approval to access the platform.</p>
                        </div>
                    </div>
                </div>

                <div class="table-responsive">
                    <table class="table table-hover mb-0">
                        <thead class="bg-gradient-primary text-white">
                            <tr>
                                <th class="border-0 fw-bold">USER</th>
                                <th class="border-0 fw-bold">EMAIL</th>
                                <th class="border-0 fw-bold">REQUESTED ROLE</th>
                                <th class="border-0 fw-bold">REGISTRATION</th>
                                <th class="border-0 fw-bold">ACTIONS</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for corp_user in pending_users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-3">
                                            {% if corp_user.user.profile.avatar %}
                                            <img src="{{ corp_user.user.profile.avatar.url }}" alt="Avatar"
                                                 class="rounded-circle border border-2 border-white" width="45" height="45">
                                            {% else %}
                                            <div class="avatar-placeholder rounded-circle d-flex align-items-center justify-content-center bg-warning text-dark fw-bold"
                                                 style="width: 45px; height: 45px; font-size: 18px;">
                                                {{ corp_user.user.username|first|upper }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="user-name mb-1">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</h6>
                                            <small class="user-handle">@{{ corp_user.user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td class="user-email">{{ corp_user.user.email }}</td>
                                <td>
                                    {% if corp_user.is_company_admin %}
                                    <span class="badge bg-danger role-badge px-3 py-2">
                                        <i class="fas fa-crown me-1"></i>Admin
                                    </span>
                                    {% else %}
                                    <span class="badge bg-secondary role-badge px-3 py-2">
                                        <i class="fas fa-user me-1"></i>Member
                                    </span>
                                    {% endif %}
                                </td>
                                <td class="date-text">
                                    <i class="fas fa-calendar me-2"></i>{{ corp_user.created_at|date:"M d, Y" }}
                                </td>
                                <td>
                                    <div class="btn-group">
                                        <form method="post" action="{% url 'corporate:company_users' %}" class="me-1">
                                            {% csrf_token %}
                                            <input type="hidden" name="action" value="approve_user">
                                            <input type="hidden" name="user_id" value="{{ corp_user.user.id }}">
                                            <button type="submit" class="btn btn-sm btn-success">
                                                <i class="fas fa-check me-1"></i> Approve
                                            </button>
                                        </form>
                                        <button type="button" class="btn btn-sm btn-danger" data-bs-toggle="modal" data-bs-target="#rejectUserModal{{ corp_user.user.id }}">
                                            <i class="fas fa-times me-1"></i> Reject
                                        </button>
                                    </div>
                                </td>
                            </tr>

                            <!-- Reject User Modal -->
                            <div class="modal fade" id="rejectUserModal{{ corp_user.user.id }}" tabindex="-1" aria-labelledby="rejectUserModalLabel{{ corp_user.user.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="rejectUserModalLabel{{ corp_user.user.id }}">Reject User Request</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to reject <strong style="color: #ffffff !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</strong>'s request to join {{ company.name }}?</p>
                                            <p class="text-danger">This action cannot be undone.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <form method="post" action="{% url 'corporate:company_users' %}">
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="reject_user">
                                                <input type="hidden" name="user_id" value="{{ corp_user.user.id }}">
                                                <button type="submit" class="btn btn-danger">Reject Request</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                </div>
            </div>
        </div>
        {% endif %}

        <div class="card shadow">
            <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                <h5 class="mb-0"><i class="fas fa-users me-2"></i>{{ company.name }} Team</h5>
                <div>
                    <a href="{% url 'corporate:company_invitations' %}" class="btn btn-light btn-sm me-2">
                        <i class="fas fa-envelope me-1"></i> Manage Invitations
                        {% if pending_users_count > 0 %}
                        <span class="badge rounded-pill bg-danger">{{ pending_users_count }}</span>
                        {% endif %}
                    </a>
                    <button type="button" class="btn btn-light btn-sm" data-bs-toggle="modal" data-bs-target="#inviteUserModal">
                        <i class="fas fa-user-plus me-1"></i> Invite User
                    </button>
                </div>
            </div>
            <div class="card-body">
                {% if company_users %}
                <div class="table-responsive">
                    <table class="table table-hover">
                        <thead style="background-color: #0d6efd !important;">
                            <tr>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">User</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Email</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Job Title</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Role</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Joined</th>
                                <th style="color: #ffffff !important; font-weight: bold !important; font-size: 16px !important; text-shadow: 1px 1px 1px rgba(0,0,0,0.5) !important;">Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            {% for corp_user in company_users %}
                            <tr>
                                <td>
                                    <div class="d-flex align-items-center">
                                        <div class="avatar me-2">
                                            {% if corp_user.user.profile.avatar %}
                                            <img src="{{ corp_user.user.profile.avatar.url }}" alt="Avatar" class="rounded-circle" width="40">
                                            {% else %}
                                            <div class="avatar-placeholder rounded-circle text-white d-flex align-items-center justify-content-center" style="width: 40px; height: 40px; background-color: #0d6efd; color: white; font-weight: bold; font-size: 16px;">
                                                {{ corp_user.user.username|first|upper }}
                                            </div>
                                            {% endif %}
                                        </div>
                                        <div>
                                            <h6 class="user-name" style="color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</h6>
                                            <small class="user-handle" style="color: #ffffff !important;">@{{ corp_user.user.username }}</small>
                                        </div>
                                    </div>
                                </td>
                                <td style="color: #ffffff !important;">{{ corp_user.user.email }}</td>
                                <td>
                                    {% if corp_user.job_title %}
                                        <span style="color: #ffffff !important;">{{ corp_user.job_title }}</span>
                                    {% else %}
                                        <span style="color: #ffffff !important; font-style: italic;">Not specified</span>
                                    {% endif %}
                                </td>
                                <td>
                                    {% if corp_user.is_company_admin %}
                                    <span class="badge bg-danger" style="border: 1px solid rgba(255, 255, 255, 0.3) !important; color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">Admin</span>
                                    {% else %}
                                    <span class="badge bg-secondary" style="border: 1px solid rgba(255, 255, 255, 0.3) !important; color: #ffffff !important; font-weight: bold !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">Member</span>
                                    {% endif %}
                                </td>
                                <td style="color: #ffffff !important;">{{ corp_user.created_at|date:"M d, Y" }}</td>
                                <td>
                                    <div class="btn-group">
                                        <a href="{% url 'corporate:user_progress_detail' user_id=corp_user.user.id %}" class="btn btn-sm btn-outline-primary">
                                            <i class="fas fa-chart-line"></i>
                                        </a>
                                        {% if corp_user.user != request.user %}
                                        <button type="button" class="btn btn-sm btn-outline-danger" data-bs-toggle="modal" data-bs-target="#removeUserModal{{ corp_user.user.id }}">
                                            <i class="fas fa-user-minus"></i>
                                        </button>
                                        {% endif %}
                                    </div>
                                </td>
                            </tr>

                            <!-- Remove User Modal -->
                            <div class="modal fade" id="removeUserModal{{ corp_user.user.id }}" tabindex="-1" aria-labelledby="removeUserModalLabel{{ corp_user.user.id }}" aria-hidden="true">
                                <div class="modal-dialog">
                                    <div class="modal-content">
                                        <div class="modal-header">
                                            <h5 class="modal-title" id="removeUserModalLabel{{ corp_user.user.id }}">Remove User</h5>
                                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                                        </div>
                                        <div class="modal-body">
                                            <p>Are you sure you want to remove <strong style="color: #ffffff !important; text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;">{{ corp_user.user.get_full_name|default:corp_user.user.username }}</strong> from {{ company.name }}?</p>
                                            <p class="text-danger">This action cannot be undone.</p>
                                        </div>
                                        <div class="modal-footer">
                                            <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                                            <form method="post" action="{% url 'corporate:company_users' %}">
                                                {% csrf_token %}
                                                <input type="hidden" name="action" value="remove_user">
                                                <input type="hidden" name="user_id" value="{{ corp_user.user.id }}">
                                                <button type="submit" class="btn btn-danger">Remove User</button>
                                            </form>
                                        </div>
                                    </div>
                                </div>
                            </div>
                            {% endfor %}
                        </tbody>
                    </table>
                </div>
                {% else %}
                <div class="alert alert-info">
                    <i class="fas fa-info-circle me-2"></i>No users have been added to this company yet.
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>

<!-- Invite User Modal -->
<div class="modal fade" id="inviteUserModal" tabindex="-1" aria-labelledby="inviteUserModalLabel" aria-hidden="true">
    <div class="modal-dialog">
        <div class="modal-content">
            <form method="post">
                {% csrf_token %}
                <input type="hidden" name="action" value="invite_user">
                <div class="modal-header">
                    <h5 class="modal-title" id="inviteUserModalLabel">Invite User</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="{{ invite_form.emails.id_for_label }}" class="form-label">Email Addresses</label>
                        {{ invite_form.emails }}
                        <div class="form-text">{{ invite_form.emails.help_text }}</div>
                        {% if invite_form.emails.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in invite_form.emails.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                    <div class="mb-3">
                        <label for="{{ invite_form.message.id_for_label }}" class="form-label">Personal Message (Optional)</label>
                        {{ invite_form.message }}
                        <div class="form-text">{{ invite_form.message.help_text }}</div>
                        {% if invite_form.message.errors %}
                        <div class="invalid-feedback d-block">
                            {% for error in invite_form.message.errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Cancel</button>
                    <button type="submit" class="btn btn-primary">Send Invitations</button>
                </div>
            </form>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
{% include 'corporate/company_users_extra.html' %}
<style>
/* User Management Hero Section */
.user-management-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Management Badges */
.management-badges .badge {
    transition: all 0.3s ease;
}

.management-badges .badge:hover {
    transform: scale(1.05);
    background-color: rgba(255,255,255,0.4) !important;
}

/* Management Stats */
.management-stats .stat-card {
    transition: all 0.3s ease;
}

.management-stats .stat-card:hover {
    transform: scale(1.05);
}

/* Enhanced Cards */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

/* User Table Styling */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: rgba(255,255,255,0.1) !important;
    transform: scale(1.01);
}

/* User Avatar */
.avatar img {
    transition: all 0.3s ease;
}

.avatar img:hover {
    transform: scale(1.1);
}

/* User Info Styling */
.user-name {
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;
}

.user-handle {
    color: #ffffff !important;
}

.user-email {
    color: #ffffff !important;
}

/* Badge Styling */
.role-badge {
    border: 1px solid rgba(255, 255, 255, 0.3) !important;
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0,0,0,0.5) !important;
}

.date-text {
    color: #ffffff !important;
}

/* Action Buttons */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Modal Styling */
.modal-content {
    border-radius: 1rem;
    border: none;
}

.modal-header {
    border-bottom: 1px solid rgba(255,255,255,0.1);
}

.modal-footer {
    border-top: 1px solid rgba(255,255,255,0.1);
}

/* Form Styling */
.form-control {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}

.form-control:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .user-management-hero .hero-content {
        padding: 2rem !important;
    }

    .display-5 {
        font-size: 2rem;
    }

    .management-badges {
        justify-content: center;
    }

    .management-stats .row {
        justify-content: center;
    }
}

/* Animation for floating elements */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}
</style>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Add Bootstrap classes to form elements
        const emailsTextarea = document.getElementById('{{ invite_form.emails.id_for_label }}');
        if (emailsTextarea) {
            emailsTextarea.classList.add('form-control');
            emailsTextarea.placeholder = 'Enter email addresses, one per line';
        }

        const messageTextarea = document.getElementById('{{ invite_form.message.id_for_label }}');
        if (messageTextarea) {
            messageTextarea.classList.add('form-control');
            messageTextarea.placeholder = 'Optional message to include in the invitation';
        }
    });
</script>
{% endblock %}
