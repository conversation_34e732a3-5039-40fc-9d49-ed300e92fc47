{% load static %}
{% load i18n %}

<header class="unified-header">
    {% if request.impersonator %}
    <div class="impersonation-banner">
        <div class="container-fluid d-flex justify-content-between align-items-center">
            <div>
                <i class="fas fa-user-secret me-2"></i>
                <strong>Impersonation Mode:</strong> You are viewing the site as {{ user.username }}
            </div>
            <div class="d-flex">
                <a href="{% url 'impersonate-stop' %}" class="btn btn-sm btn-danger me-2">
                    <i class="fas fa-sign-out-alt me-1"></i>Stop Impersonation
                </a>
                <a href="{% url 'superadmin:dashboard' %}" target="_blank" class="btn btn-sm btn-outline-light">
                    <i class="fas fa-external-link-alt me-1"></i>Open Superadmin
                </a>
            </div>
        </div>
    </div>
    {% endif %}
    <nav class="navbar navbar-expand-lg navbar-dark" style="background-color: #0f1117; border-bottom: 1px solid #1e2433;">
        <div class="container-fluid">
            <a class="navbar-brand" href="{% url 'home' %}">
                <i class="fas fa-building me-2"></i>Corporate Prompt Master
                {% if company %}
                <span class="company-badge">{{ company.name }}</span>
                {% endif %}
            </a>
            <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#unifiedNavbar">
                <span class="navbar-toggler-icon"></span>
            </button>

            <div class="collapse navbar-collapse" id="unifiedNavbar">
                <!-- Main Navigation Links -->
                <ul class="navbar-nav me-auto">
                    <!-- Home Link -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.url_name == 'home' %}active{% endif %}"
                           href="{% url 'home' %}">
                            <i class="fas fa-home me-1"></i>Home
                        </a>
                    </li>

                    <!-- Games Link -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.namespace == 'corporate' and request.resolver_match.url_name == 'game_list' %}active{% endif %}"
                           href="{% url 'corporate:game_list' %}">
                            <i class="fas fa-gamepad me-1"></i>Games
                        </a>
                    </li>

                    <!-- Leaderboard Link -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.namespace == 'corporate' and request.resolver_match.url_name == 'leaderboard' %}active{% endif %}"
                           href="{% url 'corporate:leaderboard' %}">
                            <i class="fas fa-trophy me-1"></i>Leaderboard
                        </a>
                    </li>

                    <!-- Public Certificate Verification Link -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.namespace == 'corporate' and request.resolver_match.url_name == 'public_verify_certificate' %}active{% endif %}"
                           href="{% url 'corporate:public_verify_certificate' %}">
                            <i class="fas fa-shield-check me-1"></i>Verify Certificate
                        </a>
                    </li>

                    {% if user.is_authenticated %}
                    <!-- Dashboard Link -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.namespace == 'corporate' and request.resolver_match.url_name == 'corporate_dashboard' %}active{% endif %}"
                           href="{% url 'corporate:corporate_dashboard' %}">
                            <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                        </a>
                    </li>

                    <!-- Certificates Link -->
                    <li class="nav-item">
                        <a class="nav-link {% if request.resolver_match.namespace == 'corporate' and request.resolver_match.url_name == 'view_certificates' %}active{% endif %}"
                           href="{% url 'corporate:view_certificates' %}">
                            <i class="fas fa-certificate me-1"></i>Certificates
                        </a>
                    </li>

                    <!-- Company Admin Dropdown -->
                    {% if user.corporate_profile.is_company_admin %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle {% if request.resolver_match.namespace == 'corporate' and request.resolver_match.url_name in 'company_users,company_invitations,user_progress' %}active{% endif %}"
                           href="#" id="adminDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-user-shield me-1"></i>Admin
                            {% if pending_users_count > 0 %}
                            <span class="badge rounded-pill bg-danger">{{ pending_users_count }}</span>
                            {% endif %}
                        </a>
                        <ul class="dropdown-menu">
                            <li>
                                <form id="manage-users-form" action="{% url 'corporate:company_users' %}" method="get" style="display: inline;">
                                    {% csrf_token %}
                                    <a class="dropdown-item" href="javascript:void(0);" onclick="document.getElementById('manage-users-form').submit();">
                                        <i class="fas fa-users me-1"></i>Manage Users
                                        {% if pending_users_count > 0 %}
                                        <span class="badge rounded-pill bg-danger">{{ pending_users_count }}</span>
                                        {% endif %}
                                    </a>
                                </form>
                            </li>
                            <li>
                                <form id="manage-invitations-form" action="{% url 'corporate:company_invitations' %}" method="get" style="display: inline;">
                                    {% csrf_token %}
                                    <a class="dropdown-item" href="javascript:void(0);" onclick="document.getElementById('manage-invitations-form').submit();">
                                        <i class="fas fa-envelope me-1"></i>Manage Invitations
                                        {% if pending_users_count > 0 %}
                                        <span class="badge rounded-pill bg-danger">{{ pending_users_count }}</span>
                                        {% endif %}
                                    </a>
                                </form>
                            </li>
                            <li>
                                <form id="user-progress-form" action="{% url 'corporate:user_progress' %}" method="get" style="display: inline;">
                                    {% csrf_token %}
                                    <a class="dropdown-item" href="javascript:void(0);" onclick="document.getElementById('user-progress-form').submit();">
                                        <i class="fas fa-chart-line me-1"></i>User Progress
                                    </a>
                                </form>
                            </li>
                        </ul>
                    </li>
                    {% endif %}

                    <!-- Superadmin Link -->
                    {% if user.is_superuser %}
                    <li class="nav-item {% if request.resolver_match.namespace == 'superadmin' %}active{% endif %}">
                        <a class="nav-link" href="{% url 'superadmin:dashboard' %}">
                            <i class="fas fa-shield-alt me-1"></i>Superadmin
                        </a>
                    </li>
                    {% endif %}
                    {% endif %}
                </ul>

                <!-- User Menu -->
                <ul class="navbar-nav">
                    {% if user.is_authenticated %}
                    <!-- Company Dropdown -->
                    {% if user_companies and user_companies|length > 1 %}
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="companyDropdown" role="button" data-bs-toggle="dropdown">
                            <i class="fas fa-building me-1"></i>
                            {{ company.name|default:"Select Company" }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            {% for user_company in user_companies %}
                            <li>
                                <a class="dropdown-item {% if user_company == company %}active{% endif %}"
                                   href="{% url 'corporate:company_switch' %}?company_id={{ user_company.id }}">
                                    {{ user_company.name }}
                                    {% if user_company == company %}
                                    <i class="fas fa-check ms-2"></i>
                                    {% endif %}
                                </a>
                            </li>
                            {% endfor %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{% url 'corporate:company_switch' %}">
                                    <i class="fas fa-exchange-alt me-1"></i>Switch Company
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% endif %}

                    <!-- Help Button -->
                    <li class="nav-item">
                        <a class="nav-link btn btn-info text-white px-3 mx-2" href="{% url 'game:help_redirect' %}" target="_blank">
                            <i class="fas fa-question-circle me-1"></i>{% trans "Help" %}
                        </a>
                    </li>

                    <!-- User Dropdown -->
                    <li class="nav-item dropdown">
                        <a class="nav-link dropdown-toggle" href="#" id="userDropdown" role="button" data-bs-toggle="dropdown">
                            {% if user.profile.avatar %}
                            <img src="{{ user.profile.avatar.url }}" alt="Profile" class="rounded-circle me-1" style="width: 24px; height: 24px; object-fit: cover;">
                            {% else %}
                            <i class="fas fa-user-circle me-1"></i>
                            {% endif %}
                            {{ user.get_full_name|default:user.username }}
                        </a>
                        <ul class="dropdown-menu dropdown-menu-end">
                            <li>
                                <a class="dropdown-item" href="{% url 'corporate:corporate_dashboard' %}">
                                    <i class="fas fa-tachometer-alt me-1"></i>Dashboard
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'corporate:edit_profile' %}">
                                    <i class="fas fa-user-edit me-1"></i>Edit Profile
                                </a>
                            </li>
                            <li>
                                <a class="dropdown-item" href="{% url 'corporate:create_company' %}">
                                    <i class="fas fa-plus-circle me-1"></i>Create Company
                                </a>
                            </li>
                            {% if user.is_superuser %}
                            <li>
                                <a class="dropdown-item" href="{% url 'superadmin:dashboard' %}">
                                    <i class="fas fa-shield-alt me-1"></i>Superadmin Dashboard
                                </a>
                            </li>
                            {% endif %}
                            <li><hr class="dropdown-divider"></li>
                            <li>
                                <a class="dropdown-item" href="{% url 'corporate:corporate_logout' %}">
                                    <i class="fas fa-sign-out-alt me-1"></i>Logout
                                </a>
                            </li>
                        </ul>
                    </li>
                    {% else %}
                    <!-- Login/Register Links -->
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'corporate:corporate_login' %}">
                            <i class="fas fa-sign-in-alt me-1"></i>Login
                        </a>
                    </li>
                    <li class="nav-item">
                        <a class="nav-link" href="{% url 'corporate:corporate_register' %}">
                            <i class="fas fa-user-plus me-1"></i>Register
                        </a>
                    </li>
                    {% endif %}
                </ul>
            </div>
        </div>
    </nav>
</header>

<!-- CSS for the unified header -->
<style>
    .unified-header {
        position: sticky;
        top: 0;
        z-index: 1030;
        background-color: #0f1117; /* Match body background */
        margin: 0;
        padding: 0;
    }

    .unified-header .navbar {
        margin: 0;
        padding: 0.5rem 1rem;
    }

    .unified-header .navbar-brand {
        font-weight: 600;
    }

    .unified-header .company-badge {
        font-size: 0.8rem;
        background-color: rgba(255, 255, 255, 0.2);
        padding: 0.2rem 0.5rem;
        border-radius: 0.25rem;
        margin-left: 0.5rem;
    }

    /* Dark mode styles */
    body.dark-mode .unified-header .navbar {
        background-color: #0f1117 !important;
        border-bottom: 1px solid #1e2433;
    }

    body.dark-mode .unified-header .dropdown-menu {
        background-color: #252538;
        border-color: #2a2a45;
    }

    body.dark-mode .unified-header .dropdown-item {
        color: #e0e0e0;
    }

    body.dark-mode .unified-header .dropdown-item:hover {
        background-color: #2a2a45;
    }

    body.dark-mode .unified-header .dropdown-divider {
        border-color: #2a2a45;
    }

    /* Impersonation banner styles */
    .impersonation-banner {
        background-color: #dc3545;
        color: white;
        padding: 0.5rem 1rem;
        font-size: 0.9rem;
    }

    .impersonation-banner button {
        font-size: 0.8rem;
    }

    /* Adjust navbar position when impersonation banner is visible */
    .impersonation-banner + .navbar {
        top: 40px;
    }
</style>

<!-- JavaScript for enforcing dark mode -->
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Always apply dark mode
        const body = document.body;
        const html = document.documentElement;

        // Apply dark mode to both html and body
        body.classList.add('dark-mode');
        html.classList.add('dark-mode');

        // Set data-theme attribute for components that use it
        body.setAttribute('data-theme', 'dark');
        html.setAttribute('data-theme', 'dark');

        // Store preference in cookie and localStorage
        document.cookie = 'theme=dark; path=/; max-age=31536000';
        localStorage.setItem('theme', 'dark');
    });
</script>
