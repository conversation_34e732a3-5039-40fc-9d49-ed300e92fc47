import uuid
import json
from django.utils import timezone
from django.template.loader import render_to_string
from .models import Certificate

# Check if Pillow is available for image generation
try:
    from PIL import Image, ImageDraw, ImageFont
    import io
    PILLOW_AVAILABLE = True
except ImportError as e:
    print(f"Pillow not available: {str(e)}")
    PILLOW_AVAILABLE = False

# Check if html2image is available for HTML-to-image conversion
try:
    from html2image import Html2Image
    HTML2IMAGE_AVAILABLE = True
except ImportError as e:
    print(f"html2image not available: {str(e)}")
    HTML2IMAGE_AVAILABLE = False


def generate_certificate_html(user, game_session, template='standard', certificate=None):
    """
    Generate HTML for a certificate based on the specified template.

    Args:
        user: The user who completed the game
        game_session: The completed game session
        template: The certificate template to use
        certificate: Optional certificate object to use for additional data

    Returns:
        str: HTML content for the certificate
    """
    # Get user's full name or username
    full_name = f"{user.first_name} {user.last_name}".strip()
    if not full_name:
        full_name = user.username

    # Get company name if available
    company_name = None
    try:
        if hasattr(user, 'corporate_profile'):
            company_name = user.corporate_profile.company.name
    except:
        pass

    # Get highest role achieved
    highest_role = game_session.current_role

    # Format date
    issue_date = timezone.now()
    formatted_date = issue_date.strftime("%B %d, %Y")

    # Generate verification code
    verification_code = str(uuid.uuid4())[:8].upper()

    # Get completed games
    completed_games = []
    if hasattr(game_session, 'get_completed_roles'):
        completed_roles = game_session.get_completed_roles()
        if completed_roles:
            completed_games = ["Corporate Prompt Master"]

    # If there are no completed games but the game is completed, add the current game
    if not completed_games and game_session.game_completed:
        completed_games = ["Corporate Prompt Master"]

    # Context for the template
    context = {
        'full_name': full_name,
        'company_name': company_name,
        'highest_role': highest_role,
        'score': game_session.performance_score,
        'issue_date': formatted_date,
        'verification_code': verification_code,
        'completed_games': completed_games,
        'description': "Has successfully completed the Corporate Prompt Master training program.",
        'certificate': certificate,
    }

    # Render the appropriate template
    if template == 'standard':
        html_content = render_to_string('corporate/certificates/standard.html', context)
    else:
        # Default to standard template
        html_content = render_to_string('corporate/certificates/standard.html', context)

    return html_content


def generate_certificate_html_for_image(user, game_session, certificate):
    """
    Generate the exact HTML/CSS that matches the certificates page display.
    """
    # Get user's full name or username
    full_name = f"{user.first_name} {user.last_name}".strip()
    if not full_name:
        full_name = user.username

    # Format date
    formatted_date = certificate.issue_date.strftime("%B %d, %Y")

    # Get completed games
    completed_games = certificate.get_completed_games()
    if not completed_games:
        completed_games = ["Corporate Prompt Master"]

    html_content = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <meta charset="UTF-8">
        <style>
            body {{
                margin: 0;
                padding: 20px;
                background-color: #f9f9f9;
                font-family: Arial, sans-serif;
            }}

            .certificate-preview {{
                border: 1px solid #ddd;
                padding: 20px;
                background-color: #f9f9f9;
                border-radius: 8px;
                margin: 0 auto;
                max-width: 800px;
            }}

            .certificate-inner {{
                background: #111111 url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAEklEQVQImWNgYGD4z0AswK4SAFXuAf8EPy+xAAAAAElFTkSuQmCC') repeat;
                border: 12px solid #d4af37;
                padding: 40px;
                text-align: center;
                position: relative;
                min-height: 500px;
                box-shadow: 0 8px 25px rgba(0,0,0,0.6);
                border-radius: 6px;
            }}

            .certificate-header h3 {{
                color: #d4af37 !important;
                font-size: 28px !important;
                margin-bottom: 20px !important;
                text-transform: uppercase;
                font-weight: bold;
                text-shadow: 1px 1px 5px rgba(0,0,0,0.5);
                letter-spacing: 2px;
            }}

            .certificate-name {{
                font-size: 32px !important;
                font-weight: bold !important;
                margin: 0 auto 20px !important;
                border-bottom: 3px solid #d4af37;
                display: inline-block;
                padding-bottom: 8px;
                color: #ffffff !important;
                text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
            }}

            .certificate-text {{
                margin-bottom: 15px !important;
                font-size: 18px !important;
                color: #d4af37 !important;
                line-height: 1.5;
            }}

            .certificate-role, .certificate-score {{
                font-weight: bold !important;
                color: #ffffff !important;
                font-size: 22px !important;
                text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
                margin-bottom: 15px !important;
            }}

            .certificate-games {{
                margin: 15px auto;
                padding: 10px;
                border: 2px solid #d4af37;
                border-radius: 6px;
                background-color: rgba(0, 0, 0, 0.5);
                max-width: 90%;
                box-shadow: 0 2px 5px rgba(0,0,0,0.3);
            }}

            .certificate-games-title {{
                font-weight: bold !important;
                color: #d4af37 !important;
                margin-bottom: 12px !important;
                text-align: center;
                font-size: 18px !important;
                text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
            }}

            .certificate-games-list {{
                list-style-type: none !important;
                padding: 0 !important;
                margin: 0 !important;
                text-align: center;
            }}

            .certificate-games-list li {{
                font-weight: bold !important;
                padding: 4px 0 !important;
                font-size: 16px !important;
                color: #ffffff !important;
            }}

            .certificate-date, .certificate-verification {{
                margin-top: 20px !important;
                font-size: 16px !important;
                color: #d4af37 !important;
                margin-bottom: 12px !important;
            }}

            .certificate-verification {{
                font-size: 14px !important;
                color: #a0a0a0 !important;
            }}

            .certificate-inner::after {{
                content: "CERTIFIED";
                position: absolute;
                bottom: 20px;
                right: 20px;
                width: 80px;
                height: 80px;
                background: radial-gradient(circle, #d4af37, #b8860b);
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-weight: bold;
                font-size: 12px;
                transform: rotate(-15deg);
                border: 3px solid #8B4513;
                box-shadow: 0 3px 15px rgba(139, 69, 19, 0.4);
                line-height: 1;
                text-align: center;
            }}

            .certificate-signatures {{
                display: flex;
                justify-content: space-between;
                margin-top: 20px;
                padding-top: 15px;
                border-top: 1px solid #d4af37;
            }}

            .signature {{
                text-align: center;
                flex: 1;
                margin: 0 10px;
            }}

            .signature-line {{
                width: 100%;
                height: 1px;
                background-color: #d4af37;
                margin-bottom: 5px;
            }}

            .signature-name {{
                font-weight: bold;
                color: #ffffff;
                font-size: 12px;
                margin-bottom: 2px;
            }}

            .signature-title {{
                font-size: 10px;
                color: #d4af37;
                font-style: italic;
            }}
        </style>
    </head>
    <body>
        <div class="certificate-preview">
            <div class="certificate-inner">
                <div class="certificate-header">
                    <h3>Certificate of Completion</h3>
                </div>
                <div class="certificate-content">
                    <div class="certificate-name">{full_name}</div>
                    <p class="certificate-text">{certificate.description}</p>
                    <p class="certificate-role">Highest Role: {certificate.highest_role.replace('_', ' ').title()}</p>
                    <p class="certificate-score">Final Score: {certificate.final_score}</p>

                    <div class="certificate-games">
                        <p class="certificate-games-title">Training Modules:</p>
                        <ul class="certificate-games-list">
                            {"".join([f"<li>{game}</li>" for game in completed_games])}
                        </ul>
                    </div>

                    <p class="certificate-date">Issued on {formatted_date}</p>
                    <p class="certificate-verification">Verification Code: {certificate.verification_code}</p>

                    <div class="certificate-signatures">
                        <div class="signature">
                            <div class="signature-line"></div>
                            <div class="signature-name">Allan Scofield</div>
                            <div class="signature-title">CEO, 24seven Assistants</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </body>
    </html>
    """
    return html_content


def generate_certificate_image(user, game_session, template='standard'):
    """
    Generate a certificate as an image using Pillow.
    """
    if not PILLOW_AVAILABLE:
        print("Pillow not available")
        return None, None
    
    print(f"Generating certificate image for user: {user.username}, game_session: {game_session.id}")
    
    # Get user's full name or username
    full_name = f"{user.first_name} {user.last_name}".strip()
    if not full_name:
        full_name = user.username

    # Get company name if available
    company_name = None
    try:
        if hasattr(user, 'corporate_profile'):
            company_name = user.corporate_profile.company.name
    except:
        pass

    # Get highest role achieved
    highest_role = game_session.current_role

    # Format date
    issue_date = timezone.now()
    formatted_date = issue_date.strftime("%B %d, %Y")

    # Generate verification code
    verification_code = str(uuid.uuid4())[:8].upper()

    # Create or update certificate record
    certificate, created = Certificate.objects.update_or_create(
        user=user,
        game_session=game_session,
        defaults={
            'title': "Prompt Engineering Excellence",
            'description': "Has successfully completed the Corporate Prompt Master training program.",
            'issue_date': timezone.now(),
            'template': template,
            'final_score': game_session.performance_score,
            'highest_role': game_session.current_role,
            'verification_code': verification_code,
            'completed_games': json.dumps(["Corporate Prompt Master"]),
        }
    )

    # Create certificate image
    # Certificate dimensions (landscape orientation)
    width, height = 1200, 800

    # Create image with very dark background to match HTML template
    img = Image.new('RGB', (width, height), color=(17, 17, 17))  # #111111 - very dark background
    draw = ImageDraw.Draw(img)

    # Add dotted pattern background to match HTML template
    # Create small dots pattern across the background
    dot_spacing = 8
    dot_size = 1
    dot_color = (40, 40, 40)  # Slightly lighter than background for subtle effect

    for x in range(0, width, dot_spacing):
        for y in range(0, height, dot_spacing):
            draw.ellipse([x, y, x + dot_size, y + dot_size], fill=dot_color)

    # Add thick gold border to match HTML template (12px border)
    border_width = 12
    gold_color = (212, 175, 55)  # #d4af37 - gold color
    draw.rectangle([0, 0, width-1, height-1], outline=gold_color, width=border_width)
    
    # Try to load fonts, fallback to default if not available
    try:
        title_font = ImageFont.truetype("arial.ttf", 48)
        subtitle_font = ImageFont.truetype("arial.ttf", 28)
        name_font = ImageFont.truetype("arial.ttf", 36)
        text_font = ImageFont.truetype("arial.ttf", 20)
        small_font = ImageFont.truetype("arial.ttf", 16)
    except:
        # Fallback to default font
        title_font = ImageFont.load_default()
        subtitle_font = ImageFont.load_default()
        name_font = ImageFont.load_default()
        text_font = ImageFont.load_default()
        small_font = ImageFont.load_default()
    
    # Colors to match HTML template exactly
    gold_color = (212, 175, 55)    # #d4af37 - gold color
    white_color = (255, 255, 255)  # #ffffff - white text
    gray_color = (160, 160, 160)   # #a0a0a0 - light gray for verification
    purple_color = (102, 126, 234) # #667eea - purple color for title

    # Start layout with proper spacing
    y_pos = 80

    # Title: "CERTIFICATE OF COMPLETION" in purple, uppercase, bold
    title_text = "CERTIFICATE OF COMPLETION"
    title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
    title_width = title_bbox[2] - title_bbox[0]
    draw.text(((width - title_width) // 2, y_pos), title_text, fill=purple_color, font=title_font)
    y_pos += 80
    
    # Recipient name in white with gold underline
    name_bbox = draw.textbbox((0, 0), full_name, font=name_font)
    name_width = name_bbox[2] - name_bbox[0]
    name_x = (width - name_width) // 2
    draw.text((name_x, y_pos), full_name, fill=white_color, font=name_font)

    # Gold underline for name (3px thick)
    draw.line([name_x, y_pos + 45, name_x + name_width, y_pos + 45], fill=gold_color, width=3)
    y_pos += 80

    # Description in gold
    description = "Has successfully completed the Corporate Prompt Master training program."
    desc_bbox = draw.textbbox((0, 0), description, font=text_font)
    desc_width = desc_bbox[2] - desc_bbox[0]
    draw.text(((width - desc_width) // 2, y_pos), description, fill=gold_color, font=text_font)
    y_pos += 40

    # Highest Role in white
    role_text = f"Highest Role: {highest_role.replace('_', ' ').title()}"
    role_bbox = draw.textbbox((0, 0), role_text, font=subtitle_font)
    role_width = role_bbox[2] - role_bbox[0]
    draw.text(((width - role_width) // 2, y_pos), role_text, fill=white_color, font=subtitle_font)
    y_pos += 40

    # Final Score in white
    score_text = f"Final Score: {game_session.performance_score}"
    score_bbox = draw.textbbox((0, 0), score_text, font=subtitle_font)
    score_width = score_bbox[2] - score_bbox[0]
    draw.text(((width - score_width) // 2, y_pos), score_text, fill=white_color, font=subtitle_font)
    y_pos += 40

    # Training Modules section (like HTML template)
    modules_box_width = int(width * 0.6)  # 60% of width
    modules_box_height = 80
    modules_x = (width - modules_box_width) // 2
    modules_y = y_pos

    # Draw modules box with gold border and dark background
    draw.rectangle([modules_x, modules_y, modules_x + modules_box_width, modules_y + modules_box_height],
                   fill=(0, 0, 0), outline=gold_color, width=2)

    # Training Modules title in gold
    modules_title = "Training Modules:"
    modules_title_bbox = draw.textbbox((0, 0), modules_title, font=text_font)
    modules_title_width = modules_title_bbox[2] - modules_title_bbox[0]
    draw.text(((width - modules_title_width) // 2, modules_y + 15), modules_title, fill=gold_color, font=text_font)

    # Module name in white
    module_name = "Corporate Prompt Master"
    module_bbox = draw.textbbox((0, 0), module_name, font=text_font)
    module_width = module_bbox[2] - module_bbox[0]
    draw.text(((width - module_width) // 2, modules_y + 45), module_name, fill=white_color, font=text_font)

    y_pos += modules_box_height + 40

    # Date in gold
    date_text = f"Issued on {formatted_date}"
    date_bbox = draw.textbbox((0, 0), date_text, font=text_font)
    date_width = date_bbox[2] - date_bbox[0]
    draw.text(((width - date_width) // 2, y_pos), date_text, fill=gold_color, font=text_font)
    y_pos += 30

    # Verification code in gray
    verif_text = f"Verification Code: {verification_code}"
    verif_bbox = draw.textbbox((0, 0), verif_text, font=small_font)
    verif_width = verif_bbox[2] - verif_bbox[0]
    draw.text(((width - verif_width) // 2, y_pos), verif_text, fill=gray_color, font=small_font)
    y_pos += 40

    # Signature section with gold line
    sig_line_width = 200
    sig_x = (width - sig_line_width) // 2
    draw.line([sig_x, y_pos, sig_x + sig_line_width, y_pos], fill=gold_color, width=1)

    # Signature name in white
    sig_name = "Allan Scofield"
    sig_name_bbox = draw.textbbox((0, 0), sig_name, font=small_font)
    sig_name_width = sig_name_bbox[2] - sig_name_bbox[0]
    draw.text(((width - sig_name_width) // 2, y_pos + 10), sig_name, fill=white_color, font=small_font)

    # Signature title in gold
    sig_title = "CEO, 24seven Assistants"
    sig_title_bbox = draw.textbbox((0, 0), sig_title, font=small_font)
    sig_title_width = sig_title_bbox[2] - sig_title_bbox[0]
    draw.text(((width - sig_title_width) // 2, y_pos + 25), sig_title, fill=gold_color, font=small_font)
    
    # Add "CERTIFIED" seal to match HTML template (bottom right, rotated)
    seal_x, seal_y = width - 100, height - 100
    seal_radius = 40

    # Create gold gradient effect with multiple circles
    for i in range(3):
        radius = seal_radius - i * 2
        color_intensity = 212 - i * 20  # Darker gold for inner circles
        circle_color = (color_intensity, int(color_intensity * 0.8), 55)
        draw.ellipse([seal_x - radius, seal_y - radius,
                      seal_x + radius, seal_y + radius],
                     fill=circle_color)

    # Brown border like HTML template
    draw.ellipse([seal_x - seal_radius, seal_y - seal_radius,
                  seal_x + seal_radius, seal_y + seal_radius],
                 outline=(139, 69, 19), width=3)

    # CERTIFIED text in white
    seal_text = "CERTIFIED"
    seal_bbox = draw.textbbox((0, 0), seal_text, font=small_font)
    seal_text_width = seal_bbox[2] - seal_bbox[0]
    seal_text_height = seal_bbox[3] - seal_bbox[1]
    draw.text((seal_x - seal_text_width // 2, seal_y - seal_text_height // 2),
              seal_text, fill='white', font=small_font)
    
    # Convert to bytes
    img_buffer = io.BytesIO()
    img.save(img_buffer, format='PNG', quality=95)
    img_bytes = img_buffer.getvalue()
    img_buffer.close()
    
    print(f"Certificate image generated: {len(img_bytes)} bytes")
    return img_bytes, certificate


def generate_certificate_image_from_existing(certificate):
    """
    Generate a certificate image from an existing certificate object.

    Args:
        certificate: The existing Certificate object

    Returns:
        bytes: The image bytes or None if generation fails
    """
    if not PILLOW_AVAILABLE:
        print("Pillow not available for certificate generation")
        return None

    try:
        # Get user's full name or username
        user = certificate.user
        full_name = f"{user.first_name} {user.last_name}".strip()
        if not full_name:
            full_name = user.username

        # Format date
        formatted_date = certificate.issue_date.strftime("%B %d, %Y")

        # Get completed games
        completed_games = certificate.get_completed_games()
        if not completed_games:
            completed_games = ["Corporate Prompt Master"]

        # Certificate dimensions (landscape orientation)
        width, height = 1200, 800

        # Create image with very dark background to match HTML template
        img = Image.new('RGB', (width, height), color=(17, 17, 17))  # #111111 - very dark background
        draw = ImageDraw.Draw(img)

        # Add dotted pattern background to match HTML template
        # Create small dots pattern across the background
        dot_spacing = 8
        dot_size = 1
        dot_color = (40, 40, 40)  # Slightly lighter than background for subtle effect

        for x in range(0, width, dot_spacing):
            for y in range(0, height, dot_spacing):
                draw.ellipse([x, y, x + dot_size, y + dot_size], fill=dot_color)

        # Add thick gold border to match HTML template (12px border)
        border_width = 12
        gold_color = (212, 175, 55)  # #d4af37 - gold color
        draw.rectangle([0, 0, width-1, height-1], outline=gold_color, width=border_width)

        # Try to load fonts, fallback to default if not available
        try:
            title_font = ImageFont.truetype("arial.ttf", 48)
            subtitle_font = ImageFont.truetype("arial.ttf", 28)
            name_font = ImageFont.truetype("arial.ttf", 36)
            text_font = ImageFont.truetype("arial.ttf", 20)
            small_font = ImageFont.truetype("arial.ttf", 16)
        except:
            # Fallback to default font
            title_font = ImageFont.load_default()
            subtitle_font = ImageFont.load_default()
            name_font = ImageFont.load_default()
            text_font = ImageFont.load_default()
            small_font = ImageFont.load_default()

        # Colors to match HTML template exactly
        gold_color = (212, 175, 55)    # #d4af37 - gold color
        white_color = (255, 255, 255)  # #ffffff - white text
        gray_color = (160, 160, 160)   # #a0a0a0 - light gray for verification
        purple_color = (102, 126, 234) # #667eea - purple color for title

        # Start layout with proper spacing
        y_pos = 80

        # Title: "CERTIFICATE OF COMPLETION" in purple, uppercase, bold
        title_text = "CERTIFICATE OF COMPLETION"
        title_bbox = draw.textbbox((0, 0), title_text, font=title_font)
        title_width = title_bbox[2] - title_bbox[0]
        draw.text(((width - title_width) // 2, y_pos), title_text, fill=purple_color, font=title_font)
        y_pos += 80

        # Recipient name in white with gold underline
        name_bbox = draw.textbbox((0, 0), full_name, font=name_font)
        name_width = name_bbox[2] - name_bbox[0]
        name_x = (width - name_width) // 2
        draw.text((name_x, y_pos), full_name, fill=white_color, font=name_font)

        # Gold underline for name (3px thick)
        draw.line([name_x, y_pos + 45, name_x + name_width, y_pos + 45], fill=gold_color, width=3)
        y_pos += 80

        # Description in gold
        description = certificate.description
        desc_bbox = draw.textbbox((0, 0), description, font=text_font)
        desc_width = desc_bbox[2] - desc_bbox[0]
        draw.text(((width - desc_width) // 2, y_pos), description, fill=gold_color, font=text_font)
        y_pos += 40

        # Highest Role in white
        role_text = f"Highest Role: {certificate.highest_role.replace('_', ' ').title()}"
        role_bbox = draw.textbbox((0, 0), role_text, font=subtitle_font)
        role_width = role_bbox[2] - role_bbox[0]
        draw.text(((width - role_width) // 2, y_pos), role_text, fill=white_color, font=subtitle_font)
        y_pos += 40

        # Final Score in white
        score_text = f"Final Score: {certificate.final_score}"
        score_bbox = draw.textbbox((0, 0), score_text, font=subtitle_font)
        score_width = score_bbox[2] - score_bbox[0]
        draw.text(((width - score_width) // 2, y_pos), score_text, fill=white_color, font=subtitle_font)
        y_pos += 40

        # Training Modules section (like HTML template)
        modules_box_width = int(width * 0.6)  # 60% of width
        modules_box_height = 80
        modules_x = (width - modules_box_width) // 2
        modules_y = y_pos

        # Draw modules box with gold border and dark background
        draw.rectangle([modules_x, modules_y, modules_x + modules_box_width, modules_y + modules_box_height],
                       fill=(0, 0, 0), outline=gold_color, width=2)

        # Training Modules title in gold
        modules_title = "Training Modules:"
        modules_title_bbox = draw.textbbox((0, 0), modules_title, font=text_font)
        modules_title_width = modules_title_bbox[2] - modules_title_bbox[0]
        draw.text(((width - modules_title_width) // 2, modules_y + 15), modules_title, fill=gold_color, font=text_font)

        # Module names in white
        for i, game in enumerate(completed_games[:3]):  # Show up to 3 games
            module_bbox = draw.textbbox((0, 0), game, font=text_font)
            module_width = module_bbox[2] - module_bbox[0]
            draw.text(((width - module_width) // 2, modules_y + 45 + i * 15), game, fill=white_color, font=text_font)

        y_pos += modules_box_height + 40

        # Date in gold
        date_text = f"Issued on {formatted_date}"
        date_bbox = draw.textbbox((0, 0), date_text, font=text_font)
        date_width = date_bbox[2] - date_bbox[0]
        draw.text(((width - date_width) // 2, y_pos), date_text, fill=gold_color, font=text_font)
        y_pos += 30

        # Verification code in gray
        verif_text = f"Verification Code: {certificate.verification_code}"
        verif_bbox = draw.textbbox((0, 0), verif_text, font=small_font)
        verif_width = verif_bbox[2] - verif_bbox[0]
        draw.text(((width - verif_width) // 2, y_pos), verif_text, fill=gray_color, font=small_font)
        y_pos += 40

        # Signature section with gold line
        sig_line_width = 200
        sig_x = (width - sig_line_width) // 2
        draw.line([sig_x, y_pos, sig_x + sig_line_width, y_pos], fill=gold_color, width=1)

        # Signature name in white
        sig_name = "Allan Scofield"
        sig_name_bbox = draw.textbbox((0, 0), sig_name, font=small_font)
        sig_name_width = sig_name_bbox[2] - sig_name_bbox[0]
        draw.text(((width - sig_name_width) // 2, y_pos + 10), sig_name, fill=white_color, font=small_font)

        # Signature title in gold
        sig_title = "CEO, 24seven Assistants"
        sig_title_bbox = draw.textbbox((0, 0), sig_title, font=small_font)
        sig_title_width = sig_title_bbox[2] - sig_title_bbox[0]
        draw.text(((width - sig_title_width) // 2, y_pos + 25), sig_title, fill=gold_color, font=small_font)

        # Add "CERTIFIED" seal to match HTML template (bottom right, rotated)
        seal_x, seal_y = width - 100, height - 100
        seal_radius = 40

        # Create gold gradient effect with multiple circles
        for i in range(3):
            radius = seal_radius - i * 2
            color_intensity = 212 - i * 20  # Darker gold for inner circles
            circle_color = (color_intensity, int(color_intensity * 0.8), 55)
            draw.ellipse([seal_x - radius, seal_y - radius,
                          seal_x + radius, seal_y + radius],
                         fill=circle_color)

        # Brown border like HTML template
        draw.ellipse([seal_x - seal_radius, seal_y - seal_radius,
                      seal_x + seal_radius, seal_y + seal_radius],
                     outline=(139, 69, 19), width=3)

        # CERTIFIED text in white
        seal_text = "CERTIFIED"
        seal_bbox = draw.textbbox((0, 0), seal_text, font=small_font)
        seal_text_width = seal_bbox[2] - seal_bbox[0]
        seal_text_height = seal_bbox[3] - seal_bbox[1]
        draw.text((seal_x - seal_text_width // 2, seal_y - seal_text_height // 2),
                  seal_text, fill='white', font=small_font)

        # Convert to bytes
        img_buffer = io.BytesIO()
        img.save(img_buffer, format='PNG', quality=95)
        img_bytes = img_buffer.getvalue()
        img_buffer.close()

        print(f"Certificate image generated from existing certificate: {len(img_bytes)} bytes")
        return img_bytes

    except Exception as e:
        print(f"Error generating image from existing certificate: {str(e)}")
        import traceback
        traceback.print_exc()
        return None


def generate_certificate_pdf(user, game_session, template='standard'):
    """
    Generate a certificate image for a user who completed the game.

    Args:
        user: The user who completed the game
        game_session: The completed game session
        template: The certificate template to use

    Returns:
        tuple: (image_bytes, certificate_object) or (None, certificate_object) if generation fails
    """
    # Use image generation only
    if PILLOW_AVAILABLE:
        try:
            return generate_certificate_image(user, game_session, template)
        except Exception as e:
            print(f"Certificate image generation failed: {str(e)}")
            import traceback
            traceback.print_exc()
    else:
        print("Pillow not available for certificate generation")

    # If image generation fails, return None
    return None, None


def verify_certificate(verification_code):
    """
    Verify a certificate using its verification code.

    Args:
        verification_code: The verification code to check

    Returns:
        Certificate or None: The certificate if found, None otherwise
    """
    try:
        return Certificate.objects.get(verification_code=verification_code)
    except Certificate.DoesNotExist:
        return None
