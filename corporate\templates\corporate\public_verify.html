{% extends 'corporate/base.html' %}

{% block title %}Certificate Verification - Corporate Prompt Master{% endblock %}

{% block extra_css %}
<style>

        /* Certificate Preview Styles - Match existing project styles */
        .certificate-preview {
            border: 1px solid #ddd;
            padding: 20px;
            background-color: #f9f9f9;
            border-radius: 8px;
            margin: 0 auto 30px;
            max-width: 800px;
        }

        .certificate-inner {
            background: #111111 url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAEklEQVQImWNgYGD4z0AswK4SAFXuAf8EPy+xAAAAAElFTkSuQmCC') repeat;
            border: 12px solid #d4af37;
            padding: 40px;
            text-align: center;
            position: relative;
            min-height: 500px;
            box-shadow: 0 8px 25px rgba(0,0,0,0.6);
            border-radius: 6px;
        }

        .certificate-header h3 {
            color: #d4af37 !important;
            font-size: 28px !important;
            margin-bottom: 20px !important;
            text-transform: uppercase;
            font-weight: bold;
            text-shadow: 1px 1px 5px rgba(0,0,0,0.5);
            letter-spacing: 2px;
        }

        .certificate-name {
            font-size: 32px !important;
            font-weight: bold !important;
            margin: 0 auto 20px !important;
            border-bottom: 3px solid #d4af37;
            display: inline-block;
            padding-bottom: 8px;
            color: #ffffff !important;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
        }

        .certificate-text {
            margin-bottom: 15px !important;
            font-size: 18px !important;
            color: #d4af37 !important;
            line-height: 1.5;
        }

        .certificate-role, .certificate-score {
            font-weight: bold !important;
            color: #ffffff !important;
            font-size: 22px !important;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
            margin-bottom: 15px !important;
        }

        .certificate-games {
            margin: 15px auto;
            padding: 10px;
            border: 2px solid #d4af37;
            border-radius: 6px;
            background-color: rgba(0, 0, 0, 0.5);
            max-width: 90%;
            box-shadow: 0 2px 5px rgba(0,0,0,0.3);
        }

        .certificate-games-title {
            font-weight: bold !important;
            color: #d4af37 !important;
            margin-bottom: 12px !important;
            text-align: center;
            font-size: 18px !important;
            text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
        }

        .certificate-games-list {
            list-style-type: none !important;
            padding: 0 !important;
            margin: 0 !important;
            text-align: center;
        }

        .certificate-games-list li {
            font-weight: bold !important;
            padding: 4px 0 !important;
            font-size: 16px !important;
            color: #ffffff !important;
        }

        .certificate-date, .certificate-verification {
            margin-top: 20px !important;
            font-size: 16px !important;
            color: #d4af37 !important;
            margin-bottom: 12px !important;
        }

        .certificate-verification {
            font-size: 14px !important;
            color: #a0a0a0 !important;
        }

        /* Add certificate seal */
        .certificate-inner::after {
            content: "CERTIFIED";
            position: absolute;
            bottom: 20px;
            right: 20px;
            width: 80px;
            height: 80px;
            background: radial-gradient(circle, #d4af37, #b8860b);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-weight: bold;
            font-size: 12px;
            transform: rotate(-15deg);
            border: 3px solid #8B4513;
            box-shadow: 0 3px 15px rgba(139, 69, 19, 0.4);
            line-height: 1;
            text-align: center;
        }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-md-8">
        <div class="card shadow">
            <div class="card-header bg-primary text-white">
                <h4 class="mb-0"><i class="fas fa-certificate me-2"></i>Public Certificate Verification</h4>
                <p class="mb-0 mt-2">Verify the authenticity of Corporate Prompt Master certificates</p>
            </div>
            <div class="card-body">
                <form method="get" class="mb-4">
                    <div class="input-group">
                        <input type="text" name="code" class="form-control" placeholder="Enter certificate verification code" value="{{ verification_code }}">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Verify
                        </button>
                    </div>
                </form>

                {% if verification_code and not certificate %}
                <div class="alert alert-danger">
                    <i class="fas fa-times-circle me-2"></i>Invalid verification code. The certificate could not be found.
                </div>
                {% endif %}

                {% if certificate %}
                <div class="alert alert-success mb-4">
                    <i class="fas fa-check-circle me-2"></i>Certificate verified successfully!
                </div>

                <div class="certificate-preview mb-4">
                    <div class="certificate-inner">
                        <div class="certificate-header">
                            <h3>Certificate of Completion</h3>
                        </div>
                        <div class="certificate-content">
                            <div class="certificate-name">{{ certificate.user.get_full_name|default:certificate.user.username }}</div>
                            <p class="certificate-text">{{ certificate.description }}</p>
                            <p class="certificate-role">Highest Role: {{ certificate.highest_role|title }}</p>
                            <p class="certificate-score">Final Score: {{ certificate.final_score }}</p>

                            {% if certificate.get_completed_games %}
                            <div class="certificate-games">
                                <div class="certificate-games-title">Completed Training Programs</div>
                                <ul class="certificate-games-list">{% for game in certificate.get_completed_games %}<li>{{ game }}</li>{% endfor %}</ul>
                            </div>
                            {% endif %}

                            <div class="certificate-date">
                                Issued on {{ certificate.issue_date|date:"F d, Y" }}
                            </div>
                            <div class="certificate-verification">
                                Verification Code: {{ certificate.verification_code }}
                            </div>
                        </div>
                    </div>
                </div>

                <div class="card mb-4">
                    <div class="card-header">
                        <h5 class="mb-0">Certificate Details</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <p><strong>Recipient:</strong> {{ certificate.user.get_full_name|default:certificate.user.username }}</p>
                                <p><strong>Title:</strong> {{ certificate.title }}</p>
                                <p><strong>Issue Date:</strong> {{ certificate.issue_date|date:"F d, Y" }}</p>
                            </div>
                            <div class="col-md-6">
                                <p><strong>Final Score:</strong> {{ certificate.final_score }}</p>
                                <p><strong>Highest Role:</strong> {{ certificate.highest_role|title }}</p>
                                <p><strong>Verification Code:</strong> {{ certificate.verification_code }}</p>
                            </div>
                        </div>
                    </div>
                </div>
                {% endif %}

                {% if not verification_code %}
                <div class="mt-4">
                    <h5><i class="fas fa-question-circle me-2"></i>How to Verify a Certificate</h5>
                    <ol class="mt-3">
                        <li>Obtain the verification code from the certificate holder</li>
                        <li>Enter the code in the field above</li>
                        <li>Click "Verify" to check authenticity</li>
                        <li>View the verified certificate details and information</li>
                    </ol>
                    <div class="alert alert-info mt-3">
                        <i class="fas fa-info-circle me-2"></i>
                        Verification codes are unique 8-character alphanumeric codes (e.g., ABC12345)
                    </div>
                </div>
                {% endif %}
            </div>
        </div>
    </div>
</div>
{% endblock %}
