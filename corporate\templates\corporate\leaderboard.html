{% extends 'corporate/base.html' %}

{% block title %}Leaderboard - Corporate Prompt Master{% endblock %}

{% block content %}
<!-- Enhanced Leaderboard Hero Section -->
<section class="leaderboard-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <h1 class="display-5 fw-bold mb-3 text-shadow">
                                    <i class="fas fa-trophy me-3"></i>
                                    Global <span class="text-gradient">Leaderboard</span>
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Compete with professionals worldwide and showcase your prompt engineering mastery.
                                </p>
                                <div class="achievement-badges d-flex flex-wrap gap-3">
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-medal me-2"></i>Top Performers
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-chart-line me-2"></i>Real-time Rankings
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-users me-2"></i>Company Competition
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="leaderboard-stats text-center">
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ total_players|default:"0" }}</div>
                                            <small class="text-white-75">Active Players</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ total_companies|default:"0" }}</div>
                                            <small class="text-white-75">Companies</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Filters Section -->
<div class="container-fluid mb-5">
    <div class="card border-0 shadow-lg">
        <div class="card-header bg-gradient-primary text-white border-0 py-4">
            <div class="d-flex align-items-center">
                <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                    <i class="fas fa-sliders-h fa-lg text-white"></i>
                </div>
                <div>
                    <h4 class="mb-1 fw-bold">Leaderboard Filters</h4>
                    <p class="mb-0 text-white-75">Customize your ranking view and competition scope</p>
                </div>
            </div>
        </div>
        <div class="card-body p-4">
            <form method="get" class="row g-4">
                <!-- Time Period Filter -->
                <div class="col-lg-4">
                    <label class="form-label fw-bold mb-3">
                        <i class="fas fa-calendar-alt me-2 text-primary"></i>Time Period
                    </label>
                    <div class="filter-group">
                        <div class="btn-group-vertical w-100" role="group">
                            <a href="?period=all_time{% if selected_game %}&game={{ selected_game }}{% endif %}{% if show_company_only %}&company_only=true{% endif %}"
                               class="btn {% if time_period == 'all_time' %}btn-primary{% else %}btn-outline-primary{% endif %} mb-2">
                                <i class="fas fa-infinity me-2"></i>All Time Champions
                            </a>
                            <a href="?period=monthly{% if selected_game %}&game={{ selected_game }}{% endif %}{% if show_company_only %}&company_only=true{% endif %}"
                               class="btn {% if time_period == 'monthly' %}btn-primary{% else %}btn-outline-primary{% endif %} mb-2">
                                <i class="fas fa-calendar me-2"></i>This Month
                            </a>
                            <a href="?period=weekly{% if selected_game %}&game={{ selected_game }}{% endif %}{% if show_company_only %}&company_only=true{% endif %}"
                               class="btn {% if time_period == 'weekly' %}btn-primary{% else %}btn-outline-primary{% endif %} mb-2">
                                <i class="fas fa-calendar-week me-2"></i>This Week
                            </a>
                            <a href="?period=daily{% if selected_game %}&game={{ selected_game }}{% endif %}{% if show_company_only %}&company_only=true{% endif %}"
                               class="btn {% if time_period == 'daily' %}btn-primary{% else %}btn-outline-primary{% endif %}">
                                <i class="fas fa-calendar-day me-2"></i>Today
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Game Filter -->
                <div class="col-lg-4">
                    <label class="form-label fw-bold mb-3">
                        <i class="fas fa-gamepad me-2 text-success"></i>Training Module
                    </label>
                    <div class="filter-group">
                        <select name="game" class="form-select form-select-lg" onchange="this.form.submit()" title="Select a training module">
                            {% for game in games_list %}
                            <option value="{{ game.game_id }}" {% if selected_game == game.game_id %}selected{% endif %}>
                                {{ game.game_name }}
                            </option>
                            {% endfor %}
                        </select>
                        <input type="hidden" name="period" value="{{ time_period }}">
                        <div class="form-text mt-2">
                            <i class="fas fa-lightbulb me-1 text-warning"></i>
                            <small>Filter rankings by specific training modules</small>
                        </div>
                    </div>
                </div>

                <!-- Scope Filter -->
                <div class="col-lg-4">
                    <label class="form-label fw-bold mb-3">
                        <i class="fas fa-users me-2 text-warning"></i>Competition Scope
                    </label>
                    <div class="filter-group">
                        {% if show_company_only %}
                        <a href="?period={{ time_period }}{% if selected_game %}&game={{ selected_game }}{% endif %}"
                           class="btn btn-warning btn-lg w-100">
                            <i class="fas fa-globe me-2"></i>View Global Rankings
                        </a>
                        <div class="form-text mt-2">
                            <i class="fas fa-building me-1 text-primary"></i>
                            <small>Currently showing: {{ company.name }} only</small>
                        </div>
                        {% else %}
                        <a href="?period={{ time_period }}{% if selected_game %}&game={{ selected_game }}{% endif %}&company_only=true"
                           class="btn btn-outline-warning btn-lg w-100">
                            <i class="fas fa-building me-2"></i>View Company Rankings
                        </a>
                        <div class="form-text mt-2">
                            <i class="fas fa-globe me-1 text-success"></i>
                            <small>Currently showing: Global competition</small>
                        </div>
                        {% endif %}
                    </div>
                </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaderboard Tabs -->
    <div class="row">
        <div class="col-12">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-light border-0 p-0">
                    <ul class="nav nav-tabs border-0" id="leaderboardTabs" role="tablist">
                        <li class="nav-item" role="presentation">
                            <button class="nav-link active fw-semibold" id="company-tab" data-bs-toggle="tab"
                                    data-bs-target="#company-leaderboard" type="button" role="tab">
                                <i class="fas fa-building me-2"></i>{{ company.name }} Leaderboard
                            </button>
                        </li>
                        <li class="nav-item" role="presentation">
                            <button class="nav-link fw-semibold" id="global-tab" data-bs-toggle="tab"
                                    data-bs-target="#global-leaderboard" type="button" role="tab">
                                <i class="fas fa-globe me-2"></i>Global Leaderboard
                            </button>
                        </li>
                    </ul>
                </div>

                <div class="card-body p-0">
                    <div class="tab-content" id="leaderboardTabsContent">
                        <!-- Company Leaderboard -->
                        <div class="tab-pane fade show active" id="company-leaderboard" role="tabpanel">
                            <div class="p-4">
                                <div class="d-flex justify-content-between align-items-center mb-4">
                                    <div>
                                        <h5 class="mb-1 fw-bold">
                                            <i class="fas fa-building me-2 text-primary"></i>{{ company.name }} Leaderboard
                                        </h5>
                                        <span class="badge bg-primary rounded-pill">{{ time_period|title|cut:"_" }} Period</span>
                                    </div>
                                    {% if user_company_rank %}
                                    <div class="text-end">
                                        <div class="small text-muted">Your Rank</div>
                                        <span class="badge bg-success fs-6">#{{ user_company_rank }}</span>
                                    </div>
                                    {% endif %}
                                </div>

                                {% if company_entries %}
                                <!-- Legend -->
                                <div class="mb-4 p-3 bg-light rounded-3">
                                    <div class="small fw-semibold text-muted mb-2">
                                        <i class="fas fa-info-circle me-1"></i>Legend
                                    </div>
                                    <div class="d-flex flex-wrap gap-2">
                                        <span class="badge bg-primary">
                                            <i class="fas fa-user me-1"></i>You
                                        </span>
                                        <span class="badge bg-secondary">
                                            <i class="fas fa-users me-1"></i>{{ company.name }} Colleague
                                        </span>
                                    </div>
                                </div>

                                <div class="table-responsive">
                                    <table class="table table-hover mb-0">
                                        <thead class="bg-primary text-white">
                                            <tr>
                                                <th class="border-0 fw-semibold">RANK</th>
                                                <th class="border-0 fw-semibold">USER</th>
                                                <th class="border-0 fw-semibold">SCORE</th>
                                        <th>Highest Role</th>
                                        <th>Status</th>
                                        {% if not selected_game %}
                                        <th>Game</th>
                                        {% endif %}
                                        <th>Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {% for entry in company_entries %}
                                    <tr {% if entry.user == user %}class="bg-primary text-white"{% endif %}>
                                        <td>{{ forloop.counter }}</td>
                                        <td>
                                            {{ entry.user.get_full_name|default:entry.user.username }}
                                            <span class="badge bg-secondary ms-1" title="Colleague">👥</span>
                                        </td>
                                        <td>{{ entry.score }}</td>
                                        <td>{{ entry.highest_role|title }}</td>
                                        <td>
                                            {% if entry.game_status == "Completed" %}
                                                <span class="badge bg-success">{{ entry.game_status }}</span>
                                            {% elif entry.game_status == "In Progress" %}
                                                <span class="badge bg-warning text-dark">{{ entry.game_status }}</span>
                                            {% else %}
                                                <span class="badge bg-secondary">{{ entry.game_status|default:"Unknown" }}</span>
                                            {% endif %}
                                        </td>
                                        {% if not selected_game %}
                                        <td>{{ entry.game_name|default:"Corporate Prompt Master" }}</td>
                                        {% endif %}
                                        <td>{{ entry.recorded_at|date:"M d, Y" }}</td>
                                    </tr>
                                    {% endfor %}
                                </tbody>
                            </table>
                        </div>
                        {% else %}
                        <div class="alert alert-info">
                            <i class="fas fa-info-circle me-2"></i>No entries in the company leaderboard yet. Be the first to complete a game!
                            <div class="mt-2">
                                <a href="{% url 'corporate:game_list' %}" class="btn btn-primary btn-sm">
                                    <i class="fas fa-gamepad me-1"></i> Play Corporate Prompt Master
                                </a>
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>


        </div>
    </div>
</div>
{% endblock %}

{% block extra_css %}
<style>
/* Leaderboard Hero Section */
.leaderboard-hero {
    background: linear-gradient(135deg, #f093fb 0%, #f5576c 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Achievement Badges */
.achievement-badges .badge {
    transition: all 0.3s ease;
}

.achievement-badges .badge:hover {
    transform: scale(1.05);
    background-color: rgba(255,255,255,0.4) !important;
}

/* Leaderboard Stats */
.leaderboard-stats .stat-card {
    transition: all 0.3s ease;
}

.leaderboard-stats .stat-card:hover {
    transform: scale(1.05);
}

/* Filter Groups */
.filter-group {
    transition: all 0.3s ease;
}

.filter-group:hover {
    transform: translateY(-2px);
}

.filter-group .btn-group-vertical .btn {
    border-radius: 0.5rem !important;
    margin-bottom: 0.5rem;
    transition: all 0.3s ease;
}

.filter-group .btn-group-vertical .btn:hover {
    transform: translateX(5px);
}

.filter-group .form-select {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
}

.filter-group .form-select:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Enhanced Cards */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-2px);
}

/* Leaderboard Table */
.table-responsive {
    border-radius: 0.5rem;
    overflow: hidden;
}

.table th {
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
    font-size: 0.875rem;
}

.table tbody tr {
    transition: all 0.3s ease;
}

.table tbody tr:hover {
    background-color: #f8f9fa !important;
    transform: scale(1.01);
}

.table tbody tr.bg-primary:hover {
    background-color: #0d6efd !important;
    transform: scale(1.02);
}

/* Nav Tabs */
.nav-tabs .nav-link {
    border: none;
    border-radius: 0.5rem 0.5rem 0 0;
    transition: all 0.3s ease;
    font-weight: 600;
}

.nav-tabs .nav-link:hover {
    background-color: #f8f9fa;
    transform: translateY(-2px);
}

.nav-tabs .nav-link.active {
    background-color: #0d6efd;
    color: white !important;
}

/* Badges */
.badge {
    transition: all 0.3s ease;
}

.badge:hover {
    transform: scale(1.1);
}

/* Buttons */
.btn {
    transition: all 0.3s ease;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .leaderboard-hero .hero-content {
        padding: 2rem !important;
    }

    .display-5 {
        font-size: 2rem;
    }

    .achievement-badges {
        justify-content: center;
    }

    .leaderboard-stats .row {
        justify-content: center;
    }

    .filter-group .btn-group-vertical .btn {
        margin-bottom: 0.25rem;
    }
}

/* Animation for floating elements */
@keyframes float {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
}

.floating-element {
    animation: float 6s ease-in-out infinite;
}

/* Trophy Icons */
.fa-trophy {
    color: #ffd700;
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Rank Styling */
.table tbody tr:nth-child(1) td:first-child::before {
    content: '🥇 ';
}

.table tbody tr:nth-child(2) td:first-child::before {
    content: '🥈 ';
}

.table tbody tr:nth-child(3) td:first-child::before {
    content: '🥉 ';
}
</style>
{% endblock %}
