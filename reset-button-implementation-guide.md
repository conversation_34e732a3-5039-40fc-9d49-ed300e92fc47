# Reset Button Implementation Guide - Functional Mechanics

This document explains how reset buttons work functionally in the prompt game project, focusing on the actual mechanisms that reset game state, data structures, and system behavior.

## Overview

The project implements comprehensive reset functionality that operates at multiple levels:
1. **Frontend State Reset** - JavaScript variables, UI elements, and client-side storage
2. **Backend State Reset** - Database records, session data, and server-side game state
3. **Full System Reset** - Complete game restart with fresh initialization

## 1. Complete Game Reset Flow

### The Reset Trigger Chain

When a user clicks the reset button, this is the complete flow that occurs:

1. **User Confirmation** → 2. **Frontend Cleanup** → 3. **Backend Reset** → 4. **Fresh Initialization**

### Frontend Reset Mechanics (`game/static/game/js/context_aware_game.js`)

#### Game State Object Reset

```javascript
// Complete game state reset - all variables cleared
function resetGameState() {
    gameState.messages = [];                    // Clear all chat messages
    gameState.currentRole = 'applicant';        // Reset to starting role
    gameState.performanceScore = 0;             // Reset score to zero
    gameState.challengesCompleted = 0;          // Reset challenge counter
    gameState.roleChallengesCompleted = 0;      // Reset role-specific challenges
    gameState.gameCompleted = false;            // Mark game as not completed
    gameState.currentManager = 'hr';            // Reset to HR manager
    gameState.currentTask = 'cover_letter';     // Reset to first task
    gameState.completedRoles = [];              // Clear completed roles array
    gameState.firstTaskPending = true;          // Reset task pending flag
    gameState.nextTaskPending = false;          // Clear next task flag
    gameState.taskCompleted = false;            // Reset task completion flag
}
```

#### UI Element Reset

```javascript
// Clear all UI elements and restore initial state
function resetUIElements() {
    // 1. Clear message container completely
    if (elements.messagesContainer) {
        elements.messagesContainer.innerHTML = '';
        console.log('Cleared message container in UI');
    }

    // 2. Reset input fields
    if (elements.promptInput) {
        elements.promptInput.value = '';
    }

    // 3. Reset button states
    if (elements.submitButton) {
        elements.submitButton.disabled = false;
    }

    // 4. Hide any modal dialogs
    elements.gameCompleteModal.classList.add('hidden');

    // 5. Reset step visibility
    setStep('prompt'); // Return to initial step
}
```

#### The Complete Reset Function

```javascript
// This is the main reset function that orchestrates everything
async function startGame() {
    console.log('Starting game...');
    showLoading();

    // STEP 1: Clear UI immediately
    if (elements.messagesContainer) {
        elements.messagesContainer.innerHTML = '';
    }

    // STEP 2: Reset all game state variables
    gameState.messages = [];
    gameState.currentRole = 'applicant';
    gameState.performanceScore = 0;
    gameState.challengesCompleted = 0;
    gameState.roleChallengesCompleted = 0;
    gameState.gameCompleted = false;
    gameState.currentManager = 'hr';
    gameState.currentTask = 'cover_letter';
    gameState.completedRoles = [];
    gameState.firstTaskPending = true;
    gameState.nextTaskPending = false;
    gameState.taskCompleted = false;

    // STEP 3: Make API call to reset backend
    try {
        const timestamp = new Date().getTime();
        const response = await fetch(`${API_ENDPOINTS.START_GAME}?t=${timestamp}`, {
            headers: {
                'Cache-Control': 'no-cache, no-store, must-revalidate',
                'Pragma': 'no-cache',
                'Expires': '0'
            }
        });

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        const data = await response.json();

        // STEP 4: Update frontend with fresh backend data
        gameState.currentRole = data.current_role || 'applicant';
        gameState.performanceScore = data.performance_score || 0;
        gameState.challengesCompleted = data.challenges_completed || 0;
        gameState.gameCompleted = data.game_completed || false;
        gameState.currentManager = data.current_manager || 'hr';
        gameState.currentTask = data.current_task || 'cover_letter';
        gameState.completedRoles = data.completed_roles || [];

        // STEP 5: Initialize fresh UI
        updateUI();

    } catch (error) {
        console.error('Error starting game:', error);
    } finally {
        hideLoading();
    }
}
```

## 2. Backend Reset Mechanics (`game/views.py`)

### Database Models Structure

First, understand the database structure that needs to be reset:

```python
# GameSession Model - stores all game state
class GameSession(models.Model):
    user = models.ForeignKey(User, on_delete=models.CASCADE, null=True, blank=True)
    session_id = models.CharField(max_length=100, null=True, blank=True)
    created_at = models.DateTimeField(auto_now_add=True)
    updated_at = models.DateTimeField(auto_now=True)

    # Company relationship for corporate users
    company = models.ForeignKey(CorporateCompany, on_delete=models.CASCADE, null=True, blank=True)
    team = models.CharField(max_length=100, blank=True)

    # THESE ARE THE FIELDS THAT GET RESET:
    current_role = models.CharField(max_length=50, default="applicant")
    performance_score = models.IntegerField(default=0)
    challenges_completed = models.IntegerField(default=0)
    role_challenges_completed = models.IntegerField(default=0)
    game_completed = models.BooleanField(default=False)
    current_manager = models.CharField(max_length=50, default="hr")
    current_task = models.CharField(max_length=50, default="cover_letter")
    completed_roles = models.TextField(default="[]")  # JSON string
    first_task_pending = models.BooleanField(default=True)
    next_task_pending = models.BooleanField(default=False)

# Message Model - stores all chat messages
class Message(models.Model):
    game_session = models.ForeignKey(GameSession, related_name='messages', on_delete=models.CASCADE)
    message_id = models.CharField(max_length=100)
    sender = models.CharField(max_length=50)
    text = models.TextField()
    html = models.TextField()
    timestamp = models.DateTimeField(auto_now_add=True)
    is_challenge = models.BooleanField(default=False)
    is_markdown = models.BooleanField(default=True)
    is_promotion = models.BooleanField(default=False, null=True, blank=True)
    task_id = models.CharField(max_length=50, null=True, blank=True)
```

### Session Management Function

```python
def get_or_create_game_session(request):
    """
    Get or create a game session for the current user/session.
    This handles both authenticated users and anonymous sessions.
    """
    try:
        # Get company from request session if available
        company_id = request.session.get('active_company_id')
        company = None
        if company_id:
            try:
                company = Company.objects.get(id=company_id)
                logging.info(f"Found company {company.name} (ID: {company.id})")
            except (Company.DoesNotExist, ValueError):
                logging.warning(f"Company with ID {company_id} not found")
                company = None

        # Handle authenticated users
        if request.user.is_authenticated:
            # Try to get corporate profile's company
            try:
                from corporate.models import CorporateUser
                corporate_user = CorporateUser.objects.get(user=request.user)
                if not company:
                    company = corporate_user.company
                    logging.info(f"Using company from corporate profile: {company.name}")
            except CorporateUser.DoesNotExist:
                logging.info("User has no corporate profile")

            # Get or create session for authenticated user
            game_session, created = GameSession.objects.get_or_create(
                user=request.user,
                company=company,
                defaults={
                    'current_role': 'applicant',
                    'performance_score': 0,
                    'challenges_completed': 0,
                    'role_challenges_completed': 0,
                    'game_completed': False,
                    'current_manager': 'hr',
                    'current_task': 'cover_letter',
                    'completed_roles': '[]',
                    'first_task_pending': True,
                    'next_task_pending': False
                }
            )
        else:
            # Handle anonymous users with session ID
            session_id = request.session.session_key
            if not session_id:
                request.session.create()
                session_id = request.session.session_key

            game_session, created = GameSession.objects.get_or_create(
                session_id=session_id,
                company=company,
                defaults={
                    'current_role': 'applicant',
                    'performance_score': 0,
                    'challenges_completed': 0,
                    'role_challenges_completed': 0,
                    'game_completed': False,
                    'current_manager': 'hr',
                    'current_task': 'cover_letter',
                    'completed_roles': '[]',
                    'first_task_pending': True,
                    'next_task_pending': False
                }
            )

        return game_session

    except Exception as e:
        logging.error(f"Error getting game session: {str(e)}")
        # Create temporary in-memory session as fallback
        game_session = GameSession()
        game_session.current_role = "applicant"
        game_session.performance_score = 0
        game_session.challenges_completed = 0
        game_session.role_challenges_completed = 0
        game_session.game_completed = False
        game_session.current_manager = "hr"
        game_session.current_task = "cover_letter"
        game_session.completed_roles = "[]"
        game_session.first_task_pending = True
        game_session.next_task_pending = False
        return game_session
```

### Complete Database Reset Function

```python
def start_game(request):
    """
    API endpoint to start a new game - resets all database state
    """
    try:
        # Check if this is a restart request
        is_restart = 'restart' in request.GET or 't' in request.GET
        logging.info(f"Starting game with restart={is_restart}")

        # Get or create game session from database
        game_session = get_or_create_game_session(request)

        # Ensure the game session is saved to database before proceeding
        if not game_session.pk:
            game_session.save()
            logging.info(f"Saved game session to database with ID: {game_session.id}")

        # CRITICAL STEP: Delete all existing messages for this session
        if is_restart and game_session.pk:
            logging.info(f"Restart requested - deleting all existing messages for game session {game_session.id}")
            Message.objects.filter(game_session=game_session).delete()
            logging.info(f"Deleted all messages for game session {game_session.id}")

        # RESET ALL DATABASE FIELDS to initial state
        game_session.current_role = "applicant"           # Reset role progression
        game_session.performance_score = 0                # Reset score to zero
        game_session.challenges_completed = 0             # Reset challenge count
        game_session.role_challenges_completed = 0        # Reset role-specific challenges
        game_session.game_completed = False               # Mark as not completed
        game_session.current_manager = "hr"               # Reset to HR manager
        game_session.current_task = "cover_letter"        # Reset to first task
        game_session.set_completed_roles([])              # Clear completed roles list
        game_session.first_task_pending = True            # Reset task flags

        # Handle optional fields
        if hasattr(game_session, 'next_task_pending'):
            game_session.next_task_pending = False

        # SAVE to database - this persists the reset
        game_session.save()

        # Delete all existing messages for this session (if not already done)
        if not is_restart:
            message_count_before = game_session.messages.count()
            if message_count_before > 0:
                logging.info(f"Deleting {message_count_before} messages for game session {game_session.id}")
                deleted_count = Message.objects.filter(game_session=game_session).delete()[0]
                logging.info(f"Deleted {deleted_count} messages using Django ORM")

        # Return fresh state to frontend
        return JsonResponse({
            'success': True,
            'current_role': game_session.current_role,
            'performance_score': game_session.performance_score,
            'challenges_completed': game_session.challenges_completed,
            'game_completed': game_session.game_completed,
            'current_manager': game_session.current_manager,
            'current_task': game_session.current_task,
            'completed_roles': game_session.get_completed_roles(),
            'first_task_pending': game_session.first_task_pending,
            'next_task_pending': getattr(game_session, 'next_task_pending', False)
        })

    except Exception as e:
        logging.error(f"Error starting game: {str(e)}")
        return JsonResponse({'success': False, 'message': str(e)}, status=500)
```

### Game State Initialization (`game/game_state.py`)

```python
def initialize_game_state():
    """
    Initialize a completely fresh game state.
    This defines what "reset" means for the game.
    """
    game_state = {
        "current_role": "applicant",              # Starting role
        "performance_score": 0,                   # Zero score
        "challenges_completed": 0,                # No challenges completed
        "role_challenges_completed": 0,           # No role challenges completed
        "messages": [],                           # Empty message history
        "game_completed": False,                  # Game not completed
        "current_manager": "hr",                  # Start with HR manager
        "current_task": "cover_letter",           # First task
        "completed_roles": [],                    # No roles completed
        "first_task_pending": True                # First task should be delayed
    }

    return game_state
```

### Database Helper Functions

```python
# Helper function to handle completed roles as JSON
class GameSession(models.Model):
    # ... other fields ...

    def get_completed_roles(self):
        """Get completed roles as a list from JSON string"""
        try:
            return json.loads(self.completed_roles)
        except (json.JSONDecodeError, TypeError):
            return []

    def set_completed_roles(self, roles_list):
        """Set completed roles from a list to JSON string"""
        self.completed_roles = json.dumps(roles_list)

    def add_completed_role(self, role):
        """Add a role to the completed roles list"""
        roles = self.get_completed_roles()
        if role not in roles:
            roles.append(role)
            self.set_completed_roles(roles)

    def reset_to_defaults(self):
        """Reset all game fields to their default values"""
        self.current_role = "applicant"
        self.performance_score = 0
        self.challenges_completed = 0
        self.role_challenges_completed = 0
        self.game_completed = False
        self.current_manager = "hr"
        self.current_task = "cover_letter"
        self.set_completed_roles([])
        self.first_task_pending = True
        self.next_task_pending = False

# Message model helper functions
class Message(models.Model):
    # ... other fields ...

    def to_dict(self):
        """Convert message model to dictionary for API response"""
        result = {
            "id": self.message_id,
            "sender": self.sender,
            "text": self.text,
            "html": self.html,
            "timestamp": self.timestamp.isoformat(),
            "is_challenge": self.is_challenge,
            "is_markdown": self.is_markdown
        }

        # Add optional fields if they exist
        try:
            result["is_promotion"] = self.is_promotion
        except AttributeError:
            result["is_promotion"] = False

        if self.task_id:
            result["task_id"] = self.task_id

        return result

    @classmethod
    def clear_session_messages(cls, game_session):
        """Clear all messages for a specific game session"""
        return cls.objects.filter(game_session=game_session).delete()
```

### Cleanup and Maintenance

```python
# Management command for cleaning up old anonymous sessions
# game/management/commands/cleanup_anonymous_players.py

def cleanup_anonymous_sessions(hours=24, dry_run=False):
    """
    Clean up anonymous game sessions older than specified hours
    """
    from django.utils import timezone
    from datetime import timedelta

    cutoff_time = timezone.now() - timedelta(hours=hours)

    # Find anonymous sessions older than cutoff time
    anonymous_sessions = GameSession.objects.filter(
        user__isnull=True,  # Anonymous users
        updated_at__lt=cutoff_time
    )

    count = anonymous_sessions.count()

    if count > 0:
        if not dry_run:
            # Delete the sessions (cascades to messages due to foreign key)
            deleted_count, details = anonymous_sessions.delete()

            logging.info(f"Cleaned up {deleted_count} anonymous game sessions older than {hours} hours")

            # Log details of what was deleted
            for model, count in details.items():
                logging.info(f"  - {count} {model} objects deleted")

            return deleted_count
        else:
            logging.info(f"Dry run - would have deleted {count} anonymous game sessions")
            return count

    return 0
```

## 3. Organization Chart Reset Mechanics

### Org Chart State Reset (`game/static/game/js/org-chart.js`)

```javascript
// Reset the organizational chart game state
function resetGame() {
    // Reset the org chart state object
    orgChartState = {
        currentPosition: "applicant",      // Back to starting position
        completedTasks: [],               // Clear all completed tasks
        completedPositions: [],           // Clear all completed positions
        currentTaskIndex: 0               // Reset task index to first task
    };

    // Save the reset state to localStorage
    saveGameState();

    // Reinitialize the org chart with fresh state
    initOrgChart();
}

// Save state to localStorage for persistence
function saveGameState() {
    localStorage.setItem('orgChartState', JSON.stringify(orgChartState));
}

// Initialize org chart from saved or default state
function initGameState() {
    const savedState = localStorage.getItem('orgChartState');
    if (savedState) {
        orgChartState = JSON.parse(savedState);
    } else {
        // Use default initial state if no saved state
        orgChartState = {
            currentPosition: "applicant",
            completedTasks: [],
            completedPositions: [],
            currentTaskIndex: 0
        };
    }
}
```

## 4. Reset Confirmation and User Experience

### Confirmation Modal Implementation

```javascript
// Show restart confirmation modal with proper event handling
function showRestartConfirmation() {
    const restartConfirmationModal = document.getElementById('restart-confirmation-modal');
    if (restartConfirmationModal) {
        restartConfirmationModal.classList.remove('hidden');

        // Handle confirmation button
        const confirmButton = document.getElementById('confirm-restart-button');
        if (confirmButton) {
            // Clone button to remove any existing event listeners
            const newConfirmButton = confirmButton.cloneNode(true);
            confirmButton.parentNode.replaceChild(newConfirmButton, confirmButton);

            // Add fresh event listener
            newConfirmButton.addEventListener('click', () => {
                console.log('Restart confirmed by user');
                restartConfirmationModal.classList.add('hidden');

                // Execute the actual reset
                performCompleteReset();
            });
        }
    }
}

// The actual reset function that does all the work
function performCompleteReset() {
    // 1. Clear UI immediately for instant feedback
    if (elements.messagesContainer) {
        elements.messagesContainer.innerHTML = '';
    }

    // 2. Reset JavaScript game state
    gameState.messages = [];

    // 3. Call backend to reset database
    startGame(); // This makes the API call to reset backend
}
```

## How to Replicate This Reset System

### Step 1: Define Your Reset Scope

First, identify what needs to be reset in your application:

```javascript
// Define what constitutes "reset" for your application
const resetScope = {
    // Frontend state
    uiElements: ['message-container', 'input-field', 'progress-bar'],
    jsVariables: ['gameState', 'userProgress', 'currentLevel'],
    localStorage: ['gameData', 'userPreferences'],
    sessionStorage: ['tempData', 'currentSession'],

    // Backend state
    databaseFields: ['score', 'level', 'completed_tasks'],
    sessionData: ['user_session', 'game_session'],

    // UI state
    modalDialogs: ['game-modal', 'settings-modal'],
    formInputs: ['user-input', 'settings-form'],
    visualElements: ['charts', 'progress-indicators']
};
```

### Step 2: Create the Reset Function Architecture

```javascript
// Main reset orchestrator function
async function resetApplication() {
    try {
        // Step 1: Show loading state
        showLoadingIndicator();

        // Step 2: Reset frontend immediately
        resetFrontendState();

        // Step 3: Reset backend via API
        await resetBackendState();

        // Step 4: Reinitialize application
        await initializeApplication();

        // Step 5: Update UI with fresh state
        updateUIWithFreshState();

    } catch (error) {
        console.error('Reset failed:', error);
        handleResetError(error);
    } finally {
        hideLoadingIndicator();
    }
}

// Frontend reset function
function resetFrontendState() {
    // Clear all JavaScript variables
    window.appState = getInitialState();

    // Clear storage
    localStorage.removeItem('appData');
    sessionStorage.clear();

    // Reset UI elements
    document.querySelectorAll('.user-content').forEach(el => {
        el.innerHTML = '';
    });

    // Reset form inputs
    document.querySelectorAll('input, textarea, select').forEach(input => {
        input.value = input.defaultValue || '';
    });
}

// Backend reset function
async function resetBackendState() {
    const response = await fetch('/api/reset', {
        method: 'POST',
        headers: {
            'Content-Type': 'application/json',
            'Cache-Control': 'no-cache'
        },
        body: JSON.stringify({ resetType: 'complete' })
    });

    if (!response.ok) {
        throw new Error(`Reset failed: ${response.status}`);
    }

    return response.json();
}
```

### Step 3: Backend Reset Implementation

```python
# Django view for handling reset
def reset_application(request):
    """
    Reset all application state for the current user/session
    """
    try:
        if request.method == 'POST':
            # Get user session or create new one
            user_session = get_or_create_user_session(request)

            # Reset all relevant database fields
            user_session.score = 0
            user_session.level = 1
            user_session.completed_tasks = []
            user_session.current_progress = 0
            user_session.is_completed = False
            user_session.last_activity = timezone.now()

            # Clear any related data
            user_session.game_data = {}
            user_session.preferences = get_default_preferences()

            # Save to database
            user_session.save()

            # Return fresh state
            return JsonResponse({
                'success': True,
                'state': {
                    'score': user_session.score,
                    'level': user_session.level,
                    'completed_tasks': user_session.completed_tasks,
                    'current_progress': user_session.current_progress
                }
            })

    except Exception as e:
        return JsonResponse({
            'success': False,
            'error': str(e)
        }, status=500)
```

### Step 4: Error Handling and Recovery

```javascript
// Robust error handling for reset operations
function handleResetError(error) {
    console.error('Reset operation failed:', error);

    // Try to recover gracefully
    try {
        // Attempt partial reset if full reset failed
        resetFrontendState();

        // Show user-friendly error message
        showErrorMessage('Reset partially completed. Please refresh the page.');

    } catch (recoveryError) {
        console.error('Recovery also failed:', recoveryError);

        // Last resort: suggest page refresh
        if (confirm('Reset failed. Would you like to refresh the page?')) {
            window.location.reload();
        }
    }
}

// Validation before reset
function validateResetConditions() {
    // Check if reset is safe to perform
    const checks = [
        () => document.readyState === 'complete',
        () => !window.operationInProgress,
        () => navigator.onLine
    ];

    return checks.every(check => check());
}
```

## Key Functional Principles

1. **Immediate UI Feedback**: Clear UI elements first for instant user feedback
2. **State Synchronization**: Ensure frontend and backend state match after reset
3. **Error Recovery**: Always have fallback mechanisms for failed resets
4. **Validation**: Check conditions before allowing reset operations
5. **Logging**: Track reset operations for debugging and analytics
6. **User Confirmation**: Always confirm destructive operations
7. **Progressive Reset**: Reset in stages (UI → Frontend → Backend → Reinitialize)

This functional approach ensures reliable, user-friendly reset behavior that maintains data integrity and provides excellent user experience.
