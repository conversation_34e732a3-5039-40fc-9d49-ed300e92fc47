from django.shortcuts import redirect, get_object_or_404, render
from django.urls import reverse_lazy, reverse
from django.views.generic import TemplateView, ListView, View, DetailView
from django.contrib.auth.decorators import user_passes_test
from django.utils.decorators import method_decorator
from django.contrib import messages
from django.contrib.auth import get_user_model, login, logout
from django.core.paginator import Paginator
import sys
import django
from django.utils.translation import gettext_lazy as _
from django.utils import timezone
from django.db.models import Q, Count, Sum, Avg, Max
from django.db import connection
from django.contrib.auth.models import Group, User

from game.models import GameSession, Message
from corporate.models import Company, Leaderboard, LeaderboardEntry, CorporateUser

# Decorator for checking superuser status
superuser_required = method_decorator(user_passes_test(lambda u: u.is_superuser), name='dispatch')

@superuser_required
class GameSessionListView(ListView):
    """
    Displays a list of all game sessions with filtering options.
    """
    model = GameSession
    template_name = 'superadmin/game_session_list.html'
    context_object_name = 'game_sessions'
    paginate_by = 25

    def get_queryset(self):
        queryset = super().get_queryset().select_related('user', 'company').order_by('-created_at')

        # Filtering & Searching
        company_id = self.request.GET.get('company')
        status = self.request.GET.get('status')
        query = self.request.GET.get('q')  # Get search query

        if query:
            # Search by user username or company name
            queryset = queryset.filter(
                Q(user__username__icontains=query) |
                Q(company__name__icontains=query)
            )

        if company_id:
            queryset = queryset.filter(company_id=company_id)

        if status == 'active':
            # Active sessions are those that are not completed
            queryset = queryset.filter(game_completed=False)
        elif status == 'completed':
            queryset = queryset.filter(game_completed=True)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Game Sessions")

        # Get all companies for filtering
        context['companies'] = Company.objects.all().order_by('name')

        # Pass filter values back to template
        context['filter_company'] = self.request.GET.get('company', '')
        context['filter_status'] = self.request.GET.get('status', '')
        context['filter_q'] = self.request.GET.get('q', '')

        # Get some statistics
        context['total_sessions'] = GameSession.objects.count()
        context['active_sessions'] = GameSession.objects.filter(game_completed=False).count()
        context['completed_sessions'] = GameSession.objects.filter(game_completed=True).count()

        return context

@superuser_required
class GameSessionDetailView(DetailView):
    """
    Displays details of a specific game session.
    """
    model = GameSession
    template_name = 'superadmin/game_session_detail.html'
    context_object_name = 'game_session'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        game_session = self.get_object()

        context['title'] = f"Game Session: {game_session.user.username}"

        # Get messages for this session
        context['messages'] = Message.objects.filter(
            game_session=game_session
        ).order_by('timestamp')

        # Get leaderboard entries for this user
        entries = LeaderboardEntry.objects.filter(
            user=game_session.user
        ).select_related('leaderboard').order_by('-score')

        # Add rank to each entry
        for entry in entries:
            # Get the rank by counting entries with higher scores
            rank = LeaderboardEntry.objects.filter(
                leaderboard=entry.leaderboard,
                score__gt=entry.score
            ).count() + 1
            entry.rank = rank

        context['leaderboard_entries'] = entries

        return context

@superuser_required
class LeaderboardListView(ListView):
    """
    Displays a list of all leaderboards with filtering options.
    """
    model = Leaderboard
    template_name = 'superadmin/leaderboard_list.html'
    context_object_name = 'leaderboards'
    paginate_by = 25

    def get_queryset(self):
        queryset = super().get_queryset().select_related('company')

        # Filtering & Searching
        company_id = self.request.GET.get('company')
        time_period = self.request.GET.get('time_period')
        query = self.request.GET.get('q')  # Get search query

        if query:
            # Search by leaderboard name or company name
            queryset = queryset.filter(
                Q(name__icontains=query) |
                Q(company__name__icontains=query)
            )

        if company_id:
            queryset = queryset.filter(company_id=company_id)

        if time_period:
            queryset = queryset.filter(time_period=time_period)

        # Group by company - only show one leaderboard per company (the most recent one)
        # unless specific time_period is selected
        if not time_period:
            # Get the most recent leaderboard for each company
            company_ids = queryset.values_list('company_id', flat=True).distinct()
            latest_leaderboards = []

            for company_id in company_ids:
                company_leaderboards = queryset.filter(company_id=company_id).order_by('-created_at')
                if company_leaderboards.exists():
                    latest_leaderboards.append(company_leaderboards.first().id)

            queryset = queryset.filter(id__in=latest_leaderboards)

        # Annotate with entry count and order by company name then created date
        queryset = queryset.annotate(entry_count=Count('entries')).order_by('company__name', '-created_at')

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Leaderboards")

        # Get all companies for filtering
        context['companies'] = Company.objects.all().order_by('name')

        # Get time period choices
        context['time_periods'] = [
            ('all_time', _('All Time')),
            ('monthly', _('Monthly')),
            ('weekly', _('Weekly')),
            ('daily', _('Daily')),
        ]

        # Pass filter values back to template
        context['filter_company'] = self.request.GET.get('company', '')
        context['filter_time_period'] = self.request.GET.get('time_period', '')
        context['filter_q'] = self.request.GET.get('q', '')

        # Get some statistics
        context['total_leaderboards'] = Leaderboard.objects.count()
        context['total_entries'] = LeaderboardEntry.objects.count()

        # Add more detailed statistics
        context['global_leaderboards'] = Leaderboard.objects.filter(is_global=True).count()
        context['company_leaderboards'] = Leaderboard.objects.filter(is_global=False).count()
        context['active_leaderboards'] = Leaderboard.objects.filter(active=True).count()

        # Get top companies by leaderboard entries
        top_companies = Company.objects.annotate(
            entry_count=Count('leaderboards__entries')
        ).order_by('-entry_count')[:5]
        context['top_companies'] = top_companies

        return context

@superuser_required
class LeaderboardDetailView(DetailView):
    """
    Displays details of a specific leaderboard.
    """
    model = Leaderboard
    template_name = 'superadmin/leaderboard_detail.html'
    context_object_name = 'leaderboard'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        leaderboard = self.get_object()

        context['title'] = f"Leaderboard: {leaderboard.name}"

        # Get entries for this leaderboard
        entries = LeaderboardEntry.objects.filter(
            leaderboard=leaderboard
        ).select_related('user').order_by('-score')

        # Create a dictionary of existing entries keyed by user_id for quick lookup
        existing_entries_by_user = {entry.user_id: entry for entry in entries}

        # Add rank to each entry
        ranked_entries = []
        rank = 1

        # If this is a company leaderboard, get all users from this company
        if leaderboard.company:
            # Get all users from this company
            company_users = User.objects.filter(
                corporate_profile__company=leaderboard.company
            ).select_related('corporate_profile')

            # Create a list of all entries, including users who don't have entries yet
            all_entries = []

            for user in company_users:
                if user.id in existing_entries_by_user:
                    # User has an entry, use it
                    entry = existing_entries_by_user[user.id]
                else:
                    # User doesn't have an entry, create a placeholder
                    entry = LeaderboardEntry(
                        leaderboard=leaderboard,
                        user=user,
                        score=0,
                        highest_role="Not started"
                    )
                    # Add a recorded_at attribute to avoid template errors
                    from django.utils import timezone
                    entry.recorded_at = timezone.now()

                # Add rank attribute
                entry.rank = rank
                rank += 1
                all_entries.append(entry)

            # Sort by score (descending)
            all_entries.sort(key=lambda x: x.score, reverse=True)

            # Re-assign ranks after sorting
            for i, entry in enumerate(all_entries, 1):
                entry.rank = i

            ranked_entries = all_entries
        else:
            # For non-company leaderboards, just use the existing entries
            for i, entry in enumerate(entries, 1):
                entry.rank = i  # Add a rank attribute to each entry
                ranked_entries.append(entry)

        paginator = Paginator(ranked_entries, 25)
        page_number = self.request.GET.get('page', 1)
        page_obj = paginator.get_page(page_number)

        context['entries'] = page_obj

        # Get statistics
        context['entry_count'] = len(ranked_entries)

        # Calculate average and max scores only from real entries (not placeholders)
        if entries:
            context['avg_score'] = entries.aggregate(Avg('score'))['score__avg']
            context['max_score'] = entries.aggregate(Max('score'))['score__max']
        else:
            context['avg_score'] = 0
            context['max_score'] = 0

        # Add more detailed statistics
        if leaderboard.company:
            # Get company information
            context['company'] = leaderboard.company
            context['company_user_count'] = User.objects.filter(
                corporate_profile__company=leaderboard.company
            ).count()

            # Get other time periods for this company's leaderboards
            # Instead of showing all leaderboards, group by time period
            time_periods = Leaderboard.objects.filter(
                company=leaderboard.company
            ).exclude(time_period=leaderboard.time_period).values_list('time_period', flat=True).distinct()

            other_time_periods = []
            for period in time_periods:
                # Get the most recent leaderboard for each time period
                period_leaderboard = Leaderboard.objects.filter(
                    company=leaderboard.company,
                    time_period=period
                ).order_by('-created_at').first()

                if period_leaderboard:
                    other_time_periods.append({
                        'id': period_leaderboard.id,
                        'time_period': period,
                        'display_name': dict(Leaderboard.PERIOD_CHOICES).get(period, period),
                        'entry_count': LeaderboardEntry.objects.filter(leaderboard=period_leaderboard).count()
                    })

            context['other_time_periods'] = other_time_periods

        # Get top roles from entries (only from real entries, not placeholders)
        top_roles = entries.values('highest_role').annotate(
            count=Count('id')
        ).order_by('-count')[:5]
        context['top_roles'] = top_roles

        return context

@superuser_required
class DashboardView(TemplateView):
    """
    Displays the main superadmin dashboard with system information and key metrics.
    """
    template_name = 'superadmin/dashboard.html'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Game Superadmin Dashboard")

        # System information
        context['django_version'] = django.get_version()
        context['python_version'] = f"{sys.version_info.major}.{sys.version_info.minor}.{sys.version_info.micro}"

        # Database information
        db_engine = connection.vendor
        if db_engine == 'sqlite':
            db_name = 'SQLite'
        elif db_engine == 'postgresql':
            db_name = 'PostgreSQL'
        elif db_engine == 'mysql':
            db_name = 'MySQL'
        else:
            db_name = db_engine.capitalize()
        context['database_engine'] = db_name

        # Total counts
        User = get_user_model()

        context['total_companies'] = Company.objects.count()
        context['total_users'] = User.objects.count()
        context['total_game_sessions'] = GameSession.objects.count()
        context['total_leaderboard_entries'] = LeaderboardEntry.objects.count()

        # Add pending company approvals count
        context['pending_company_approvals'] = Company.objects.filter(pending_approval=True).count()

        return context

@superuser_required
class CompanyListView(ListView):
    """
    Displays a list of all companies with filtering options.
    """
    model = Company
    template_name = 'superadmin/company_list.html'
    context_object_name = 'companies'
    paginate_by = 25 # Optional: Add pagination

    def get_queryset(self):
        queryset = super().get_queryset().select_related('owner').order_by('-created_at')

        # Filtering & Searching
        status = self.request.GET.get('status')
        has_sessions = self.request.GET.get('has_sessions')
        approval = self.request.GET.get('approval')
        query = self.request.GET.get('q') # Get search query

        if query:
            # Search by company name or owner username
            queryset = queryset.filter(
                Q(name__icontains=query) | Q(owner__username__icontains=query)
            )

        # Filter by approval status
        if approval == 'pending':
            queryset = queryset.filter(pending_approval=True)
        elif approval == 'approved':
            queryset = queryset.filter(pending_approval=False)

        # Filter by active status
        if status == 'inactive':
            queryset = queryset.filter(is_active=False)
        elif status == 'active':
            queryset = queryset.filter(is_active=True)

        if has_sessions == 'true':
            # Get companies with game sessions
            companies_with_sessions = GameSession.objects.values_list('company_id', flat=True).distinct()
            queryset = queryset.filter(id__in=companies_with_sessions)
        elif has_sessions == 'false':
            # Get companies without game sessions
            companies_with_sessions = GameSession.objects.values_list('company_id', flat=True).distinct()
            queryset = queryset.exclude(id__in=companies_with_sessions)

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Manage Companies")
        # Pass filter values back to template for display/form persistence
        context['filter_status'] = self.request.GET.get('status', '')
        context['filter_has_sessions'] = self.request.GET.get('has_sessions', '')
        context['filter_approval'] = self.request.GET.get('approval', '')
        context['filter_q'] = self.request.GET.get('q', '') # Pass search query back

        # Add counts for pending approvals
        context['pending_approvals_count'] = Company.objects.filter(pending_approval=True).count()
        return context

@superuser_required
class CompanyDetailView(DetailView):
    """
    Displays details of a specific company.
    """
    model = Company
    template_name = 'superadmin/company_detail.html'
    context_object_name = 'company'

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        company = self.get_object()

        context['title'] = f"Company: {company.name}"

        # Get users for this company
        context['company_users'] = User.objects.filter(
            corporate_profile__company=company
        ).select_related('corporate_profile').order_by('username')

        # Get leaderboards for this company
        context['leaderboards'] = Leaderboard.objects.filter(
            company=company
        ).annotate(entry_count=Count('entries')).order_by('-created_at')

        # Get game sessions for this company
        # GameSession uses corporate.models.Company directly, so we can use the same company object
        context['game_sessions'] = GameSession.objects.filter(
            company=company
        ).select_related('user').order_by('-created_at')[:10]

        # Get statistics
        context['user_count'] = context['company_users'].count()
        context['leaderboard_count'] = context['leaderboards'].count()
        context['game_session_count'] = GameSession.objects.filter(company=company).count()

        # Get top users by score
        top_users = User.objects.filter(
            leaderboard_entries__leaderboard__company=company
        ).annotate(
            total_score=Sum('leaderboard_entries__score'),
            entry_count=Count('leaderboard_entries')
        ).order_by('-total_score')[:5]
        context['top_users'] = top_users

        return context

@superuser_required
class CompanyActivateToggleView(View):
    """
    Toggles the active status of a Company via POST request.
    Also handles approval of pending companies.
    """
    http_method_names = ['post'] # Only allow POST

    def post(self, request, **kwargs):
        company = get_object_or_404(Company, pk=kwargs.get('pk'))

        # Check if this is a pending approval
        if company.pending_approval:
            # Approve the company
            company.pending_approval = False
            company.is_active = True
            company.approved_at = timezone.now()
            company.approved_by = request.user
            company.save(update_fields=['pending_approval', 'is_active', 'approved_at', 'approved_by'])

            messages.success(request, _("Company '{name}' has been approved and activated.").format(name=company.name))
        else:
            # Toggle active status
            company.is_active = not company.is_active
            company.save(update_fields=['is_active'])

            if company.is_active:
                messages.success(request, _("Company '{name}' activated successfully.").format(name=company.name))
            else:
                messages.warning(request, _("Company '{name}' deactivated.").format(name=company.name))

        # Redirect back to the company list, potentially preserving filters
        redirect_url = reverse_lazy('superadmin:company_list')
        query_params = request.GET.urlencode() # Get original query params
        if query_params:
            redirect_url += f'?{query_params}'

        return redirect(redirect_url)

# StopImpersonateView removed - now using django-impersonate's built-in functionality


@superuser_required
class CompanyImpersonateView(View):
    """
    Initiates impersonation of a company owner (user) via POST request.
    Uses django-impersonate for handling the impersonation.
    """
    http_method_names = ['post']

    def post(self, request, **kwargs):
        user_pk_to_impersonate = kwargs.get('user_pk')
        print(f"CompanyImpersonateView called for user_pk: {user_pk_to_impersonate}")

        # Get the user to impersonate
        User = get_user_model()
        try:
            user_to_impersonate = User.objects.get(pk=user_pk_to_impersonate)
            print(f"Found user to impersonate: {user_to_impersonate.username}")
        except User.DoesNotExist:
            messages.error(request, _("User not found."))
            return redirect('superadmin:company_list')

        # Redirect to django-impersonate's start URL
        impersonate_url = reverse('impersonate-start', args=[user_pk_to_impersonate])

        # If the user has a corporate profile, add a query parameter to set the active company
        try:
            corporate_profile = user_to_impersonate.corporate_profile
            company_id = str(corporate_profile.company.id)
            print(f"User has corporate profile with company ID: {company_id}")
            impersonate_url += f"?active_company_id={company_id}"
        except Exception as e:
            print(f"Error getting company ID: {str(e)}")
            pass

        print(f"Redirecting to: {impersonate_url}")
        return redirect(impersonate_url)


@superuser_required
class UserListView(ListView):
    """
    Displays a list of all users with filtering options.
    """
    model = get_user_model()
    template_name = 'superadmin/user_list.html'
    context_object_name = 'users'
    paginate_by = 25

    def get_queryset(self):
        User = get_user_model()
        queryset = User.objects.all().order_by('-date_joined')

        # Filtering & Searching
        status = self.request.GET.get('status')
        role = self.request.GET.get('role')
        company = self.request.GET.get('company')
        query = self.request.GET.get('q')  # Get search query

        if query:
            # Search by username, email, first name, or last name
            queryset = queryset.filter(
                Q(username__icontains=query) |
                Q(email__icontains=query) |
                Q(first_name__icontains=query) |
                Q(last_name__icontains=query)
            )

        if status == 'active':
            queryset = queryset.filter(is_active=True)
        elif status == 'inactive':
            queryset = queryset.filter(is_active=False)

        if role == 'superuser':
            queryset = queryset.filter(is_superuser=True)
        elif role == 'staff':
            queryset = queryset.filter(is_staff=True, is_superuser=False)
        elif role == 'regular':
            queryset = queryset.filter(is_staff=False, is_superuser=False)

        # Filter by company if specified
        if company:
            # Find users who are owners of the specified company OR have a corporate profile with this company
            queryset = queryset.filter(
                Q(owned_corporate_companies__id=company) |      # Users who own the company
                Q(corporate_profile__company__id=company)       # Users who are members of the company
            ).distinct()

        # Annotate with game session count and company information
        queryset = queryset.annotate(
            session_count=Count('gamesession', distinct=True),
            leaderboard_count=Count('leaderboard_entries', distinct=True),
            company_count=Count('owned_corporate_companies', distinct=True)
        )

        return queryset

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        context['title'] = _("Manage Users")

        # Pass filter values back to template
        context['filter_status'] = self.request.GET.get('status', '')
        context['filter_role'] = self.request.GET.get('role', '')
        context['filter_company'] = self.request.GET.get('company', '')
        context['filter_q'] = self.request.GET.get('q', '')

        # Get some statistics
        User = get_user_model()
        context['total_users'] = User.objects.count()
        context['active_users'] = User.objects.filter(is_active=True).count()
        context['superusers'] = User.objects.filter(is_superuser=True).count()
        context['staff_users'] = User.objects.filter(is_staff=True, is_superuser=False).count()

        # Get all companies for the company filter dropdown
        context['companies'] = Company.objects.all().order_by('name')

        return context


@superuser_required
class UserDetailView(DetailView):
    """
    Displays details of a specific user.
    """
    model = get_user_model()
    template_name = 'superadmin/user_detail.html'
    context_object_name = 'user_obj'  # Use 'user_obj' to avoid conflict with request.user

    def get_context_data(self, **kwargs):
        context = super().get_context_data(**kwargs)
        user_obj = self.get_object()

        context['title'] = f"User: {user_obj.username}"

        # Get corporate profile if exists
        try:
            context['corporate_profile'] = user_obj.corporate_profile
        except:
            context['corporate_profile'] = None

        # Get game sessions for this user
        context['game_sessions'] = GameSession.objects.filter(
            user=user_obj
        ).select_related('company').order_by('-created_at')[:10]  # Limit to 10 most recent

        # Get leaderboard entries for this user (all entries, not just top 10)
        entries = LeaderboardEntry.objects.filter(
            user=user_obj
        ).select_related('leaderboard').order_by('-score')

        # Add rank to each entry
        for entry in entries:
            # Get the rank by counting entries with higher scores
            rank = LeaderboardEntry.objects.filter(
                leaderboard=entry.leaderboard,
                score__gt=entry.score
            ).count() + 1
            entry.rank = rank

        context['leaderboard_entries'] = entries

        # Get companies owned by this user
        context['owned_companies'] = Company.objects.filter(owner=user_obj)

        return context


@superuser_required
class UserToggleActiveView(View):
    """
    Toggles the is_active status of a User via POST request.
    """
    http_method_names = ['post']

    def post(self, request, **kwargs):
        User = get_user_model()
        user = get_object_or_404(User, pk=kwargs.get('pk'))

        # Don't allow deactivating yourself
        if user == request.user:
            messages.error(request, _("You cannot deactivate your own account."))
            return redirect('superadmin:user_detail', pk=user.pk)

        user.is_active = not user.is_active
        user.save(update_fields=['is_active'])

        if user.is_active:
            messages.success(request, _("User '{username}' activated successfully.").format(username=user.username))
        else:
            messages.warning(request, _("User '{username}' deactivated.").format(username=user.username))

        # Redirect back to the user detail page
        return redirect('superadmin:user_detail', pk=user.pk)


@superuser_required
class UserToggleStaffView(View):
    """
    Toggles the is_staff status of a User via POST request.
    """
    http_method_names = ['post']

    def post(self, request, **kwargs):
        User = get_user_model()
        user = get_object_or_404(User, pk=kwargs.get('pk'))

        # Don't allow changing yourself
        if user == request.user:
            messages.error(request, _("You cannot change your own staff status."))
            return redirect('superadmin:user_detail', pk=user.pk)

        user.is_staff = not user.is_staff
        user.save(update_fields=['is_staff'])

        if user.is_staff:
            messages.success(request, _("User '{username}' granted staff status.").format(username=user.username))
        else:
            messages.warning(request, _("User '{username}' staff status revoked.").format(username=user.username))

        # Redirect back to the user detail page
        return redirect('superadmin:user_detail', pk=user.pk)


@superuser_required
class UserToggleSuperuserView(View):
    """
    Toggles the is_superuser status of a User via POST request.
    """
    http_method_names = ['post']

    def post(self, request, **kwargs):
        User = get_user_model()
        user = get_object_or_404(User, pk=kwargs.get('pk'))

        # Don't allow changing yourself
        if user == request.user:
            messages.error(request, _("You cannot change your own superuser status."))
            return redirect('superadmin:user_detail', pk=user.pk)

        user.is_superuser = not user.is_superuser
        user.save(update_fields=['is_superuser'])

        if user.is_superuser:
            messages.success(request, _("User '{username}' granted superuser status.").format(username=user.username))
        else:
            messages.warning(request, _("User '{username}' superuser status revoked.").format(username=user.username))

        # Redirect back to the user detail page
        return redirect('superadmin:user_detail', pk=user.pk)
