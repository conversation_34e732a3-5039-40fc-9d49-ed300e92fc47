#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to verify the fix is working by checking what messages should be displayed.
"""
import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from game.models import GameSession, Message

def test_fix():
    """Test what messages should be displayed after the fix"""
    print("Testing message display after fix...")
    
    # Get the most recent game session
    session = GameSession.objects.last()
    if not session:
        print("No game sessions found")
        return
    
    print(f"Current game state:")
    print(f"  Role: {session.current_role}")
    print(f"  Current task: {session.current_task}")
    print(f"  Role challenges completed: {session.role_challenges_completed}")
    
    # Get all messages for this session
    all_messages = session.messages.all().order_by('timestamp')
    print(f"\nAll messages ({len(all_messages)}):")
    
    for msg in all_messages:
        msg_type = "CHALLENGE" if msg.is_challenge else "REGULAR"
        task_info = f" (task: {msg.task_id})" if msg.task_id else ""
        print(f"  {msg.timestamp.strftime('%H:%M:%S')} - {msg.sender} [{msg_type}]{task_info}: {msg.text[:60]}...")
    
    # Check specifically for onboarding_checklist messages
    onboarding_messages = all_messages.filter(task_id="onboarding_checklist")
    print(f"\nOnboarding checklist messages ({len(onboarding_messages)}):")
    for msg in onboarding_messages:
        print(f"  {msg.timestamp.strftime('%H:%M:%S')} - {msg.sender}: {msg.text[:100]}...")
    
    print(f"\nWith the fix, the onboarding_checklist message should now be displayed")
    print(f"because we removed the task filtering logic that was blocking it.")

if __name__ == "__main__":
    test_fix()
