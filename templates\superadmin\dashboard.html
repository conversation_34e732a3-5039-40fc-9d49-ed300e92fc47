{% extends "superadmin/base_superadmin.html" %}
{% load static %}
{% load i18n %}

{% block title %}Dashboard - Superadmin{% endblock %}

{% block breadcrumbs %}
<li class="breadcrumb-item active" aria-current="page">{% trans "Dashboard" %}</li>
{% endblock %}

{% block superadmin_content %}
<!-- Enhanced Welcome Section -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card border-0 shadow-lg">
            <div class="card-body bg-gradient-danger text-white rounded-3 p-4">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-4">
                        <i class="fas fa-shield-alt fa-2x text-white"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h2 class="mb-2 fw-bold">Superadmin Dashboard</h2>
                        <p class="mb-0 text-white-75 fs-5">
                            Complete platform oversight and management control center
                        </p>
                        <div class="mt-3">
                            <span class="badge bg-white text-danger me-2">
                                <i class="fas fa-clock me-1"></i>{{ "now"|date:"M d, Y - H:i" }}
                            </span>
                            <span class="badge bg-white bg-opacity-25 text-white">
                                <i class="fas fa-user me-1"></i>{{ request.user.username }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Enhanced Stats Cards -->
<div class="row mb-4">
    <!-- Companies Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h6 class="text-muted text-uppercase fw-bold mb-1">Companies</h6>
                        <h2 class="mb-0 fw-bold text-primary">{{ total_companies|default:"0" }}</h2>
                    </div>
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-building text-primary fa-lg"></i>
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-between">
                    <small class="text-muted">
                        <i class="fas fa-chart-line me-1"></i>Total registered
                    </small>
                    <a href="{% url 'superadmin:company_list' %}" class="btn btn-primary btn-sm">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Game Sessions Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h6 class="text-muted text-uppercase fw-bold mb-1">Game Sessions</h6>
                        <h2 class="mb-0 fw-bold text-success">{{ total_game_sessions|default:"0" }}</h2>
                    </div>
                    <div class="bg-success bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-gamepad text-success fa-lg"></i>
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-between">
                    <small class="text-muted">
                        <i class="fas fa-play me-1"></i>Total sessions
                    </small>
                    <a href="{% url 'superadmin:game_session_list' %}" class="btn btn-success btn-sm">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Users Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h6 class="text-muted text-uppercase fw-bold mb-1">Users</h6>
                        <h2 class="mb-0 fw-bold text-info">{{ total_users|default:"0" }}</h2>
                    </div>
                    <div class="bg-info bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-users text-info fa-lg"></i>
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-between">
                    <small class="text-muted">
                        <i class="fas fa-user-plus me-1"></i>Registered users
                    </small>
                    <a href="{% url 'superadmin:user_list' %}" class="btn btn-info btn-sm">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                </div>
            </div>
        </div>
    </div>

    <!-- Leaderboard Entries Card -->
    <div class="col-xl-3 col-md-6 mb-4">
        <div class="card border-0 shadow-sm h-100">
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-start mb-3">
                    <div>
                        <h6 class="text-muted text-uppercase fw-bold mb-1">Leaderboard Entries</h6>
                        <h2 class="mb-0 fw-bold text-warning">{{ total_leaderboard_entries|default:"0" }}</h2>
                    </div>
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3">
                        <i class="fas fa-trophy text-warning fa-lg"></i>
                    </div>
                </div>
                <div class="d-flex align-items-center justify-content-between">
                    <small class="text-muted">
                        <i class="fas fa-medal me-1"></i>Total entries
                    </small>
                    <a href="{% url 'superadmin:leaderboard_list' %}" class="btn btn-warning btn-sm">
                        <i class="fas fa-eye me-1"></i>View
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- System Information -->
<div class="row mb-4">
    <div class="col-12">
        <div class="card system-info-card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-info-circle me-2"></i>System Information</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-4 mb-3">
                        <div class="system-info-item">
                            <div class="system-info-icon">
                                <i class="bi bi-django"></i>
                            </div>
                            <div>
                                <div style="color: var(--text-color-muted); font-size: 0.85rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 4px;">Django Version</div>
                                <div style="color: var(--text-color-bright); font-weight: 600; font-size: 1.1rem;">{{ django_version }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="system-info-item">
                            <div class="system-info-icon" style="color: var(--accent-color-purple);">
                                <i class="bi bi-filetype-py"></i>
                            </div>
                            <div>
                                <div style="color: var(--text-color-muted); font-size: 0.85rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 4px;">Python Version</div>
                                <div style="color: var(--text-color-bright); font-weight: 600; font-size: 1.1rem;">{{ python_version }}</div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-4 mb-3">
                        <div class="system-info-item">
                            <div class="system-info-icon" style="color: #06b6d4;">
                                <i class="bi bi-database"></i>
                            </div>
                            <div>
                                <div style="color: var(--text-color-muted); font-size: 0.85rem; text-transform: uppercase; letter-spacing: 1px; margin-bottom: 4px;">Database Engine</div>
                                <div style="color: var(--text-color-bright); font-weight: 600; font-size: 1.1rem;">{{ database_engine }}</div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Pending Approvals -->
{% if pending_company_approvals > 0 %}
<div class="row mb-4">
    <div class="col-12">
        <div class="card" style="border-left: 4px solid var(--accent-color-orange);">
            <div class="card-header bg-warning text-dark">
                <h5 class="mb-0"><i class="bi bi-exclamation-triangle me-2"></i>Pending Approvals</h5>
            </div>
            <div class="card-body">
                <div class="d-flex justify-content-between align-items-center">
                    <div>
                        <h5 class="mb-1">{{ pending_company_approvals }} company/companies pending approval</h5>
                        <p class="mb-0">New companies require your approval before they can become active.</p>
                    </div>
                    <a href="{% url 'superadmin:company_list' %}?approval=pending" class="btn btn-warning">
                        <i class="bi bi-check-circle me-1"></i> Review Pending Companies
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
{% endif %}

<!-- Quick Actions -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header">
                <h5 class="mb-0"><i class="bi bi-lightning-charge me-2"></i>Quick Actions</h5>
            </div>
            <div class="card-body">
                <div class="row">
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card" style="border-top: 3px solid var(--accent-color-blue);">
                            <div class="quick-action-icon" style="color: var(--accent-color-blue);">
                                <i class="bi bi-building"></i>
                            </div>
                            <div class="quick-action-title">Manage Companies</div>
                            <a href="{% url 'superadmin:company_list' %}" class="btn btn-primary mt-3">
                                <i class="bi bi-arrow-right me-1"></i> Go
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card" style="border-top: 3px solid var(--accent-color-green);">
                            <div class="quick-action-icon" style="color: var(--accent-color-green);">
                                <i class="bi bi-controller"></i>
                            </div>
                            <div class="quick-action-title">Game Sessions</div>
                            <a href="{% url 'superadmin:game_session_list' %}" class="btn btn-success mt-3">
                                <i class="bi bi-arrow-right me-1"></i> Go
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card" style="border-top: 3px solid #06b6d4;">
                            <div class="quick-action-icon" style="color: #06b6d4;">
                                <i class="bi bi-people"></i>
                            </div>
                            <div class="quick-action-title">Users</div>
                            <a href="{% url 'superadmin:user_list' %}" class="btn btn-info mt-3">
                                <i class="bi bi-arrow-right me-1"></i> Go
                            </a>
                        </div>
                    </div>
                    <div class="col-md-3 mb-3">
                        <div class="quick-action-card" style="border-top: 3px solid var(--accent-color-orange);">
                            <div class="quick-action-icon" style="color: var(--accent-color-orange);">
                                <i class="bi bi-trophy"></i>
                            </div>
                            <div class="quick-action-title">Leaderboards</div>
                            <a href="{% url 'superadmin:leaderboard_list' %}" class="btn btn-warning mt-3">
                                <i class="bi bi-arrow-right me-1"></i> Go
                            </a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock superadmin_content %}
