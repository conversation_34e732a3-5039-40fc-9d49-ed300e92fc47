#!/usr/bin/env python
"""
<PERSON>rip<PERSON> to test the to_dict method directly.
"""
import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from game.models import GameSession

def test_to_dict():
    """Test the to_dict method directly"""
    print("Testing to_dict method...")
    
    # Get the most recent game session
    session = GameSession.objects.last()
    if not session:
        print("No game sessions found")
        return
    
    print(f"Direct field access:")
    print(f"  current_role: {session.current_role}")
    print(f"  current_task: {session.current_task}")
    print(f"  current_manager: {session.current_manager}")
    print(f"  role_challenges_completed: {session.role_challenges_completed}")
    
    print(f"\nto_dict() result:")
    result = session.to_dict()
    print(f"  current_role: {result.get('current_role')}")
    print(f"  current_task: {result.get('current_task')}")
    print(f"  current_manager: {result.get('current_manager')}")
    print(f"  role_challenges_completed: {result.get('role_challenges_completed')}")
    
    print(f"\nFull to_dict keys:")
    for key in sorted(result.keys()):
        if key != 'messages':  # Skip messages as it's too long
            print(f"  {key}: {result[key]}")

if __name__ == "__main__":
    test_to_dict()
