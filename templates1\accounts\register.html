{% extends 'base/layout.html' %}
{% load static %}
{% load widget_tweaks %}

{% block title %}Create Account - Corporate Prompt Master{% endblock %}

{% block body_class %}no-sidebar{% endblock %}

{% block content %}
<div class="min-vh-100 d-flex align-items-center bg-light">
    <div class="container">
        <div class="row justify-content-center">
            <div class="col-md-8 col-lg-6 col-xl-5">
                <!-- Enhanced Registration Card -->
                <div class="card border-0 shadow-lg">
                    <!-- Header Section -->
                    <div class="card-header bg-gradient-success text-white text-center border-0 py-4">
                        <div class="mb-3">
                            <div class="bg-white bg-opacity-25 rounded-circle p-3 d-inline-block">
                                <i class="fas fa-user-plus fa-2x text-white"></i>
                            </div>
                        </div>
                        <h2 class="h4 mb-1 fw-bold">Create Your Account</h2>
                        <p class="mb-0 text-white-75">Join Corporate Prompt Master today</p>
                    </div>

                    <!-- Form Section -->
                    <div class="card-body p-4">
                        <form method="post" novalidate>
                            {% csrf_token %}

                            {% if form.non_field_errors %}
                                <div class="alert alert-danger border-0 rounded-3" role="alert">
                                    <i class="fas fa-exclamation-triangle me-2"></i>
                                    {% for error in form.non_field_errors %}
                                        {{ error }}
                                    {% endfor %}
                                </div>
                            {% endif %}

                            <!-- Name Fields -->
                            <div class="row g-3 mb-3">
                                <div class="col-md-6">
                                    <label for="{{ form.first_name.id_for_label }}" class="form-label fw-semibold">
                                        <i class="fas fa-user me-2 text-success"></i>First Name
                                    </label>
                                    {% render_field form.first_name class="form-control" placeholder="Enter your first name" %}
                                    {% if form.first_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {{ form.first_name.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                                <div class="col-md-6">
                                    <label for="{{ form.last_name.id_for_label }}" class="form-label fw-semibold">
                                        <i class="fas fa-user me-2 text-success"></i>Last Name
                                    </label>
                                    {% render_field form.last_name class="form-control" placeholder="Enter your last name" %}
                                    {% if form.last_name.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {{ form.last_name.errors|join:", " }}
                                        </div>
                                    {% endif %}
                                </div>
                            </div>
                        </div>

                        <div class="form-floating mb-3 mt-3">
                            {% render_field form.username class="form-control" placeholder="Username" %}
                            <label for="{{ form.username.id_for_label }}">Username</label>
                            {% if form.username.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.username.errors|join:", " }}
                                </div>
                            {% endif %}
                            <div class="form-text">
                                Letters, digits, and @/./+/-/_ only.
                            </div>
                        </div>

                        <div class="form-floating mb-3">
                            {% render_field form.email class="form-control" placeholder="Email" %}
                            <label for="{{ form.email.id_for_label }}">Email Address</label>
                            {% if form.email.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.email.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>

                        <div class="form-floating mb-3">
                            {% render_field form.password1 class="form-control" placeholder="Password" %}
                            {# Label removed, handled by form-floating/placeholder #}
                            {% if form.password1.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password1.errors|join:", " }}
                                </div>
                            {% endif %}
                            <div class="form-text small">
                                {{ form.password1.help_text|safe }}
                            </div>
                        </div>

                        <div class="form-floating mb-4"> {# Added password2 block back #}
                            {% render_field form.password2 class="form-control" placeholder="Confirm Password" %}
                            {# Label removed, handled by form-floating/placeholder #}
                            {% if form.password2.errors %}
                                <div class="invalid-feedback d-block">
                                    {{ form.password2.errors|join:", " }}
                                </div>
                            {% endif %}
                        </div>

                        {% if form.captcha %}
                            <div class="mb-3">
                                {{ form.captcha }}
                            </div>
                        {% endif %}

                        <div class="form-check mb-4">
                            <input class="form-check-input" type="checkbox" id="agree_terms" required>
                            <label class="form-check-label small" for="agree_terms">
                                I agree to the <a href="{% url 'terms' %}" target="_blank">Terms of Service</a>
                                and <a href="{% url 'privacy' %}" target="_blank">Privacy Policy</a>
                            </label>
                        </div>

                        <div class="d-grid mb-4">
                            <button type="submit" class="btn btn-primary btn-lg">
                                <i class="bi bi-person-plus me-2"></i>
                                Create Account
                            </button>
                        </div>

                        {% if invitation_token %}
                            <input type="hidden" name="invitation_token" value="{{ invitation_token }}">
                        {% endif %}
                    </form>

                    <div class="text-center">
                        <p class="text-muted">
                            Already have an account?
                            <a href="{% url 'accounts:login' %}" class="text-decoration-none">
                                Log in
                            </a>
                        </p>
                    </div>
                </div>
            </div>

            <!-- SSO Options -->
            {% if social_auth_providers %}
                <div class="card shadow-sm mt-4">
                    <div class="card-body p-4">
                        <p class="text-center text-muted small mb-3">Or sign up with</p>
                        <div class="d-grid gap-2">
                            {% for provider in social_auth_providers %}
                                <a href="{% url 'social:begin' provider.id %}"
                                   class="btn btn-outline-secondary">
                                    <i class="bi bi-{{ provider.icon }} me-2"></i>
                                    {{ provider.name }}
                                </a>
                            {% endfor %}
                        </div>
                    </div>
                </div>
            {% endif %}
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
document.addEventListener('DOMContentLoaded', function() {
    // Password visibility toggle
    const addPasswordToggle = function(fieldId) {
        const toggleBtn = document.createElement('button');
        toggleBtn.type = 'button';
        toggleBtn.className = 'btn btn-outline-secondary';
        toggleBtn.innerHTML = '<i class="bi bi-eye"></i>';
        toggleBtn.onclick = function() {
            const field = document.getElementById(fieldId);
            const type = field.getAttribute('type') === 'password' ? 'text' : 'password';
            field.setAttribute('type', type);
            this.innerHTML = type === 'password' ? '<i class="bi bi-eye"></i>' : '<i class="bi bi-eye-slash"></i>';
        };

        const field = document.getElementById(fieldId);
        const wrapper = document.createElement('div');
        wrapper.className = 'input-group';
        field.parentNode.insertBefore(wrapper, field);
        wrapper.appendChild(field);
        wrapper.appendChild(toggleBtn);
    };

    // Add toggle buttons for both password fields
    addPasswordToggle('{{ form.password1.id_for_label }}');
    addPasswordToggle('{{ form.password2.id_for_label }}'); // Added back toggle for password2

    // Form validation
    const form = document.querySelector('form');
    const termsCheckbox = document.getElementById('agree_terms');

    form.addEventListener('submit', function(e) {
        if (!termsCheckbox.checked) {
            e.preventDefault();
            alert('Please agree to the Terms of Service and Privacy Policy');
            termsCheckbox.focus();
        }
    });
});
</script>
{% endblock %}
