<!DOCTYPE html>
<html lang="en">
<head>
  <meta http-equiv="content-type" content="text/html; charset=utf-8">
  <title>Page not found at /corporate/certificates/download/1/</title>
  <meta name="robots" content="NONE,NOARCHIVE">
  <style>
    html * { padding:0; margin:0; }
    body * { padding:10px 20px; }
    body * * { padding:0; }
    body { font-family: sans-serif; background:#eee; color:#000; }
    body > :where(header, main, footer) { border-bottom:1px solid #ddd; }
    h1 { font-weight:normal; margin-bottom:.4em; }
    h1 small { font-size:60%; color:#666; font-weight:normal; }
    table { border:none; border-collapse: collapse; width:100%; }
    td, th { vertical-align:top; padding:2px 3px; }
    th { width:12em; text-align:right; color:#666; padding-right:.5em; }
    #info { background:#f6f6f6; }
    #info ol { margin: 0.5em 4em; }
    #info ol li { font-family: monospace; }
    #summary { background: #ffc; }
    #explanation { background:#eee; border-bottom: 0px none; }
    pre.exception_value { font-family: sans-serif; color: #575757; font-size: 1.5em; margin: 10px 0 10px 0; }
  </style>
</head>
<body>
  <header id="summary">
    <h1>Page not found <small>(404)</small></h1>
    
    <table class="meta">
      <tr>
        <th scope="row">Request Method:</th>
        <td>GET</td>
      </tr>
      <tr>
        <th scope="row">Request URL:</th>
        <td>http://127.0.0.1:8000/corporate/certificates/download/1/</td>
      </tr>
      
    </table>
  </header>

  <main id="info">
    
      <p>
      Using the URLconf defined in <code>prompt_game.urls</code>,
      Django tried these URL patterns, in this order:
      </p>
      <ol>
        
          <li>
            
              <code>
                admin/
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                game/
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                
                [name='home']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                login/
                [name='corporate_login']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                logout/
                [name='corporate_logout']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                register/
                [name='corporate_register']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                dashboard/
                [name='corporate_dashboard']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                profile/edit/
                [name='edit_profile']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                company/edit/
                [name='edit_company']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                games/
                [name='game_list']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                company/switch/
                [name='company_switch']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                company/create/
                [name='create_company']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                leaderboard/
                [name='leaderboard']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                certificates/
                [name='view_certificates']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                certificates/&lt;uuid:certificate_id&gt;/download/
                [name='download_certificate']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                verify-certificate/
                [name='verify_certificate']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                public/verify/
                [name='public_verify_certificate']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                users/
                [name='company_users']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                users/progress/
                [name='user_progress']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                users/&lt;int:user_id&gt;/progress/
                [name='user_progress_detail']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                invitations/
                [name='company_invitations']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                invitations/&lt;int:invitation_id&gt;/delete/
                [name='delete_invitation']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                invitations/accept/&lt;str:token&gt;/
                [name='accept_invitation']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                register/invitation/
                [name='register_with_invitation']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                registration-links/&lt;uuid:token&gt;/delete/
                [name='delete_registration_link']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                join/&lt;uuid:token&gt;/
                [name='register_with_link']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                register/link/
                [name='register_with_link_form']
              </code>
            
          </li>
        
          <li>
            
              <code>
                corporate/
                
              </code>
            
              <code>
                api/update-leaderboard/
                [name='update_leaderboard']
              </code>
            
          </li>
        
          <li>
            
              <code>
                superadmin/
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                impersonate/
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                sitemap.xml
                [name='django.contrib.sitemaps.views.sitemap']
              </code>
            
          </li>
        
          <li>
            
              <code>
                robots.txt
                
              </code>
            
          </li>
        
          <li>
            
              <code>
                
                [name='home']
              </code>
            
          </li>
        
          <li>
            
              <code>
                ^media/(?P&lt;path&gt;.*)$
                
              </code>
            
          </li>
        
      </ol>
      <p>
        
          The current path, <code>corporate/certificates/download/1/</code>,
        
        didn’t match any of these.
      </p>
    
  </main>

  <footer id="explanation">
    <p>
      You’re seeing this error because you have <code>DEBUG = True</code> in
      your Django settings file. Change that to <code>False</code>, and Django
      will display a standard 404 page.
    </p>
  </footer>
</body>
</html>
