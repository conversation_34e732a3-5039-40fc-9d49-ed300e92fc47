{% extends 'corporate/base.html' %}

{% block title %}Register - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-10 col-xl-8">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-building fa-3x text-white-50"></i>
                    </div>
                    <h3 class="mb-0 fw-bold">Corporate Registration</h3>
                    <p class="mb-0 text-white-75">Create your corporate account and start training</p>
                </div>
                <div class="card-body p-4">
                    <form method="post" enctype="multipart/form-data" novalidate class="needs-validation">
                        {% csrf_token %}

                        <div class="row g-4">
                            <div class="col-lg-6">
                                <div class="border rounded-3 p-4 bg-light">
                                    <h5 class="mb-4 text-primary fw-bold">
                                        <i class="fas fa-building me-2"></i>Company Information
                                    </h5>

                                    <div class="mb-4">
                                        <label for="{{ company_form.name.id_for_label }}" class="form-label fw-semibold">
                                            <i class="fas fa-tag me-2 text-primary"></i>Company Name <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-dark text-white border-end-0">
                                                <i class="fas fa-building text-white-50"></i>
                                            </span>
                                            {{ company_form.name }}
                                        </div>
                                        {% if company_form.name.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {% for error in company_form.name.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-4">
                                        <label for="{{ company_form.description.id_for_label }}" class="form-label fw-semibold">
                                            <i class="fas fa-align-left me-2 text-primary"></i>Company Description
                                        </label>
                                        {{ company_form.description }}
                                        {% if company_form.description.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {% for error in company_form.description.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>Brief description of your company
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label for="{{ company_form.phone_number.id_for_label }}" class="form-label fw-semibold">
                                            <i class="fas fa-phone me-2 text-primary"></i>Phone Number <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-dark text-white border-end-0">
                                                <i class="fas fa-phone text-white-50"></i>
                                            </span>
                                            {{ company_form.phone_number }}
                                        </div>
                                        {% if company_form.phone_number.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {% for error in company_form.phone_number.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        {% if company_form.phone_number.help_text %}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>{{ company_form.phone_number.help_text }}
                                        </div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ company_form.logo.id_for_label }}" class="form-label fw-semibold">
                                            <i class="fas fa-image me-2 text-primary"></i>Company Logo
                                        </label>
                                        <div class="border border-secondary rounded-3 p-3 bg-dark">
                                            {{ company_form.logo }}
                                        </div>
                                        {% if company_form.logo.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {% for error in company_form.logo.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <div class="form-text">
                                            <i class="fas fa-upload me-1"></i>Optional: Upload your company logo (PNG, JPG, max 2MB)
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="col-lg-6">
                                <div class="border rounded-3 p-4 bg-light">
                                    <h5 class="mb-4 text-success fw-bold">
                                        <i class="fas fa-user-tie me-2"></i>Admin User Information
                                    </h5>

                                    <div class="row g-3">
                                        <div class="col-md-6">
                                            <label for="{{ user_form.first_name.id_for_label }}" class="form-label fw-semibold">
                                                <i class="fas fa-user me-2 text-success"></i>First Name <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-dark text-white border-end-0">
                                                    <i class="fas fa-user text-white-50"></i>
                                                </span>
                                                {{ user_form.first_name }}
                                            </div>
                                            {% if user_form.first_name.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {% for error in user_form.first_name.errors %}
                                                {{ error }}
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>

                                        <div class="col-md-6">
                                            <label for="{{ user_form.last_name.id_for_label }}" class="form-label fw-semibold">
                                                <i class="fas fa-user me-2 text-success"></i>Last Name <span class="text-danger">*</span>
                                            </label>
                                            <div class="input-group">
                                                <span class="input-group-text bg-dark text-white border-end-0">
                                                    <i class="fas fa-user text-white-50"></i>
                                                </span>
                                                {{ user_form.last_name }}
                                            </div>
                                            {% if user_form.last_name.errors %}
                                            <div class="invalid-feedback d-block">
                                                <i class="fas fa-exclamation-circle me-1"></i>
                                                {% for error in user_form.last_name.errors %}
                                                {{ error }}
                                                {% endfor %}
                                            </div>
                                            {% endif %}
                                        </div>
                                    </div>

                                    <div class="mb-4 mt-3">
                                        <label for="{{ user_form.email.id_for_label }}" class="form-label fw-semibold">
                                            <i class="fas fa-envelope me-2 text-success"></i>Email Address <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-dark text-white border-end-0">
                                                <i class="fas fa-at text-white-50"></i>
                                            </span>
                                            {{ user_form.email }}
                                        </div>
                                        {% if user_form.email.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {% for error in user_form.email.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                        <div class="form-text">
                                            <i class="fas fa-info-circle me-1"></i>This will be your login email
                                        </div>
                                    </div>

                                    <div class="mb-4">
                                        <label for="{{ user_form.username.id_for_label }}" class="form-label fw-semibold">
                                            <i class="fas fa-id-badge me-2 text-success"></i>Username <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-dark text-white border-end-0">
                                                <i class="fas fa-user-circle text-white-50"></i>
                                            </span>
                                            {{ user_form.username }}
                                        </div>
                                        {% if user_form.username.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {% for error in user_form.username.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-4">
                                        <label for="{{ user_form.password1.id_for_label }}" class="form-label fw-semibold">
                                            <i class="fas fa-lock me-2 text-success"></i>Password <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-dark text-white border-end-0">
                                                <i class="fas fa-key text-white-50"></i>
                                            </span>
                                            {{ user_form.password1 }}
                                        </div>
                                        {% if user_form.password1.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {% for error in user_form.password1.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>

                                    <div class="mb-3">
                                        <label for="{{ user_form.password2.id_for_label }}" class="form-label fw-semibold">
                                            <i class="fas fa-lock me-2 text-success"></i>Confirm Password <span class="text-danger">*</span>
                                        </label>
                                        <div class="input-group">
                                            <span class="input-group-text bg-dark text-white border-end-0">
                                                <i class="fas fa-check-circle text-white-50"></i>
                                            </span>
                                            {{ user_form.password2 }}
                                        </div>
                                        {% if user_form.password2.errors %}
                                        <div class="invalid-feedback d-block">
                                            <i class="fas fa-exclamation-circle me-1"></i>
                                            {% for error in user_form.password2.errors %}
                                            {{ error }}
                                            {% endfor %}
                                        </div>
                                        {% endif %}
                                    </div>
                                </div>
                            </div>
                        </div>

                        <div class="text-center mt-4">
                            <div class="d-grid gap-2 col-md-6 mx-auto">
                                <button type="submit" class="btn btn-primary btn-lg py-3 fw-semibold">
                                    <i class="fas fa-rocket me-2"></i>Create Corporate Account
                                </button>
                            </div>
                            <p class="mt-3 text-muted small">
                                <i class="fas fa-shield-alt me-1"></i>
                                By registering, you agree to our Terms of Service and Privacy Policy
                            </p>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light text-center py-3">
                    <p class="mb-0 text-muted">
                        Already have an account?
                        <a href="{% url 'corporate:corporate_login' %}" class="text-primary fw-semibold text-decoration-none">
                            <i class="fas fa-sign-in-alt me-1"></i>Login here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
