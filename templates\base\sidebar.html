{% load i18n %}
<!-- Enhanced Professional Sidebar -->
<div class="sidebar" id="sidebar">
    <!-- Sidebar Header -->
    <div class="sidebar-header p-4 border-bottom">
        <div class="d-flex align-items-center">
            <div class="bg-gradient-primary rounded-circle p-2 me-3">
                <i class="fas fa-brain text-white"></i>
            </div>
            <div class="sidebar-brand-text">
                <h6 class="mb-0 fw-bold text-primary">Corporate</h6>
                <small class="text-muted">Prompt Master</small>
            </div>
        </div>
    </div>

    <!-- Sidebar Menu -->
    <div class="sidebar-menu p-3">
        <!-- Main Navigation -->
        <div class="mb-4">
            <h6 class="sidebar-heading text-muted text-uppercase fw-bold mb-3">
                <i class="fas fa-th-large me-2"></i>Main Menu
            </h6>
            <ul class="nav flex-column">
                <li class="nav-item mb-1">
                    <a class="nav-link d-flex align-items-center rounded-3 p-3" href="/">
                        <i class="fas fa-home me-3 text-primary"></i>
                        <span class="fw-semibold">{% trans "Dashboard" %}</span>
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link d-flex align-items-center rounded-3 p-3" href="/game/">
                        <i class="fas fa-gamepad me-3 text-success"></i>
                        <span class="fw-semibold">{% trans "Play Game" %}</span>
                    </a>
                </li>
                <li class="nav-item mb-1">
                    <a class="nav-link d-flex align-items-center rounded-3 p-3" href="/leaderboard/">
                        <i class="fas fa-trophy me-3 text-warning"></i>
                        <span class="fw-semibold">{% trans "Leaderboard" %}</span>
                    </a>
                </li>
            </ul>
        </div>

        {% if request.user.is_authenticated %}
        <!-- User Section -->
        <div class="mb-4">
            <h6 class="sidebar-heading text-muted text-uppercase fw-bold mb-3">
                <i class="fas fa-user me-2"></i>Account
            </h6>
            <ul class="nav flex-column">
                <li class="nav-item mb-1">
                    <a class="nav-link d-flex align-items-center rounded-3 p-3" href="/profile/">
                        <i class="fas fa-user-circle me-3 text-info"></i>
                        <span class="fw-semibold">{% trans "Profile" %}</span>
                    </a>
                </li>
                {% if request.user.is_staff %}
                <li class="nav-item mb-1">
                    <a class="nav-link d-flex align-items-center rounded-3 p-3" href="{% url 'admin:index' %}">
                        <i class="fas fa-cog me-3 text-secondary"></i>
                        <span class="fw-semibold">{% trans "Admin" %}</span>
                    </a>
                </li>
                {% endif %}
                {% if request.user.is_superuser %}
                <li class="nav-item mb-1">
                    <a class="nav-link d-flex align-items-center rounded-3 p-3" href="{% url 'superadmin:dashboard' %}">
                        <i class="fas fa-shield-alt me-3 text-danger"></i>
                        <span class="fw-semibold">{% trans "Superadmin" %}</span>
                    </a>
                </li>
                {% endif %}
            </ul>
        </div>

        <!-- User Info Card -->
        <div class="card bg-gradient-primary text-white border-0 mb-4">
            <div class="card-body p-3">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-2 me-3">
                        <i class="fas fa-user text-white"></i>
                    </div>
                    <div class="flex-grow-1">
                        <h6 class="mb-0 fw-bold">{{ request.user.get_full_name|default:request.user.username }}</h6>
                        <small class="text-white-75">{{ request.user.email|truncatechars:20 }}</small>
                    </div>
                </div>
                <hr class="my-3 border-white-25">
                <a href="/logout/" class="btn btn-outline-light btn-sm w-100">
                    <i class="fas fa-sign-out-alt me-2"></i>{% trans "Logout" %}
                </a>
            </div>
        </div>
        {% else %}
        <!-- Guest Section -->
        <div class="mb-4">
            <h6 class="sidebar-heading text-muted text-uppercase fw-bold mb-3">
                <i class="fas fa-user-plus me-2"></i>Get Started
            </h6>
            <div class="d-grid gap-2">
                <a href="/login/" class="btn btn-outline-primary">
                    <i class="fas fa-sign-in-alt me-2"></i>{% trans "Login" %}
                </a>
                <a href="/register/" class="btn btn-primary">
                    <i class="fas fa-user-plus me-2"></i>{% trans "Register" %}
                </a>
            </div>
        </div>
        {% endif %}

        <!-- Help Section -->
        <div class="mt-auto">
            <div class="card bg-light border-0">
                <div class="card-body p-3 text-center">
                    <i class="fas fa-question-circle text-info mb-2 fa-2x"></i>
                    <h6 class="mb-2">Need Help?</h6>
                    <p class="small text-muted mb-3">Get assistance with the platform</p>
                    <a href="{% url 'game:help_redirect' %}" target="_blank" class="btn btn-info btn-sm">
                        <i class="fas fa-external-link-alt me-1"></i>Help Center
                    </a>
                </div>
            </div>
        </div>
    </div>
</div>
