/* Company Admin Button Contrast Fixes */
/* This file specifically fixes button contrast issues in company admin templates */

/* Header buttons in company admin pages */
.card-header .btn-light {
    background-color: #28a745 !important;
    color: #ffffff !important;
    border: 2px solid #28a745 !important;
    font-weight: bold !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.3) !important;
    box-shadow: 0 3px 6px rgba(0, 0, 0, 0.2) !important;
}

.card-header .btn-light:hover {
    background-color: #218838 !important;
    color: #ffffff !important;
    border-color: #1e7e34 !important;
    transform: translateY(-2px) !important;
    box-shadow: 0 5px 10px rgba(0, 0, 0, 0.25) !important;
}

.card-header .btn-light:focus,
.card-header .btn-light:active {
    background-color: #1e7e34 !important;
    color: #ffffff !important;
    border-color: #1c7430 !important;
    box-shadow: 0 0 0 0.2rem rgba(40, 167, 69, 0.5) !important;
}

/* Outline buttons in admin tables */
.btn-outline-primary {
    color: #ffffff !important;
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    font-weight: 600 !important;
}

.btn-outline-primary:hover {
    color: #ffffff !important;
    background-color: #0b5ed7 !important;
    border-color: #0a58ca !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3) !important;
}

.btn-outline-primary:focus,
.btn-outline-primary:active {
    color: #ffffff !important;
    background-color: #0a58ca !important;
    border-color: #0a53be !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.5) !important;
}

/* Outline danger buttons */
.btn-outline-danger {
    color: #ffffff !important;
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    font-weight: 600 !important;
}

.btn-outline-danger:hover {
    color: #ffffff !important;
    background-color: #bb2d3b !important;
    border-color: #b02a37 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3) !important;
}

.btn-outline-danger:focus,
.btn-outline-danger:active {
    color: #ffffff !important;
    background-color: #b02a37 !important;
    border-color: #a02834 !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5) !important;
}

/* Outline success buttons */
.btn-outline-success {
    color: #ffffff !important;
    background-color: #198754 !important;
    border-color: #198754 !important;
    font-weight: 600 !important;
}

.btn-outline-success:hover {
    color: #ffffff !important;
    background-color: #157347 !important;
    border-color: #146c43 !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3) !important;
}

.btn-outline-success:focus,
.btn-outline-success:active {
    color: #ffffff !important;
    background-color: #146c43 !important;
    border-color: #13653f !important;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.5) !important;
}

/* Primary buttons enhancement */
.btn-primary {
    background-color: #0d6efd !important;
    border-color: #0d6efd !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.btn-primary:hover {
    background-color: #0b5ed7 !important;
    border-color: #0a58ca !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(13, 110, 253, 0.3) !important;
}

.btn-primary:focus,
.btn-primary:active {
    background-color: #0a58ca !important;
    border-color: #0a53be !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(13, 110, 253, 0.5) !important;
}

/* Danger buttons enhancement */
.btn-danger {
    background-color: #dc3545 !important;
    border-color: #dc3545 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.btn-danger:hover {
    background-color: #bb2d3b !important;
    border-color: #b02a37 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(220, 53, 69, 0.3) !important;
}

.btn-danger:focus,
.btn-danger:active {
    background-color: #b02a37 !important;
    border-color: #a02834 !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(220, 53, 69, 0.5) !important;
}

/* Success buttons enhancement */
.btn-success {
    background-color: #198754 !important;
    border-color: #198754 !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.btn-success:hover {
    background-color: #157347 !important;
    border-color: #146c43 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(25, 135, 84, 0.3) !important;
}

.btn-success:focus,
.btn-success:active {
    background-color: #146c43 !important;
    border-color: #13653f !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(25, 135, 84, 0.5) !important;
}

/* Secondary buttons enhancement */
.btn-secondary {
    background-color: #6c757d !important;
    border-color: #6c757d !important;
    color: #ffffff !important;
    font-weight: 600 !important;
    text-shadow: 0 1px 2px rgba(0, 0, 0, 0.2) !important;
}

.btn-secondary:hover {
    background-color: #5c636a !important;
    border-color: #565e64 !important;
    color: #ffffff !important;
    transform: translateY(-1px) !important;
    box-shadow: 0 4px 8px rgba(108, 117, 125, 0.3) !important;
}

.btn-secondary:focus,
.btn-secondary:active {
    background-color: #565e64 !important;
    border-color: #51585e !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(108, 117, 125, 0.5) !important;
}

/* Small button adjustments */
.btn-sm {
    padding: 0.375rem 0.75rem !important;
    font-size: 0.875rem !important;
    border-radius: 0.375rem !important;
    min-width: auto !important;
}

/* Button group spacing */
.btn-group .btn {
    margin-right: 0 !important;
}

.btn-group .btn + .btn {
    margin-left: -1px !important;
}

/* Icon buttons */
.btn i {
    opacity: 1 !important;
    color: inherit !important;
}

/* Modal button fixes */
.modal-footer .btn {
    min-width: 100px !important;
    margin-left: 0.5rem !important;
}

.modal-footer .btn:first-child {
    margin-left: 0 !important;
}

/* Copy button specific styling */
.copy-link {
    position: relative !important;
}

.copy-link:after {
    content: "Copied!" !important;
    position: absolute !important;
    top: -30px !important;
    left: 50% !important;
    transform: translateX(-50%) !important;
    background: #000 !important;
    color: #fff !important;
    padding: 4px 8px !important;
    border-radius: 4px !important;
    font-size: 12px !important;
    opacity: 0 !important;
    pointer-events: none !important;
    transition: opacity 0.3s !important;
}

.copy-link.copied:after {
    opacity: 1 !important;
}

/* Ensure all buttons have proper contrast in dark mode */
body.dark-mode .btn,
body[data-theme="dark"] .btn {
    border-width: 2px !important;
}

/* Badge contrast fixes in admin templates */
.badge.bg-danger {
    background-color: #dc3545 !important;
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
}

.badge.bg-success {
    background-color: #198754 !important;
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
}

.badge.bg-primary {
    background-color: #0d6efd !important;
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
}

.badge.bg-info {
    background-color: #0dcaf0 !important;
    color: #000000 !important;
    font-weight: bold !important;
    text-shadow: none !important;
}

.badge.bg-secondary {
    background-color: #6c757d !important;
    color: #ffffff !important;
    font-weight: bold !important;
    text-shadow: 0 1px 1px rgba(0, 0, 0, 0.3) !important;
}
