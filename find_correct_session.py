#!/usr/bin/env python
"""
<PERSON>ript to find the correct game session with the progression issue.
"""
import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from game.models import GameSession, Message

def find_correct_session():
    """Find the game session with the progression issue"""
    print("Finding all game sessions...")
    
    # Get all game sessions
    sessions = GameSession.objects.all().order_by('-id')
    print(f"Found {len(sessions)} game sessions:")
    
    for session in sessions:
        print(f"\nSession {session.id}:")
        print(f"  Role: {session.current_role}")
        print(f"  Current task: {session.current_task}")
        print(f"  Role challenges completed: {session.role_challenges_completed}")
        print(f"  Total challenges completed: {session.challenges_completed}")
        print(f"  Session ID: {session.session_id}")
        print(f"  User: {session.user}")
        
        # Count messages
        message_count = session.messages.count()
        print(f"  Messages: {message_count}")
        
        # Check for onboarding_checklist messages
        onboarding_count = session.messages.filter(task_id="onboarding_checklist").count()
        if onboarding_count > 0:
            print(f"  *** HAS ONBOARDING CHECKLIST MESSAGES: {onboarding_count} ***")
            
            # Show recent messages
            recent_messages = session.messages.all().order_by('-timestamp')[:5]
            print(f"  Recent messages:")
            for msg in recent_messages:
                msg_type = "CHALLENGE" if msg.is_challenge else "REGULAR"
                task_info = f" (task: {msg.task_id})" if msg.task_id else ""
                print(f"    {msg.timestamp.strftime('%H:%M:%S')} - {msg.sender} [{msg_type}]{task_info}")

if __name__ == "__main__":
    find_correct_session()
