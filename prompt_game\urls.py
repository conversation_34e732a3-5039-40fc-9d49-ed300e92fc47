"""
URL configuration for prompt_game project.

The `urlpatterns` list routes URLs to views. For more information please see:
    https://docs.djangoproject.com/en/5.2/topics/http/urls/
Examples:
Function views
    1. Add an import:  from my_app import views
    2. Add a URL to urlpatterns:  path('', views.home, name='home')
Class-based views
    1. Add an import:  from other_app.views import Home
    2. Add a URL to urlpatterns:  path('', Home.as_view(), name='home')
Including another URLconf
    1. Import the include() function: from django.urls import include, path
    2. Add a URL to urlpatterns:  path('blog/', include('blog.urls'))
"""
from django.contrib import admin
from django.urls import path, include
from django.conf import settings
from django.conf.urls.static import static
from django.shortcuts import redirect
from django.contrib.sitemaps.views import sitemap
from django.views.generic.base import TemplateView
from .sitemaps import sitemaps
from corporate import views as corporate_views

urlpatterns = [
    path('admin/', admin.site.urls),
    path('game/', include('game.urls')),
    path('corporate/', include('corporate.urls', namespace='corporate')),
    path('superadmin/', include('superadmin.urls', namespace='superadmin')),
    # Django Impersonate URLs
    path('impersonate/', include('impersonate.urls')),
    # SEO URLs
    path('sitemap.xml', sitemap, {'sitemaps': sitemaps}, name='django.contrib.sitemaps.views.sitemap'),
    path('robots.txt', TemplateView.as_view(template_name='robots.txt', content_type='text/plain')),
    # Root URL serves corporate home directly
    path('', corporate_views.home, name='home'),
]

# Serve media files (WhiteNoise handles static files)
# In production, media files should ideally be served by a web server like Nginx
urlpatterns += static(settings.MEDIA_URL, document_root=settings.MEDIA_ROOT)
