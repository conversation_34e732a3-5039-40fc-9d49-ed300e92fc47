{% extends 'corporate/base.html' %}

{% block title %}User Progress - {{ company.name }}{% endblock %}

{% block extra_css %}
{% include 'corporate/user_progress_extra.html' %}
{% endblock %}

{% block content %}
<div class="container-fluid py-4">
    <!-- Page Header -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="bg-gradient-primary text-white rounded-3 p-4">
                <div class="d-flex align-items-center">
                    <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                        <i class="fas fa-chart-line fa-lg text-white"></i>
                    </div>
                    <div>
                        <h1 class="h3 mb-1 fw-bold">User Progress</h1>
                        <p class="mb-0 text-white-75">Track training progress across {{ company.name }}</p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <div class="row">
        <div class="col-12">
            <div class="card shadow">
                <div class="card-header bg-primary text-white d-flex justify-content-between align-items-center">
                    <h5 class="mb-0"><i class="fas fa-chart-line me-2"></i>{{ company.name }} User Progress</h5>
                </div>
                <div class="card-body p-0">
                    {% if user_progress %}
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="bg-primary text-white">
                                <tr>
                                    <th class="border-0 fw-semibold">USER</th>
                                    <th class="border-0 fw-semibold">JOB TITLE</th>
                                    <th class="border-0 fw-semibold">ROLE</th>
                                    <th class="border-0 fw-semibold">HIGHEST SCORE</th>
                                    <th class="border-0 fw-semibold">TOTAL SESSIONS</th>
                                    <th class="border-0 fw-semibold">LATEST CERTIFICATE</th>
                                    <th class="border-0 fw-semibold">ACTIONS</th>
                                </tr>
                        </thead>
                            <tbody>
                                {% for progress in user_progress %}
                                <tr>
                                    <td class="border-0 py-3">
                                        <div class="d-flex align-items-center">
                                            <div class="me-3">
                                                {% if progress.user.profile.avatar %}
                                                <img src="{{ progress.user.profile.avatar.url }}" alt="Avatar"
                                                     class="rounded-circle shadow-sm" width="40" height="40">
                                                {% else %}
                                                <div class="bg-primary rounded-circle d-flex align-items-center justify-content-center text-white fw-bold"
                                                     style="width: 40px; height: 40px;">
                                                    {{ progress.user.username|first|upper }}
                                                </div>
                                                {% endif %}
                                            </div>
                                            <div>
                                                <h6 class="mb-1 fw-semibold">{{ progress.user.get_full_name|default:progress.user.username }}</h6>
                                                <small class="text-muted">@{{ progress.user.username }}</small>
                                            </div>
                                        </div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="text-muted">{{ progress.corporate_profile.job_title|default:"Not specified" }}</span>
                                    </td>
                                    <td class="border-0 py-3">
                                        {% if progress.corporate_profile.is_company_admin %}
                                        <span class="badge bg-danger rounded-pill">
                                            <i class="fas fa-crown me-1"></i>Admin
                                        </span>
                                        {% else %}
                                        <span class="badge bg-secondary rounded-pill">
                                            <i class="fas fa-user me-1"></i>Member
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="border-0 py-3">
                                        <div class="fw-bold text-success">{{ progress.highest_score }}</div>
                                    </td>
                                    <td class="border-0 py-3">
                                        <span class="badge bg-info rounded-pill">{{ progress.total_sessions }}</span>
                                    </td>
                                    <td class="border-0 py-3">
                                        {% if progress.latest_certificate %}
                                        <a href="{% url 'corporate:download_certificate' certificate_id=progress.latest_certificate.id %}"
                                           class="btn btn-sm btn-outline-success">
                                            <i class="fas fa-certificate me-1"></i>{{ progress.latest_certificate.issue_date|date:"M d, Y" }}
                                        </a>
                                        {% else %}
                                        <span class="text-muted small">
                                            <i class="fas fa-minus-circle me-1"></i>No certificates
                                        </span>
                                        {% endif %}
                                    </td>
                                    <td class="border-0 py-3">
                                        <a href="{% url 'corporate:user_progress_detail' user_id=progress.user.id %}"
                                           class="btn btn-sm btn-primary">
                                            <i class="fas fa-chart-line me-1"></i>View Details
                                        </a>
                                    </td>
                                </tr>
                                {% endfor %}
                            </tbody>
                        </table>
                    </div>
                    {% else %}
                    <div class="p-4 text-center">
                        <div class="mb-3">
                            <i class="fas fa-chart-line fa-3x text-muted"></i>
                        </div>
                        <h6 class="text-muted">No Progress Data</h6>
                        <p class="text-muted small mb-0">No user progress data available yet.</p>
                    </div>
                    {% endif %}
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
