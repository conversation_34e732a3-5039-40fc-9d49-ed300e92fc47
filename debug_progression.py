#!/usr/bin/env python
"""
Script to debug task progression after completing the second task.
"""
import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from game.models import GameSession, Message
from game.all_role_tasks import get_all_role_tasks

def debug_progression():
    """Debug the current task progression state"""
    print("Debugging task progression...")
    
    # Get the most recent game session
    session = GameSession.objects.last()
    if not session:
        print("No game sessions found")
        return
    
    print(f"Current state:")
    print(f"  Role: {session.current_role}")
    print(f"  Role challenges completed: {session.role_challenges_completed}")
    print(f"  Total challenges completed: {session.challenges_completed}")
    print(f"  Current task: {session.current_task}")
    print(f"  Current manager: {session.current_manager}")
    print(f"  Game completed: {session.game_completed}")
    
    # Get tasks for current role
    current_role_tasks = get_all_role_tasks(session.current_role)
    print(f"\nTasks for {session.current_role}:")
    for i, task in enumerate(current_role_tasks):
        status = "CURRENT" if task["id"] == session.current_task else "PENDING"
        if i < session.role_challenges_completed:
            status = "COMPLETED"
        print(f"  {i}. {task['id']} - {status}")
    
    # Check recent messages to see if task completion was processed
    print(f"\nRecent messages (last 10):")
    recent_messages = session.messages.all().order_by('-timestamp')[:10]
    for msg in recent_messages:
        print(f"  {msg.timestamp.strftime('%H:%M:%S')} - {msg.sender}: {msg.text[:100]}...")
    
    # Check if there are any pending messages or challenges
    pending_messages = session.messages.filter(is_challenge=True).order_by('-timestamp')[:3]
    print(f"\nRecent challenges:")
    for msg in pending_messages:
        print(f"  {msg.timestamp.strftime('%H:%M:%S')} - {msg.sender}: {msg.text[:100]}...")

    # Check specifically for onboarding_checklist task message
    onboarding_messages = session.messages.filter(task_id="onboarding_checklist")
    print(f"\nOnboarding checklist messages:")
    for msg in onboarding_messages:
        print(f"  {msg.timestamp.strftime('%H:%M:%S')} - {msg.sender} (challenge: {msg.is_challenge}): {msg.text[:100]}...")

    # Check if there are any messages created after the customer_service completion
    import datetime
    from django.utils import timezone
    cutoff_time = timezone.now() - datetime.timedelta(hours=1)  # Last hour
    recent_task_messages = session.messages.filter(
        is_challenge=True,
        timestamp__gte=cutoff_time
    ).order_by('-timestamp')
    print(f"\nTask messages in last hour:")
    for msg in recent_task_messages:
        task_id = getattr(msg, 'task_id', 'No task_id')
        print(f"  {msg.timestamp.strftime('%H:%M:%S')} - {msg.sender} (task: {task_id}): {msg.text[:100]}...")
    
    # Check expected vs actual state
    expected_task_index = session.role_challenges_completed
    if expected_task_index < len(current_role_tasks):
        expected_task = current_role_tasks[expected_task_index]
        print(f"\nExpected current task: {expected_task['id']} (index {expected_task_index})")
        if session.current_task != expected_task['id']:
            print(f"*** MISMATCH: Current task is {session.current_task}, should be {expected_task['id']} ***")
        else:
            print("Task assignment is correct")
    else:
        print(f"Player has completed all tasks for {session.current_role}")

if __name__ == "__main__":
    debug_progression()
