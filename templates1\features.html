{% extends 'base/layout.html' %}

{% block title %}Features - Corporate Prompt Master{% endblock %}

{% block content %}
<!-- Enhanced Features Hero Section -->
<section class="features-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <h1 class="display-4 fw-bold mb-3 text-shadow">
                                    <i class="fas fa-rocket me-3"></i>
                                    Powerful <span class="text-gradient">Features</span>
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Discover the comprehensive suite of tools and capabilities that make
                                    Corporate Prompt Master the ultimate AI training platform.
                                </p>
                                <div class="features-badges d-flex flex-wrap gap-3">
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-gamepad me-2"></i>Interactive Training
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-robot me-2"></i>AI Assistants
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-chart-line me-2"></i>Advanced Analytics
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="features-stats text-center">
                                <div class="stat-item mb-4">
                                    <div class="stat-number display-6 fw-bold text-white">50+</div>
                                    <div class="stat-label text-white-75">Features</div>
                                </div>
                                <div class="stat-item mb-4">
                                    <div class="stat-number display-6 fw-bold text-white">24/7</div>
                                    <div class="stat-label text-white-75">Availability</div>
                                </div>
                                <div class="stat-item">
                                    <div class="stat-number display-6 fw-bold text-white">∞</div>
                                    <div class="stat-label text-white-75">Possibilities</div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Features Content -->
<div class="container-fluid">
    <!-- Core Features Grid -->
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Core Features</h2>
                <p class="lead text-muted">Everything you need to transform your team's AI capabilities</p>
            </div>
        </div>

        <!-- Training Games -->
        <div class="col-lg-4 col-md-6">
            <div class="feature-card card border-0 shadow-lg h-100">
                <div class="card-header bg-gradient-primary text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-gamepad fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">Interactive Training Games</h5>
                            <p class="mb-0 text-white-75 small">Engaging learning experiences</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <ul class="feature-list list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Prompt Engineering Challenges</strong><br>
                            <small class="text-muted">Master the art of AI communication</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Progressive Difficulty Levels</strong><br>
                            <small class="text-muted">From beginner to expert training</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Real-time Feedback</strong><br>
                            <small class="text-muted">Instant scoring and improvement tips</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Leaderboards & Competition</strong><br>
                            <small class="text-muted">Gamified learning environment</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- AI Assistants -->
        <div class="col-lg-4 col-md-6">
            <div class="feature-card card border-0 shadow-lg h-100">
                <div class="card-header bg-gradient-success text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-robot fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">Custom AI Assistants</h5>
                            <p class="mb-0 text-white-75 small">Build your AI workforce</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <ul class="feature-list list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>No-Code Assistant Builder</strong><br>
                            <small class="text-muted">Create AI assistants without programming</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Company-Specific Training</strong><br>
                            <small class="text-muted">Upload your data and documents</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Multi-Modal Capabilities</strong><br>
                            <small class="text-muted">Text, images, and document processing</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>API Integration</strong><br>
                            <small class="text-muted">Connect with your existing tools</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>

        <!-- Analytics -->
        <div class="col-lg-4 col-md-6">
            <div class="feature-card card border-0 shadow-lg h-100">
                <div class="card-header bg-gradient-info text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-chart-line fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">Advanced Analytics</h5>
                            <p class="mb-0 text-white-75 small">Data-driven insights</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <ul class="feature-list list-unstyled">
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Performance Tracking</strong><br>
                            <small class="text-muted">Monitor individual and team progress</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Usage Analytics</strong><br>
                            <small class="text-muted">Understand AI assistant utilization</small>
                        </li>
                        <li class="mb-3">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>ROI Measurement</strong><br>
                            <small class="text-muted">Calculate training investment returns</small>
                        </li>
                        <li class="mb-0">
                            <i class="fas fa-check-circle text-success me-2"></i>
                            <strong>Custom Reports</strong><br>
                            <small class="text-muted">Generate detailed insights</small>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
    </div>

    <!-- Additional Features -->
    <div class="row g-4 mb-5">
        <div class="col-12">
            <div class="text-center mb-5">
                <h2 class="display-5 fw-bold mb-3">Additional Capabilities</h2>
                <p class="lead text-muted">Extended features for enterprise-grade deployment</p>
            </div>
        </div>

        <!-- Team Management -->
        <div class="col-lg-6">
            <div class="feature-card card border-0 shadow-lg h-100">
                <div class="card-header bg-gradient-warning text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-users fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">Team Management</h5>
                            <p class="mb-0 text-white-75 small">Organize and collaborate</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-user-plus text-warning me-2"></i>
                                <strong>User Invitations</strong><br>
                                <small class="text-muted">Easy team onboarding</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-shield-alt text-warning me-2"></i>
                                <strong>Role-Based Access</strong><br>
                                <small class="text-muted">Granular permissions</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-share-alt text-warning me-2"></i>
                                <strong>Resource Sharing</strong><br>
                                <small class="text-muted">Collaborative workspaces</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-comments text-warning me-2"></i>
                                <strong>Team Communication</strong><br>
                                <small class="text-muted">Built-in messaging</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Security & Compliance -->
        <div class="col-lg-6">
            <div class="feature-card card border-0 shadow-lg h-100">
                <div class="card-header bg-gradient-danger text-white border-0 py-4">
                    <div class="d-flex align-items-center">
                        <div class="feature-icon bg-white bg-opacity-25 rounded-circle p-3 me-3">
                            <i class="fas fa-lock fa-lg text-white"></i>
                        </div>
                        <div>
                            <h5 class="mb-1 fw-bold">Security & Compliance</h5>
                            <p class="mb-0 text-white-75 small">Enterprise-grade protection</p>
                        </div>
                    </div>
                </div>
                <div class="card-body p-4">
                    <div class="row g-3">
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-certificate text-danger me-2"></i>
                                <strong>SOC 2 Compliance</strong><br>
                                <small class="text-muted">Industry standards</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-key text-danger me-2"></i>
                                <strong>End-to-End Encryption</strong><br>
                                <small class="text-muted">Data protection</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-user-shield text-danger me-2"></i>
                                <strong>SSO Integration</strong><br>
                                <small class="text-muted">Seamless authentication</small>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="feature-item">
                                <i class="fas fa-history text-danger me-2"></i>
                                <strong>Audit Logs</strong><br>
                                <small class="text-muted">Complete activity tracking</small>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature Comparison -->
    <div class="row">
        <div class="col-12">
            <div class="card border-0 shadow-lg">
                <div class="card-header bg-gradient-dark text-white border-0 py-4 text-center">
                    <h4 class="mb-0 fw-bold">
                        <i class="fas fa-table me-2"></i>Feature Comparison
                    </h4>
                </div>
                <div class="card-body p-0">
                    <div class="table-responsive">
                        <table class="table table-hover mb-0">
                            <thead class="table-light">
                                <tr>
                                    <th class="fw-bold">Feature</th>
                                    <th class="text-center fw-bold">Starter</th>
                                    <th class="text-center fw-bold">Professional</th>
                                    <th class="text-center fw-bold">Enterprise</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td class="fw-bold">Training Games</td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">AI Assistants</td>
                                    <td class="text-center">5</td>
                                    <td class="text-center">25</td>
                                    <td class="text-center">Unlimited</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Team Members</td>
                                    <td class="text-center">10</td>
                                    <td class="text-center">100</td>
                                    <td class="text-center">Unlimited</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Advanced Analytics</td>
                                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">API Access</td>
                                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                    <td class="text-center">Limited</td>
                                    <td class="text-center">Full</td>
                                </tr>
                                <tr>
                                    <td class="fw-bold">Priority Support</td>
                                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                    <td class="text-center"><i class="fas fa-times text-danger"></i></td>
                                    <td class="text-center"><i class="fas fa-check text-success"></i></td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
/* Features Hero Section */
.features-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Features Stats */
.features-stats .stat-item {
    transition: all 0.3s ease;
}

.features-stats .stat-item:hover {
    transform: translateY(-5px);
}

/* Feature Cards */
.feature-card {
    transition: all 0.3s ease;
    border-radius: 1rem;
    overflow: hidden;
}

.feature-card:hover {
    transform: translateY(-10px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15) !important;
}

.feature-icon {
    width: 50px;
    height: 50px;
    display: flex;
    align-items: center;
    justify-content: center;
}

/* Feature Lists */
.feature-list li {
    transition: all 0.3s ease;
    padding: 0.5rem;
    border-radius: 0.5rem;
}

.feature-list li:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(10px);
}

/* Feature Items */
.feature-item {
    transition: all 0.3s ease;
    padding: 1rem;
    border-radius: 0.5rem;
    margin-bottom: 1rem;
}

.feature-item:hover {
    background: rgba(102, 126, 234, 0.05);
    transform: translateX(5px);
}

/* Table Styling */
.table th {
    border-top: none;
    font-weight: 600;
    background: #f8f9fa;
}

.table td {
    vertical-align: middle;
    padding: 1rem;
}

.table-hover tbody tr:hover {
    background: rgba(102, 126, 234, 0.05);
}

/* Cards */
.card {
    transition: all 0.3s ease;
}

.card:hover {
    transform: translateY(-5px);
}

/* Responsive Design */
@media (max-width: 768px) {
    .features-hero .hero-content {
        padding: 2rem !important;
    }

    .display-4 {
        font-size: 2.5rem;
    }

    .features-stats .stat-number {
        font-size: 2rem;
    }

    .feature-card .card-header {
        padding: 1.5rem !important;
    }

    .feature-icon {
        width: 40px;
        height: 40px;
    }

    .table-responsive {
        font-size: 0.9rem;
    }
}
</style>
{% endblock %}
{% endblock %}
