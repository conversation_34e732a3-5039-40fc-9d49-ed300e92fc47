{% extends 'corporate/base.html' %}

{% block title %}Login - Corporate Prompt Master{% endblock %}

{% block content %}
<div class="container py-5">
    <div class="row justify-content-center">
        <div class="col-lg-5 col-md-7">
            <div class="card shadow-lg border-0">
                <div class="card-header bg-gradient-primary text-white text-center py-4">
                    <div class="mb-3">
                        <i class="fas fa-building fa-3x text-white-50"></i>
                    </div>
                    <h3 class="mb-0 fw-bold">Corporate Login</h3>
                    <p class="mb-0 text-white-75">Access your corporate account</p>
                </div>
                <div class="card-body p-4">
                    <form method="post" novalidate class="needs-validation">
                        {% csrf_token %}

                        {% if form.non_field_errors %}
                        <div class="alert alert-danger border-0 rounded-3">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {% for error in form.non_field_errors %}
                            {{ error }}
                            {% endfor %}
                        </div>
                        {% endif %}

                        <div class="mb-4">
                            <label for="{{ form.username.id_for_label }}" class="form-label fw-semibold">
                                <i class="fas fa-user me-2 text-primary"></i>Username or Email
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-envelope text-muted"></i>
                                </span>
                                {{ form.username }}
                            </div>
                            {% if form.username.errors %}
                            <div class="invalid-feedback d-block">
                                <i class="fas fa-exclamation-circle me-1"></i>
                                {% for error in form.username.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-4">
                            <label for="{{ form.password.id_for_label }}" class="form-label fw-semibold">
                                <i class="fas fa-lock me-2 text-primary"></i>Password
                            </label>
                            <div class="input-group">
                                <span class="input-group-text bg-light border-end-0">
                                    <i class="fas fa-key text-muted"></i>
                                </span>
                                {{ form.password }}
                            </div>
                            {% if form.password.errors %}
                            <div class="invalid-feedback d-block">
                                <i class="fas fa-exclamation-circle me-1"></i>
                                {% for error in form.password.errors %}
                                {{ error }}
                                {% endfor %}
                            </div>
                            {% endif %}
                        </div>

                        <div class="mb-4">
                            <div class="form-check">
                                {{ form.remember_me }}
                                <label class="form-check-label" for="{{ form.remember_me.id_for_label }}">
                                    <i class="fas fa-clock me-1 text-muted"></i>Remember me for 30 days
                                </label>
                            </div>
                        </div>

                        <div class="d-grid mb-3">
                            <button type="submit" class="btn btn-primary btn-lg py-3 fw-semibold">
                                <i class="fas fa-sign-in-alt me-2"></i>Sign In to Corporate Account
                            </button>
                        </div>

                        <div class="text-center">
                            <a href="#" class="text-decoration-none text-muted small">
                                <i class="fas fa-question-circle me-1"></i>Forgot your password?
                            </a>
                        </div>
                    </form>
                </div>
                <div class="card-footer bg-light text-center py-3">
                    <p class="mb-0 text-muted">
                        Don't have an account?
                        <a href="{% url 'corporate:corporate_register' %}" class="text-primary fw-semibold text-decoration-none">
                            <i class="fas fa-user-plus me-1"></i>Register here
                        </a>
                    </p>
                </div>
            </div>
        </div>
    </div>
</div>
{% endblock %}
