"""
Django management command to create test certificates for testing purposes.
"""
import json
from django.core.management.base import BaseCommand
from django.contrib.auth.models import User
from django.utils import timezone
from datetime import timed<PERSON>ta
from corporate.models import Company, CorporateUser, Certificate
from game.models import GameSession


class Command(BaseCommand):
    help = 'Create test certificates for testing the certificate system'

    def add_arguments(self, parser):
        parser.add_argument(
            '--count',
            type=int,
            default=3,
            help='Number of test certificates to create (default: 3)'
        )
        parser.add_argument(
            '--user',
            type=str,
            help='Username to create certificates for (default: admin)'
        )

    def handle(self, *args, **options):
        count = options['count']
        username = options.get('user', 'admin')

        self.stdout.write(f"Creating {count} test certificates...")

        # Get or create test user
        try:
            user = User.objects.get(username=username)
            self.stdout.write(f"Using existing user: {user.username}")
        except User.DoesNotExist:
            self.stdout.write(f"User '{username}' not found. Creating new user...")
            user = User.objects.create_user(
                username=username,
                email=f"{username}@example.com",
                password="testpass123",
                first_name="Test",
                last_name="User"
            )
            self.stdout.write(f"Created user: {user.username}")

        # Get or create test company
        company, created = Company.objects.get_or_create(
            name="Test Company",
            defaults={
                'description': 'A test company for certificate generation',
                'phone_number': '******-0123',
                'is_active': True,
                'pending_approval': False
            }
        )
        if created:
            self.stdout.write(f"Created company: {company.name}")
        else:
            self.stdout.write(f"Using existing company: {company.name}")

        # Get or create corporate user
        corporate_user, created = CorporateUser.objects.get_or_create(
            user=user,
            company=company,
            defaults={
                'is_company_admin': True,
                'department': 'IT',
                'job_title': 'Test Manager'
            }
        )
        if created:
            self.stdout.write(f"Created corporate user for: {user.username}")

        # Certificate templates and data
        certificate_templates = [
            {
                'title': 'Prompt Engineering Fundamentals',
                'description': 'Successfully completed the fundamentals of prompt engineering training.',
                'template': 'standard',
                'score': 85,
                'role': 'Prompt Engineer',
                'games': ['Basic Prompting', 'Context Setting', 'Output Formatting']
            },
            {
                'title': 'Advanced Prompt Techniques',
                'description': 'Mastered advanced prompt engineering techniques and best practices.',
                'template': 'advanced',
                'score': 92,
                'role': 'Senior Prompt Engineer',
                'games': ['Chain of Thought', 'Few-Shot Learning', 'Role-Based Prompting']
            },
            {
                'title': 'Corporate Communication Excellence',
                'description': 'Demonstrated excellence in corporate communication through AI-assisted prompting.',
                'template': 'corporate',
                'score': 88,
                'role': 'Communication Specialist',
                'games': ['Business Writing', 'Email Optimization', 'Report Generation']
            },
            {
                'title': 'AI Ethics and Responsible Use',
                'description': 'Completed comprehensive training on ethical AI use and responsible prompting.',
                'template': 'ethics',
                'score': 95,
                'role': 'AI Ethics Specialist',
                'games': ['Bias Detection', 'Ethical Guidelines', 'Responsible AI Use']
            },
            {
                'title': 'Technical Documentation Mastery',
                'description': 'Achieved mastery in creating technical documentation using AI assistance.',
                'template': 'technical',
                'score': 90,
                'role': 'Technical Writer',
                'games': ['API Documentation', 'User Guides', 'Technical Specifications']
            }
        ]

        created_certificates = []

        for i in range(count):
            # Use different templates cyclically
            template_data = certificate_templates[i % len(certificate_templates)]
            
            # Create a game session (without company for now to avoid FK constraint issues)
            game_session = GameSession.objects.create(
                user=user,
                session_id=f"test_session_{i+1}_{timezone.now().strftime('%Y%m%d_%H%M%S')}",
                game_completed=True,
                performance_score=template_data['score'],
                current_role=template_data['role'],
                created_at=timezone.now() - timedelta(days=i*7),  # Spread certificates over time
                updated_at=timezone.now() - timedelta(days=i*7)
            )

            # Create certificate
            certificate = Certificate.objects.create(
                user=user,
                game_session=game_session,
                title=template_data['title'],
                description=template_data['description'],
                template=template_data['template'],
                final_score=template_data['score'],
                highest_role=template_data['role'],
                completed_games=json.dumps(template_data['games']),
                issue_date=timezone.now() - timedelta(days=i*7)
            )

            created_certificates.append(certificate)
            self.stdout.write(
                self.style.SUCCESS(
                    f"Created certificate {i+1}: {certificate.title} "
                    f"(Code: {certificate.verification_code})"
                )
            )

        self.stdout.write(
            self.style.SUCCESS(
                f"\nSuccessfully created {len(created_certificates)} test certificates!"
            )
        )
        
        self.stdout.write("\nCertificate Details:")
        for cert in created_certificates:
            self.stdout.write(f"  • {cert.title}")
            self.stdout.write(f"    Code: {cert.verification_code}")
            self.stdout.write(f"    Score: {cert.final_score}")
            self.stdout.write(f"    Role: {cert.highest_role}")
            self.stdout.write(f"    Date: {cert.issue_date.strftime('%Y-%m-%d')}")
            self.stdout.write("")

        self.stdout.write(
            self.style.SUCCESS(
                f"You can now view certificates at: http://127.0.0.1:8000/corporate/certificates/"
            )
        )
