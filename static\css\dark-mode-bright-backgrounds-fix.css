/**
 * Dark Mode Bright Backgrounds Fix
 * This file fixes all bright background classes to be dark mode compatible
 */

/* ===== BOOTSTRAP BACKGROUND CLASSES ===== */

/* Fix bg-white class */
.dark-mode .bg-white,
body.dark-mode .bg-white,
html.dark-mode .bg-white,
[data-theme="dark"] .bg-white {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Fix bg-light class */
.dark-mode .bg-light,
body.dark-mode .bg-light,
html.dark-mode .bg-light,
[data-theme="dark"] .bg-light {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

/* Fix bg-secondary class */
.dark-mode .bg-secondary,
body.dark-mode .bg-secondary,
html.dark-mode .bg-secondary,
[data-theme="dark"] .bg-secondary {
    background-color: #1a1a1a !important;
    color: #ffffff !important;
}

/* Fix bg-gradient-light class */
.dark-mode .bg-gradient-light,
body.dark-mode .bg-gradient-light,
html.dark-mode .bg-gradient-light,
[data-theme="dark"] .bg-gradient-light {
    background: linear-gradient(135deg, #2a2a2a 0%, #1e1e1e 100%) !important;
    color: #ffffff !important;
}

/* ===== WHITE BACKGROUND OPACITY CLASSES ===== */

/* Fix bg-white with opacity */
.dark-mode .bg-white.bg-opacity-25,
body.dark-mode .bg-white.bg-opacity-25,
html.dark-mode .bg-white.bg-opacity-25,
[data-theme="dark"] .bg-white.bg-opacity-25 {
    background-color: rgba(255, 255, 255, 0.1) !important;
    color: #ffffff !important;
}

.dark-mode .bg-white.bg-opacity-10,
body.dark-mode .bg-white.bg-opacity-10,
html.dark-mode .bg-white.bg-opacity-10,
[data-theme="dark"] .bg-white.bg-opacity-10 {
    background-color: rgba(255, 255, 255, 0.05) !important;
    color: #ffffff !important;
}

/* ===== CARD BACKGROUNDS ===== */

/* Fix card backgrounds */
.dark-mode .card,
body.dark-mode .card,
html.dark-mode .card,
[data-theme="dark"] .card {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Fix card body */
.dark-mode .card-body,
body.dark-mode .card-body,
html.dark-mode .card-body,
[data-theme="dark"] .card-body {
    background-color: inherit !important;
    color: #ffffff !important;
}

/* ===== FEATURE CARD BACKGROUNDS ===== */

/* Fix feature cards */
.dark-mode .feature-card,
body.dark-mode .feature-card,
html.dark-mode .feature-card,
[data-theme="dark"] .feature-card {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
}

/* ===== BADGE BACKGROUNDS ===== */

/* Fix badge backgrounds */
.dark-mode .badge.bg-white,
body.dark-mode .badge.bg-white,
html.dark-mode .badge.bg-white,
[data-theme="dark"] .badge.bg-white {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

.dark-mode .badge.bg-light,
body.dark-mode .badge.bg-light,
html.dark-mode .badge.bg-light,
[data-theme="dark"] .badge.bg-light {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

/* ===== NAVBAR BACKGROUNDS ===== */

/* Fix navbar-light bg-white */
.dark-mode .navbar-light.bg-white,
body.dark-mode .navbar-light.bg-white,
html.dark-mode .navbar-light.bg-white,
[data-theme="dark"] .navbar-light.bg-white {
    background-color: #1e1e1e !important;
}

/* ===== MODAL BACKGROUNDS ===== */

/* Fix modal backgrounds */
.dark-mode .modal-content,
body.dark-mode .modal-content,
html.dark-mode .modal-content,
[data-theme="dark"] .modal-content {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

.dark-mode .modal-header,
body.dark-mode .modal-header,
html.dark-mode .modal-header,
[data-theme="dark"] .modal-header {
    background-color: #2a2a2a !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

.dark-mode .modal-body,
body.dark-mode .modal-body,
html.dark-mode .modal-body,
[data-theme="dark"] .modal-body {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

.dark-mode .modal-footer,
body.dark-mode .modal-footer,
html.dark-mode .modal-footer,
[data-theme="dark"] .modal-footer {
    background-color: #2a2a2a !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* ===== DROPDOWN BACKGROUNDS ===== */

/* Fix dropdown backgrounds */
.dark-mode .dropdown-menu,
body.dark-mode .dropdown-menu,
html.dark-mode .dropdown-menu,
[data-theme="dark"] .dropdown-menu {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
}

.dark-mode .dropdown-item,
body.dark-mode .dropdown-item,
html.dark-mode .dropdown-item,
[data-theme="dark"] .dropdown-item {
    color: #ffffff !important;
}

.dark-mode .dropdown-item:hover,
body.dark-mode .dropdown-item:hover,
html.dark-mode .dropdown-item:hover,
[data-theme="dark"] .dropdown-item:hover {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

/* ===== TABLE BACKGROUNDS ===== */

/* Fix table backgrounds */
.dark-mode .table,
body.dark-mode .table,
html.dark-mode .table,
[data-theme="dark"] .table {
    color: #ffffff !important;
}

.dark-mode .table th,
body.dark-mode .table th,
html.dark-mode .table th,
[data-theme="dark"] .table th {
    background-color: #2a2a2a !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

.dark-mode .table td,
body.dark-mode .table td,
html.dark-mode .table td,
[data-theme="dark"] .table td {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* ===== FORM BACKGROUNDS ===== */

/* Fix form control backgrounds */
.dark-mode .form-control,
body.dark-mode .form-control,
html.dark-mode .form-control,
[data-theme="dark"] .form-control {
    background-color: #2a2a2a !important;
    border-color: #555555 !important;
    color: #ffffff !important;
}

.dark-mode .form-control:focus,
body.dark-mode .form-control:focus,
html.dark-mode .form-control:focus,
[data-theme="dark"] .form-control:focus {
    background-color: #2a2a2a !important;
    border-color: #007acc !important;
    color: #ffffff !important;
    box-shadow: 0 0 0 0.2rem rgba(0, 122, 204, 0.25) !important;
}

/* Fix form select backgrounds */
.dark-mode .form-select,
body.dark-mode .form-select,
html.dark-mode .form-select,
[data-theme="dark"] .form-select {
    background-color: #2a2a2a !important;
    border-color: #555555 !important;
    color: #ffffff !important;
}

/* ===== ALERT BACKGROUNDS ===== */

/* Fix alert backgrounds */
.dark-mode .alert-light,
body.dark-mode .alert-light,
html.dark-mode .alert-light,
[data-theme="dark"] .alert-light {
    background-color: #2a2a2a !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* ===== BREADCRUMB BACKGROUNDS ===== */

/* Fix breadcrumb backgrounds */
.dark-mode .breadcrumb,
body.dark-mode .breadcrumb,
html.dark-mode .breadcrumb,
[data-theme="dark"] .breadcrumb {
    background-color: #2a2a2a !important;
}

.dark-mode .breadcrumb-item a,
body.dark-mode .breadcrumb-item a,
html.dark-mode .breadcrumb-item a,
[data-theme="dark"] .breadcrumb-item a {
    color: #007acc !important;
}

/* ===== INLINE STYLE OVERRIDES ===== */

/* Fix inline white backgrounds */
.dark-mode [style*="background-color: white"],
body.dark-mode [style*="background-color: white"],
html.dark-mode [style*="background-color: white"],
[data-theme="dark"] [style*="background-color: white"],
.dark-mode [style*="background-color: #fff"],
body.dark-mode [style*="background-color: #fff"],
html.dark-mode [style*="background-color: #fff"],
[data-theme="dark"] [style*="background-color: #fff"],
.dark-mode [style*="background-color: rgb(255, 255, 255)"],
body.dark-mode [style*="background-color: rgb(255, 255, 255)"],
html.dark-mode [style*="background-color: rgb(255, 255, 255)"],
[data-theme="dark"] [style*="background-color: rgb(255, 255, 255)"] {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

/* Fix inline light backgrounds */
.dark-mode [style*="background-color: #f8f9fa"],
body.dark-mode [style*="background-color: #f8f9fa"],
html.dark-mode [style*="background-color: #f8f9fa"],
[data-theme="dark"] [style*="background-color: #f8f9fa"],
.dark-mode [style*="background-color: rgb(248, 249, 250)"],
body.dark-mode [style*="background-color: rgb(248, 249, 250)"],
html.dark-mode [style*="background-color: rgb(248, 249, 250)"],
[data-theme="dark"] [style*="background-color: rgb(248, 249, 250)"] {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

/* ===== SPECIFIC COMPONENT FIXES ===== */

/* Fix superadmin dashboard bright backgrounds */
.dark-mode .bg-gradient-danger,
body.dark-mode .bg-gradient-danger,
html.dark-mode .bg-gradient-danger,
[data-theme="dark"] .bg-gradient-danger {
    background: linear-gradient(135deg, #dc3545 0%, #c82333 100%) !important;
    color: #ffffff !important;
}

/* Fix testimonial and demo sections */
.dark-mode .testimonial-avatar .bg-info,
body.dark-mode .testimonial-avatar .bg-info,
html.dark-mode .testimonial-avatar .bg-info,
[data-theme="dark"] .testimonial-avatar .bg-info {
    background-color: #17a2b8 !important;
    color: #ffffff !important;
}

/* Fix navbar light backgrounds */
.dark-mode .navbar-light,
body.dark-mode .navbar-light,
html.dark-mode .navbar-light,
[data-theme="dark"] .navbar-light {
    background-color: #1e1e1e !important;
}

/* Fix list group backgrounds */
.dark-mode .list-group-item,
body.dark-mode .list-group-item,
html.dark-mode .list-group-item,
[data-theme="dark"] .list-group-item {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

/* Fix pagination backgrounds */
.dark-mode .page-link,
body.dark-mode .page-link,
html.dark-mode .page-link,
[data-theme="dark"] .page-link {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
    color: #ffffff !important;
}

.dark-mode .page-item.active .page-link,
body.dark-mode .page-item.active .page-link,
html.dark-mode .page-item.active .page-link,
[data-theme="dark"] .page-item.active .page-link {
    background-color: #007acc !important;
    border-color: #007acc !important;
    color: #ffffff !important;
}

/* ===== TEXT COLOR FIXES ===== */

/* Fix text colors on bright backgrounds */
.dark-mode .text-dark,
body.dark-mode .text-dark,
html.dark-mode .text-dark,
[data-theme="dark"] .text-dark {
    color: #ffffff !important;
}

.dark-mode .text-muted,
body.dark-mode .text-muted,
html.dark-mode .text-muted,
[data-theme="dark"] .text-muted {
    color: #cccccc !important;
}

/* ===== BORDER FIXES ===== */

/* Fix bright borders */
.dark-mode .border-light,
body.dark-mode .border-light,
html.dark-mode .border-light,
[data-theme="dark"] .border-light {
    border-color: #444444 !important;
}

.dark-mode .border-white,
body.dark-mode .border-white,
html.dark-mode .border-white,
[data-theme="dark"] .border-white {
    border-color: #444444 !important;
}

/* ===== SPECIFIC PAGE FIXES ===== */

/* Fix certificates page empty state */
.dark-mode .empty-state,
body.dark-mode .empty-state,
html.dark-mode .empty-state,
[data-theme="dark"] .empty-state {
    background: linear-gradient(145deg, #1e1e1e, #2a2a2a) !important;
    color: #ffffff !important;
}

/* Fix certificate details background */
.dark-mode .certificate-details,
body.dark-mode .certificate-details,
html.dark-mode .certificate-details,
[data-theme="dark"] .certificate-details {
    background: linear-gradient(145deg, #1e1e1e, #2a2a2a) !important;
    color: #ffffff !important;
}

/* Fix certificate preview backgrounds */
.dark-mode .certificate-preview,
body.dark-mode .certificate-preview,
html.dark-mode .certificate-preview,
[data-theme="dark"] .certificate-preview {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

.dark-mode .certificate-inner,
body.dark-mode .certificate-inner,
html.dark-mode .certificate-inner,
[data-theme="dark"] .certificate-inner {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

/* Fix any remaining bright gradients */
.dark-mode [style*="background: linear-gradient(145deg, #f8f9fa, #e9ecef)"],
body.dark-mode [style*="background: linear-gradient(145deg, #f8f9fa, #e9ecef)"],
html.dark-mode [style*="background: linear-gradient(145deg, #f8f9fa, #e9ecef)"],
[data-theme="dark"] [style*="background: linear-gradient(145deg, #f8f9fa, #e9ecef)"] {
    background: linear-gradient(145deg, #1e1e1e, #2a2a2a) !important;
    color: #ffffff !important;
}

/* Fix any elements with light gray backgrounds */
.dark-mode [style*="background-color: #f8f9fa"],
body.dark-mode [style*="background-color: #f8f9fa"],
html.dark-mode [style*="background-color: #f8f9fa"],
[data-theme="dark"] [style*="background-color: #f8f9fa"],
.dark-mode [style*="background-color: #e9ecef"],
body.dark-mode [style*="background-color: #e9ecef"],
html.dark-mode [style*="background-color: #e9ecef"],
[data-theme="dark"] [style*="background-color: #e9ecef"] {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

/* ===== CATCH-ALL BRIGHT BACKGROUND FIXES ===== */

/* Fix any remaining bright backgrounds with high specificity */
.dark-mode *[class*="bg-white"],
body.dark-mode *[class*="bg-white"],
html.dark-mode *[class*="bg-white"],
[data-theme="dark"] *[class*="bg-white"] {
    background-color: #1e1e1e !important;
    color: #ffffff !important;
}

.dark-mode *[class*="bg-light"],
body.dark-mode *[class*="bg-light"],
html.dark-mode *[class*="bg-light"],
[data-theme="dark"] *[class*="bg-light"] {
    background-color: #2a2a2a !important;
    color: #ffffff !important;
}

/* Fix any div or section with bright backgrounds */
.dark-mode div[style*="background"],
body.dark-mode div[style*="background"],
html.dark-mode div[style*="background"],
[data-theme="dark"] div[style*="background"],
.dark-mode section[style*="background"],
body.dark-mode section[style*="background"],
html.dark-mode section[style*="background"],
[data-theme="dark"] section[style*="background"] {
    filter: brightness(0.3) contrast(1.2) !important;
}

/* Restore proper colors for text elements */
.dark-mode div[style*="background"] *,
body.dark-mode div[style*="background"] *,
html.dark-mode div[style*="background"] *,
[data-theme="dark"] div[style*="background"] *,
.dark-mode section[style*="background"] *,
body.dark-mode section[style*="background"] *,
html.dark-mode section[style*="background"] *,
[data-theme="dark"] section[style*="background"] * {
    color: #ffffff !important;
    filter: none !important;
}
