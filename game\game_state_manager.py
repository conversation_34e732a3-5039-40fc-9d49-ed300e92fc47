"""
Game State Manager for Rwenzori Innovations Game

This module handles game state management, including progression, promotions,
and tracking player performance.
"""

import logging
from .enhanced_evaluation import MINIMUM_PROMOTION_SCORE

# Configure logging
logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(levelname)s - %(message)s')

def check_for_promotion(game_state, role_progression):
    """
    Check if the player should be promoted to the next role.

    Args:
        game_state (dict): The current game state
        role_progression (dict): The role progression configuration

    Returns:
        tuple: (promoted, promotion_message)
    """
    current_role = game_state.get("current_role")
    role_info = role_progression.get(current_role, {})

    # Get the minimum score required for promotion (default to MINIMUM_PROMOTION_SCORE)
    min_promotion_score = role_info.get("minimum_promotion_score", MINIMUM_PROMOTION_SCORE)
    challenges_required = role_info.get("challenges_required", 3)

    logging.info(f"Checking for promotion: Role={current_role}, " +
                f"Completed={game_state.get('role_challenges_completed', 0)}/{challenges_required}, " +
                f"Avg Score={game_state.get('role_average_score', 0):.2f}/{min_promotion_score}")

    # Check if player has completed enough challenges
    # We're making this more lenient - just requiring enough challenges completed
    # The average score requirement is commented out for now to make progression easier
    if game_state.get("role_challenges_completed", 0) >= challenges_required: # and
        # game_state.get("role_average_score", 0) >= min_promotion_score:

        next_role = role_info.get("next_role")
        promotion_message = role_info.get("promotion_message", f"Congratulations! You've been promoted to {next_role}!")

        # Add current role to completed roles
        if current_role not in game_state.get("completed_roles", []):
            if "completed_roles" not in game_state:
                game_state["completed_roles"] = []
            game_state["completed_roles"].append(current_role)

        # If there's a next role, promote the player
        if next_role:
            game_state["current_role"] = next_role
            game_state["role_challenges_completed"] = 0
            game_state["role_average_score"] = 0  # Reset for new role

            # Get the first task for the new role
            from .all_role_tasks import get_all_role_tasks
            new_role_tasks = get_all_role_tasks(next_role)
            if new_role_tasks:
                first_task = new_role_tasks[0]
                game_state["current_task"] = first_task["id"]  # Set to proper task ID string
                game_state["current_manager"] = first_task["manager"]
                logging.info(f"Set first task for new role {next_role}: {first_task['id']} with manager {first_task['manager']}")
            else:
                logging.error(f"No tasks found for role {next_role}")
                game_state["current_task"] = None

            logging.info(f"Player promoted to {next_role}")
            return True, promotion_message

    return False, None

def update_role_average_score(game_state, new_score):
    """
    Update the average score for the current role.

    Args:
        game_state (dict): The current game state
        new_score (float): The new score to incorporate

    Returns:
        float: The updated average score
    """
    # Get current values
    current_avg = game_state.get("role_average_score", 0)
    completed_tasks = game_state.get("role_challenges_completed", 0)

    # Calculate new average
    if completed_tasks == 0:
        new_avg = new_score
    else:
        # Weighted average calculation
        total = current_avg * completed_tasks
        new_avg = (total + new_score) / (completed_tasks + 1)

    # Update game state
    game_state["role_average_score"] = new_avg

    logging.info(f"Updated role average score: {new_avg:.2f}")
    return new_avg

def get_mastery_level(average_score):
    """
    Determine the mastery level based on the average score.

    Args:
        average_score (float): The player's average score

    Returns:
        str: The mastery level (Beginner, Intermediate, Advanced, Expert)
    """
    if average_score >= 9:
        return "Expert"
    elif average_score >= 7.5:
        return "Advanced"
    elif average_score >= 6:
        return "Intermediate"
    else:
        return "Beginner"

def get_next_task(game_state, tasks_by_role):
    """
    Get the next task for the player.

    Args:
        game_state (dict): The current game state
        tasks_by_role (dict): The tasks organized by role

    Returns:
        dict: The next task
    """
    current_role = game_state.get("current_role")
    current_task_index = game_state.get("current_task", 0)

    # Get tasks for the current role
    role_tasks = tasks_by_role.get(current_role, [])

    # If there are no more tasks for this role, return None
    if current_task_index >= len(role_tasks):
        return None

    # Get the next task
    next_task = role_tasks[current_task_index]

    return next_task

def process_task_completion(game_state, evaluation_results, points_earned):
    """
    Process the completion of a task.

    Args:
        game_state (dict): The current game state
        evaluation_results (dict): The evaluation results
        points_earned (int): The points earned for this task

    Returns:
        dict: Updated game state
    """
    print(f"\n\nProcessing task completion\n\n")
    print(f"\n\nGame state before: {game_state}\n\n")
    print(f"\n\nEvaluation results: {evaluation_results}\n\n")
    print(f"\n\nPoints earned: {points_earned}\n\n")

    # Ensure minimum points for any completed task
    if points_earned < 5:
        points_earned = 5
        print(f"\n\nAdjusting points to minimum value: {points_earned}\n\n")

    # Update performance score
    old_score = game_state.get("performance_score", 0)
    game_state["performance_score"] = old_score + points_earned
    print(f"\n\nUpdating performance score: {old_score} + {points_earned} = {game_state['performance_score']}\n\n")

    # Update challenges completed
    # game_state["challenges_completed"] = game_state.get("challenges_completed", 0) + 1 # Already incremented in views.py
    # game_state["role_challenges_completed"] = game_state.get("role_challenges_completed", 0) + 1 # Already incremented in views.py

    print(f"\n\nUpdated challenges completed: total={game_state['challenges_completed']}, role={game_state['role_challenges_completed']}\n\n")

    # Update current task if it's a number
    if isinstance(game_state.get("current_task"), int):
        game_state["current_task"] = game_state.get("current_task", 0) + 1
    # If current_task is a string (task_id), we don't increment it
    # The task progression will be handled in the submit_prompt function

    # Update role average score
    update_role_average_score(game_state, evaluation_results["overall_score"])

    # Update mastery level
    game_state["mastery_level"] = get_mastery_level(game_state.get("role_average_score", 0))

    # Update attempts for current task
    game_state["current_task_attempts"] = 0

    # Mark the task as completed
    game_state["task_completed"] = True

    # Set just_completed_task flag to true
    game_state["just_completed_task"] = True

    # Import here to avoid circular imports
    from .role_progression import ROLE_PROGRESSION

    # Check for promotion
    current_role = game_state.get("current_role")
    print(f"\n\nChecking for promotion. Current role: {current_role}, Role challenges completed: {game_state['role_challenges_completed']}\n\n")

    # Get the challenges required for the current role
    role_info = ROLE_PROGRESSION.get(current_role, {})
    challenges_required = role_info.get("challenges_required", 3)
    print(f"\n\nChallenges required for promotion: {challenges_required}\n\n")

    # Check if player has completed enough challenges for promotion
    promoted, promotion_message = check_for_promotion(game_state, ROLE_PROGRESSION)

    if promoted:
        print(f"\n\nPROMOTION OCCURRED! New role: {game_state['current_role']}\n\n")

        # Set promoted flag to true
        game_state["promoted"] = True

        # Set next_task_pending flag to true so frontend knows to fetch the next task
        game_state["next_task_pending"] = True

        # Task setting is already handled in check_for_promotion function

        # Add promotion message to messages if needed
        if "messages" in game_state and promotion_message:
            import uuid
            import markdown

            # Create a promotion message
            promotion_html = markdown.markdown(promotion_message, extensions=['extra'])
            promotion_message_obj = {
                "id": str(uuid.uuid4()),
                "sender": "ceo",  # CEO delivers promotions
                "text": promotion_message,
                "html": promotion_html,
                "timestamp": "now",  # This will be replaced with actual timestamp in views.py
                "is_challenge": False,
                "is_markdown": True,
                "is_promotion": True  # Mark as promotion message
            }
            game_state["messages"].append(promotion_message_obj)
    else:
        # Not promoted, just completed a task
        game_state["promoted"] = False
        print(f"\n\nNo promotion occurred. Continuing in role: {current_role}\n\n")

    logging.info(f"Task completed: Points={points_earned}, " +
                f"Total={game_state['performance_score']}, " +
                f"Mastery={game_state['mastery_level']}")

    return game_state

def process_task_failure(game_state):
    """
    Process a failed task attempt.

    Args:
        game_state (dict): The current game state

    Returns:
        dict: Updated game state
    """
    # Increment attempts for current task
    game_state["current_task_attempts"] = game_state.get("current_task_attempts", 0) + 1

    logging.info(f"Task attempt failed: Attempts={game_state['current_task_attempts']}")

    return game_state

def get_performance_summary(game_state):
    """
    Get a summary of the player's performance.

    Args:
        game_state (dict): The current game state

    Returns:
        dict: Performance summary
    """
    return {
        "current_role": game_state.get("current_role", "Intern"),
        "mastery_level": game_state.get("mastery_level", "Beginner"),
        "performance_score": game_state.get("performance_score", 0),
        "challenges_completed": game_state.get("challenges_completed", 0),
        "role_challenges_completed": game_state.get("role_challenges_completed", 0),
        "role_average_score": game_state.get("role_average_score", 0),
        "completed_roles": game_state.get("completed_roles", [])
    }
