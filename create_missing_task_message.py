#!/usr/bin/env python
"""
<PERSON><PERSON><PERSON> to create the missing task message for the current task.
"""
import os
import django
import sys
import uuid
from django.utils import timezone
import markdown

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from game.models import GameSession, Message
from game.all_role_tasks import get_all_role_tasks

def create_missing_task_message():
    """Create the missing task message for the current task"""
    print("Creating missing task message...")
    
    # Get the most recent game session
    session = GameSession.objects.last()
    if not session:
        print("No game sessions found")
        return
    
    print(f"Current state:")
    print(f"  Role: {session.current_role}")
    print(f"  Current task: {session.current_task}")
    print(f"  Current manager: {session.current_manager}")
    
    # Get the current task details
    current_role_tasks = get_all_role_tasks(session.current_role)
    current_task_details = None
    for task in current_role_tasks:
        if task["id"] == session.current_task:
            current_task_details = task
            break
    
    if not current_task_details:
        print(f"ERROR: Could not find task details for {session.current_task}")
        return
    
    print(f"Found task details: {current_task_details['id']}")
    
    # Check if a message already exists for this task
    existing_message = Message.objects.filter(
        game_session=session,
        task_id=session.current_task,
        is_challenge=True
    ).order_by('-timestamp').first()
    
    if existing_message:
        print(f"Task message already exists (created at {existing_message.timestamp})")
        print(f"Message preview: {existing_message.text[:100]}...")
        
        # Check if it's recent (within last 2 hours)
        from datetime import timedelta
        cutoff_time = timezone.now() - timedelta(hours=2)
        if existing_message.timestamp > cutoff_time:
            print("Message is recent, no need to create a new one")
            return
        else:
            print("Message is old, creating a new one...")
    
    # Create a new task message
    task_html = markdown.markdown(current_task_details["description"], extensions=['extra'])
    new_message = Message.objects.create(
        game_session=session,
        message_id=str(uuid.uuid4()),
        sender=current_task_details["manager"],
        text=current_task_details["description"],
        html=task_html,
        timestamp=timezone.now(),
        is_challenge=True,
        task_id=current_task_details["id"],
        is_markdown=True
    )
    
    print(f"✅ Created new task message: {new_message.id}")
    print(f"Task: {current_task_details['id']}")
    print(f"Manager: {current_task_details['manager']}")
    print(f"Preview: {current_task_details['description'][:100]}...")

if __name__ == "__main__":
    create_missing_task_message()
