{% extends 'corporate/base.html' %}

{% block title %}Certificates - Corporate Prompt Master{% endblock %}

{% block content %}
<!-- Enhanced Certificates Hero Section -->
<section class="certificates-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <h1 class="display-5 fw-bold mb-3 text-shadow">
                                    <i class="fas fa-certificate me-3"></i>
                                    Professional <span class="text-gradient">Certificates</span>
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Showcase your prompt engineering expertise with verified digital certificates.
                                </p>
                                <div class="achievement-badges d-flex flex-wrap gap-3">
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-award me-2"></i>Industry Recognized
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-download me-2"></i>Downloadable
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-shield-alt me-2"></i>Verified
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="certificates-stats text-center">
                                <div class="row g-3">
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ certificates|length }}</div>
                                            <small class="text-white-75">Earned</small>
                                        </div>
                                    </div>
                                    <div class="col-6">
                                        <div class="stat-card bg-white bg-opacity-15 rounded-3 p-3 backdrop-blur">
                                            <div class="h3 fw-bold text-white mb-1">{{ total_levels|default:"8" }}</div>
                                            <small class="text-white-75">Levels</small>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

{% block extra_css %}
<style>
/* Certificates Hero Section */
.certificates-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

.backdrop-blur {
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
}

/* Enhanced Certificate Styling */
.certificate-preview {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border: none;
    border-radius: 1rem;
    padding: 2rem;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.certificate-preview::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="watermark" width="50" height="50" patternUnits="userSpaceOnUse"><text x="25" y="25" font-size="8" fill="rgba(0,0,0,0.05)" text-anchor="middle">CERTIFIED</text></pattern></defs><rect width="100" height="100" fill="url(%23watermark)"/></svg>');
    pointer-events: none;
}

.certificate-preview:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.certificate-inner {
    background: linear-gradient(145deg, #ffffff, #f8f9fa);
    border: 3px solid transparent;
    background-clip: padding-box;
    border-radius: 0.75rem;
    padding: 2rem;
    text-align: center;
    position: relative;
    box-shadow: inset 0 2px 10px rgba(0,0,0,0.05);
}

.certificate-inner::before {
    content: '';
    position: absolute;
    top: -3px;
    left: -3px;
    right: -3px;
    bottom: -3px;
    background: linear-gradient(45deg, #667eea, #764ba2, #f093fb, #f5576c);
    border-radius: 0.75rem;
    z-index: -1;
}

.certificate-header h3 {
    background: linear-gradient(45deg, #667eea, #764ba2);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    font-size: 2rem;
    font-weight: 800;
    margin-bottom: 1.5rem;
    text-transform: uppercase;
    letter-spacing: 2px;
}

.certificate-name {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: 1rem;
    color: #2c3e50;
    position: relative;
    display: inline-block;
    padding-bottom: 0.5rem;
}

.certificate-name::after {
    content: '';
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
    width: 80%;
    height: 3px;
    background: linear-gradient(45deg, #667eea, #764ba2);
    border-radius: 2px;
}

.certificate-text, .certificate-role, .certificate-score, .certificate-date, .certificate-verification {
    margin-bottom: 0.75rem;
    line-height: 1.6;
}

.certificate-role, .certificate-score {
    font-weight: 700;
    color: #667eea;
    font-size: 1.1rem;
}

.certificate-games {
    margin: 1rem auto;
    padding: 1rem;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border: 2px solid #667eea;
    border-radius: 0.75rem;
    box-shadow: inset 0 2px 5px rgba(0,0,0,0.05);
        background-color: rgba(74, 134, 232, 0.1);
        max-width: 90%;
    }

.certificate-games-title {
    font-weight: 700;
    color: #667eea;
    margin-bottom: 0.75rem;
    text-align: center;
    font-size: 1rem;
}

.certificate-games-list {
    list-style: none;
    padding: 0;
    margin: 0;
    text-align: center;
}

.certificate-games-list li {
    font-weight: 600;
    padding: 0.25rem 0;
    font-size: 0.95rem;
    color: #495057;
}

/* Certificate Cards */
.certificate-card {
    transition: all 0.3s ease;
    border: none;
    border-radius: 1rem;
    overflow: hidden;
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.certificate-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 20px 40px rgba(0,0,0,0.15);
}

.certificate-card .card-header {
    background: linear-gradient(45deg, #667eea, #764ba2);
    border: none;
    padding: 1.5rem;
}

.certificate-card .card-body {
    padding: 2rem;
}

.certificate-details {
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 0.75rem;
    padding: 1.5rem;
    margin-bottom: 1.5rem;
}

.certificate-details .row > div {
    margin-bottom: 1rem;
}

.certificate-details strong {
    color: #667eea;
    font-weight: 700;
}

/* Certificate Preview Styles - Match verification page design */
.certificate-preview {
    border: 1px solid #ddd;
    padding: 20px;
    background-color: #f9f9f9;
    border-radius: 8px;
    margin: 0 auto 30px;
    max-width: 800px;
}

.certificate-inner {
    background: #111111 url('data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAAAQAAAAECAYAAACp8Z5+AAAAEklEQVQImWNgYGD4z0AswK4SAFXuAf8EPy+xAAAAAElFTkSuQmCC') repeat;
    border: 12px solid #d4af37;
    padding: 40px;
    text-align: center;
    position: relative;
    min-height: 500px;
    box-shadow: 0 8px 25px rgba(0,0,0,0.6);
    border-radius: 6px;
}

.certificate-header h3 {
    color: #d4af37 !important;
    font-size: 28px !important;
    margin-bottom: 20px !important;
    text-transform: uppercase;
    font-weight: bold;
    text-shadow: 1px 1px 5px rgba(0,0,0,0.5);
    letter-spacing: 2px;
}

.certificate-name {
    font-size: 32px !important;
    font-weight: bold !important;
    margin: 0 auto 20px !important;
    border-bottom: 3px solid #d4af37;
    display: inline-block;
    padding-bottom: 8px;
    color: #ffffff !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.certificate-text {
    margin-bottom: 15px !important;
    font-size: 18px !important;
    color: #d4af37 !important;
    line-height: 1.5;
}

.certificate-role, .certificate-score {
    font-weight: bold !important;
    color: #ffffff !important;
    font-size: 22px !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
    margin-bottom: 15px !important;
}

.certificate-games {
    margin: 15px auto;
    padding: 10px;
    border: 2px solid #d4af37;
    border-radius: 6px;
    background-color: rgba(0, 0, 0, 0.5);
    max-width: 90%;
    box-shadow: 0 2px 5px rgba(0,0,0,0.3);
}

.certificate-games-title {
    font-weight: bold !important;
    color: #d4af37 !important;
    margin-bottom: 12px !important;
    text-align: center;
    font-size: 18px !important;
    text-shadow: 1px 1px 3px rgba(0,0,0,0.5);
}

.certificate-games-list {
    list-style-type: none !important;
    padding: 0 !important;
    margin: 0 !important;
    text-align: center;
}

.certificate-games-list li {
    font-weight: bold !important;
    padding: 4px 0 !important;
    font-size: 16px !important;
    color: #ffffff !important;
}

.certificate-date, .certificate-verification {
    margin-top: 20px !important;
    font-size: 16px !important;
    color: #d4af37 !important;
    margin-bottom: 12px !important;
}

.certificate-verification {
    font-size: 14px !important;
    color: #a0a0a0 !important;
}

/* Add certificate seal */
.certificate-inner::after {
    content: "CERTIFIED";
    position: absolute;
    bottom: 20px;
    right: 20px;
    width: 80px;
    height: 80px;
    background: radial-gradient(circle, #d4af37, #b8860b);
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    color: white;
    font-weight: bold;
    font-size: 12px;
    transform: rotate(-15deg);
    border: 3px solid #8B4513;
    box-shadow: 0 3px 15px rgba(139, 69, 19, 0.4);
    line-height: 1;
    text-align: center;
}

/* Certificate signatures */
.certificate-signatures {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
    padding-top: 15px;
    border-top: 1px solid #d4af37;
}

.signature {
    text-align: center;
    flex: 1;
    margin: 0 10px;
}

.signature-line {
    width: 100%;
    height: 1px;
    background-color: #d4af37;
    margin-bottom: 5px;
}

.signature-name {
    font-weight: bold;
    color: #ffffff;
    font-size: 12px;
    margin-bottom: 2px;
}

.signature-title {
    font-size: 10px;
    color: #d4af37;
    font-style: italic;
}

/* Dark mode overrides for certificate preview */
.dark-mode .certificate-preview,
body.dark-mode .certificate-preview,
html.dark-mode .certificate-preview,
[data-theme="dark"] .certificate-preview {
    background-color: #1e1e1e !important;
    border-color: #444444 !important;
}

/* Action Buttons */
.certificate-actions {
    display: flex;
    flex-wrap: wrap;
    gap: 0.75rem;
    justify-content: center;
}

.certificate-actions .btn {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    font-weight: 600;
    padding: 0.75rem 1.5rem;
}

.certificate-actions .btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Achievement Badges */
.achievement-badges .badge {
    transition: all 0.3s ease;
}

.achievement-badges .badge:hover {
    transform: scale(1.05);
    background-color: rgba(255,255,255,0.4) !important;
}

/* Stats Cards */
.certificates-stats .stat-card {
    transition: all 0.3s ease;
}

.certificates-stats .stat-card:hover {
    transform: scale(1.05);
}

/* Empty State */
.empty-state {
    text-align: center;
    padding: 4rem 2rem;
    background: linear-gradient(145deg, #f8f9fa, #e9ecef);
    border-radius: 1rem;
    margin: 2rem 0;
}

/* Dark mode override for empty state */
.dark-mode .empty-state,
body.dark-mode .empty-state,
html.dark-mode .empty-state,
[data-theme="dark"] .empty-state {
    background: linear-gradient(145deg, #1e1e1e, #2a2a2a) !important;
    color: #ffffff !important;
}

/* Dark mode override for certificate details */
.dark-mode .certificate-details,
body.dark-mode .certificate-details,
html.dark-mode .certificate-details,
[data-theme="dark"] .certificate-details {
    background: linear-gradient(145deg, #1e1e1e, #2a2a2a) !important;
    color: #ffffff !important;
}

.empty-state .empty-icon {
    font-size: 4rem;
    color: #6c757d;
    margin-bottom: 1.5rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .certificates-hero .hero-content {
        padding: 2rem !important;
    }

    .display-5 {
        font-size: 2rem;
    }

    .certificate-actions {
        flex-direction: column;
    }

    .certificate-actions .btn {
        width: 100%;
    }

    /* Certificate preview responsive styles */
    .certificate-inner {
        padding: 15px;
        min-height: 250px;
        border-width: 6px;
    }

    .certificate-name {
        font-size: 20px !important;
    }

    .certificate-header h3 {
        font-size: 18px !important;
    }

    .certificate-inner::after {
        width: 50px;
        height: 50px;
        font-size: 9px;
        bottom: 10px;
        right: 10px;
    }

    .certificate-games {
        max-width: 95%;
        padding: 8px;
    }

    .certificate-role, .certificate-score {
        font-size: 14px !important;
    }

    .certificate-signatures {
        flex-direction: column;
        gap: 15px;
    }

    .signature {
        margin: 0;
    }

    .signature-name {
        font-size: 11px;
    }

    .signature-title {
        font-size: 9px;
    }
}
</style>
{% endblock %}

<!-- Enhanced Certificates Content -->
<div class="container-fluid">
    {% if certificates %}
    <div class="row g-4">
        {% for certificate in certificates %}
        <div class="col-12">
            <div class="card certificate-card h-100">
                <div class="card-header text-white">
                    <div class="d-flex align-items-center">
                        <i class="fas fa-award fa-lg me-3"></i>
                        <div>
                            <h5 class="mb-0 fw-bold">{{ certificate.title }}</h5>
                            <small class="text-white-75">Professional Certification</small>
                        </div>
                    </div>
                </div>

                <div class="card-body">
                    <!-- Certificate Preview -->
                    <div class="certificate-preview mb-4">
                        <div class="certificate-inner">
                            <div class="certificate-header">
                                <h3>Certificate of Completion</h3>
                            </div>
                            <div class="certificate-content">
                                <div class="certificate-name">{{ user.get_full_name|default:user.username }}</div>
                                <p class="certificate-text">{{ certificate.description }}</p>
                                <p class="certificate-role">Highest Role: {{ certificate.highest_role|title }}</p>
                                <p class="certificate-score">Final Score: {{ certificate.final_score }}</p>

                                {% if certificate.get_completed_games %}
                                <div class="certificate-games">
                                    <p class="certificate-games-title">Training Modules:</p>
                                    <ul class="certificate-games-list">
                                        {% for game in certificate.get_completed_games %}
                                        <li>{{ game }}</li>
                                        {% endfor %}
                                    </ul>
                                </div>
                                {% endif %}

                                <p class="certificate-date">Issued on {{ certificate.issue_date|date:"F d, Y" }}</p>
                                <p class="certificate-verification">Verification Code: {{ certificate.verification_code }}</p>

                                <div class="certificate-signatures">
                                    <div class="signature">
                                        <div class="signature-line"></div>
                                        <div class="signature-name">Allan Scofield</div>
                                        <div class="signature-title">CEO, 24seven Assistants</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Certificate Details -->
                    <div class="certificate-details">
                        <div class="row g-3">
                            <div class="col-6">
                                <div class="text-center">
                                    <strong>Issue Date</strong>
                                    <div class="mt-1">{{ certificate.issue_date|date:"M d, Y" }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <strong>Final Score</strong>
                                    <div class="mt-1">{{ certificate.final_score }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <strong>Achievement</strong>
                                    <div class="mt-1">{{ certificate.highest_role|title }}</div>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="text-center">
                                    <strong>Verification</strong>
                                    <div class="mt-1">
                                        <code class="small">{{ certificate.verification_code|truncatechars:8 }}</code>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Enhanced Card Footer -->
                <div class="card-footer bg-transparent border-0 pt-0">
                    <div class="certificate-actions">
                        <a href="{% url 'corporate:download_certificate' certificate.id %}"
                           class="btn btn-primary">
                            <i class="fas fa-download me-2"></i>Download Certificate
                        </a>
                        <button type="button" class="btn btn-outline-primary"
                                data-bs-toggle="modal" data-bs-target="#shareModal{{ certificate.id }}">
                            <i class="fas fa-share-alt me-2"></i>Share Achievement
                        </button>
                        <a href="{% url 'corporate:public_verify_certificate' %}?code={{ certificate.verification_code }}"
                           class="btn btn-outline-success" target="_blank">
                            <i class="fas fa-shield-alt me-2"></i>Verify
                        </a>
                    </div>
                </div>
            </div>
        </div>

            <!-- Share Modal -->
            <div class="modal fade" id="shareModal{{ certificate.id }}" tabindex="-1" aria-hidden="true">
                <div class="modal-dialog">
                    <div class="modal-content">
                        <div class="modal-header">
                            <h5 class="modal-title">Share Certificate</h5>
                            <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                        </div>
                        <div class="modal-body">
                            <p>Share your certificate with others using the verification code or link below:</p>

                            <div class="mb-3">
                                <label class="form-label">Verification Code</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{{ certificate.verification_code }}" readonly>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" data-copy="{{ certificate.verification_code }}">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="mb-3">
                                <label class="form-label">Verification Link</label>
                                <div class="input-group">
                                    <input type="text" class="form-control" value="{% url 'corporate:public_verify_certificate' %}?code={{ certificate.verification_code }}" readonly>
                                    <button class="btn btn-outline-secondary copy-btn" type="button" data-copy="{% url 'corporate:public_verify_certificate' %}?code={{ certificate.verification_code }}">
                                        <i class="fas fa-copy"></i>
                                    </button>
                                </div>
                            </div>

                            <div class="d-grid gap-2">
                                <a href="mailto:?subject=My%20Prompt%20Engineering%20Certificate&body=I've%20completed%20the%20Corporate%20Prompt%20Master%20training!%0A%0AVerify%20my%20certificate%20using%20this%20code:%20{{ certificate.verification_code }}%0A%0AOr%20click%20this%20link:%20{% url 'corporate:public_verify_certificate' %}?code={{ certificate.verification_code }}" class="btn btn-primary">
                                    <i class="fas fa-envelope me-2"></i>Share via Email
                                </a>
                                <a href="https://www.linkedin.com/sharing/share-offsite/?url={% url 'corporate:public_verify_certificate' %}?code={{ certificate.verification_code }}" target="_blank" class="btn btn-primary">
                                    <i class="fab fa-linkedin me-2"></i>Share on LinkedIn
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            {% endfor %}
    </div>
    {% else %}
    <!-- Enhanced Empty State -->
    <div class="empty-state">
        <div class="empty-icon">
            <i class="fas fa-certificate"></i>
        </div>
        <h3 class="fw-bold mb-3">No Certificates Yet</h3>
        <p class="text-muted mb-4 lead">
            Start your professional development journey by completing training modules
            and earning industry-recognized certificates.
        </p>
        <div class="d-flex flex-column flex-md-row gap-3 justify-content-center">
            <a href="{% url 'corporate:game_list' %}" class="btn btn-primary btn-lg">
                <i class="fas fa-rocket me-2"></i>Start Training
            </a>
            <a href="{% url 'corporate:leaderboard' %}" class="btn btn-outline-primary btn-lg">
                <i class="fas fa-trophy me-2"></i>View Leaderboard
            </a>
        </div>

        <!-- Benefits Section -->
        <div class="row mt-5 text-start">
            <div class="col-md-4">
                <div class="text-center">
                    <div class="bg-primary bg-opacity-10 rounded-circle p-3 d-inline-flex mb-3">
                        <i class="fas fa-award fa-lg text-primary"></i>
                    </div>
                    <h5 class="fw-bold">Industry Recognition</h5>
                    <p class="text-muted small">Earn certificates recognized by leading companies</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <div class="bg-success bg-opacity-10 rounded-circle p-3 d-inline-flex mb-3">
                        <i class="fas fa-download fa-lg text-success"></i>
                    </div>
                    <h5 class="fw-bold">Downloadable</h5>
                    <p class="text-muted small">Download and share your achievements</p>
                </div>
            </div>
            <div class="col-md-4">
                <div class="text-center">
                    <div class="bg-warning bg-opacity-10 rounded-circle p-3 d-inline-flex mb-3">
                        <i class="fas fa-shield-alt fa-lg text-warning"></i>
                    </div>
                    <h5 class="fw-bold">Verified</h5>
                    <p class="text-muted small">Blockchain-verified authenticity</p>
                </div>
            </div>
        </div>
    </div>
    {% endif %}
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    document.addEventListener('DOMContentLoaded', function() {
        // Copy button functionality
        document.querySelectorAll('.copy-btn').forEach(button => {
            button.addEventListener('click', function() {
                const textToCopy = this.getAttribute('data-copy');
                navigator.clipboard.writeText(textToCopy).then(() => {
                    // Change button text temporarily
                    const originalHTML = this.innerHTML;
                    this.innerHTML = '<i class="fas fa-check"></i>';
                    setTimeout(() => {
                        this.innerHTML = originalHTML;
                    }, 2000);
                });
            });
        });
    });
</script>
{% endblock %}
