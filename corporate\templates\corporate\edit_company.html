{% extends 'corporate/base.html' %}

{% block title %}Edit Company - {{ company.name }}{% endblock %}

{% block content %}
<!-- Enhanced Company Management Hero Section -->
<section class="company-management-hero position-relative overflow-hidden mb-5">
    <div class="hero-background"></div>
    <div class="container-fluid">
        <div class="row">
            <div class="col-12">
                <div class="hero-content text-white p-5 rounded-3 position-relative">
                    <div class="row align-items-center">
                        <div class="col-lg-8">
                            <div class="welcome-content">
                                <h1 class="display-5 fw-bold mb-3 text-shadow">
                                    <i class="fas fa-building me-3"></i>
                                    Company <span class="text-gradient">Management</span>
                                </h1>
                                <p class="lead mb-4 text-white-75">
                                    Update your company profile, branding, and organizational settings.
                                </p>
                                <div class="company-badges d-flex flex-wrap gap-3">
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-palette me-2"></i>Brand Management
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-cog me-2"></i>Settings Control
                                    </div>
                                    <div class="badge bg-white bg-opacity-25 text-white px-3 py-2 rounded-pill">
                                        <i class="fas fa-shield-alt me-2"></i>Secure Updates
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-4">
                            <div class="company-info text-center">
                                <div class="company-logo-display mb-3">
                                    {% if company.logo %}
                                    <img src="{{ company.logo.url }}" alt="{{ company.name }}"
                                         class="rounded-circle border border-3 border-white shadow-lg"
                                         width="120" height="120" style="object-fit: cover;">
                                    {% else %}
                                    <div class="logo-placeholder bg-white bg-opacity-25 rounded-circle d-flex align-items-center justify-content-center border border-3 border-white"
                                         style="width: 120px; height: 120px;">
                                        <i class="fas fa-building fa-2x text-white"></i>
                                    </div>
                                    {% endif %}
                                </div>
                                <h4 class="fw-bold text-white mb-1">{{ company.name }}</h4>
                                <p class="text-white-75 mb-0">Company Profile</p>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</section>

<!-- Enhanced Content Container -->
<div class="container-fluid">
    <div class="card border-0 shadow-lg">
        <div class="card-header bg-gradient-primary text-white border-0 py-4">
            <div class="d-flex align-items-center">
                <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                    <i class="fas fa-edit fa-lg text-white"></i>
                </div>
                <div>
                    <h4 class="mb-1 fw-bold">Edit Company Details</h4>
                    <p class="mb-0 text-white-75">Update your organization's information and branding</p>
                </div>
            </div>
        </div>
        <div class="card-body p-5">
            <form method="post" enctype="multipart/form-data" class="company-form">
                {% csrf_token %}

                <div class="row g-5">
                    <!-- Enhanced Logo Section -->
                    <div class="col-lg-4">
                        <div class="logo-section text-center">
                            <div class="logo-preview-container mb-4">
                                {% if company.logo %}
                                <div class="current-logo-wrapper position-relative">
                                    <img src="{{ company.logo.url }}" alt="{{ company.name }} Logo"
                                         class="current-logo rounded-3 shadow-lg border border-3 border-primary">
                                    <div class="logo-overlay position-absolute top-0 start-0 w-100 h-100 d-flex align-items-center justify-content-center rounded-3">
                                        <i class="fas fa-camera fa-2x text-white opacity-75"></i>
                                    </div>
                                </div>
                                {% else %}
                                <div class="logo-placeholder bg-gradient-primary text-white rounded-3 shadow-lg d-flex align-items-center justify-content-center">
                                    <div class="text-center">
                                        <i class="fas fa-building fa-3x mb-3"></i>
                                        <p class="mb-0 fw-bold">No Logo</p>
                                    </div>
                                </div>
                                {% endif %}
                            </div>

                            <div class="logo-upload-section">
                                <label for="{{ form.logo.id_for_label }}" class="form-label fw-bold text-primary">
                                    <i class="fas fa-upload me-2"></i>Company Logo
                                </label>
                                <div class="upload-wrapper">
                                    {{ form.logo }}
                                </div>
                                {% if form.logo.errors %}
                                <div class="alert alert-danger mt-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>{{ form.logo.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text mt-2">
                                    <i class="fas fa-info-circle me-1"></i>
                                    Upload a professional logo (PNG, JPG, max 2MB)
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Enhanced Company Details -->
                    <div class="col-lg-8">
                        <div class="company-details-section">
                            <h5 class="section-title mb-4">
                                <i class="fas fa-info-circle me-2 text-primary"></i>Company Information
                            </h5>

                            <div class="form-group mb-4">
                                <label for="{{ form.name.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-building me-2 text-primary"></i>Company Name
                                </label>
                                {{ form.name }}
                                {% if form.name.errors %}
                                <div class="alert alert-danger mt-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>{{ form.name.errors }}
                                </div>
                                {% endif %}
                                <div class="form-text">
                                    <i class="fas fa-lightbulb me-1"></i>
                                    This will be displayed across the platform
                                </div>
                            </div>

                            <div class="form-group mb-4">
                                <label for="{{ form.description.id_for_label }}" class="form-label fw-bold">
                                    <i class="fas fa-align-left me-2 text-primary"></i>Company Description
                                </label>
                                {{ form.description }}
                                {% if form.description.errors %}
                                <div class="alert alert-danger mt-2">
                                    <i class="fas fa-exclamation-triangle me-2"></i>{{ form.description.errors }}
                                </div>
                                    {% endif %}
                                    <div class="form-text">Provide a brief description of your company</div>
                                </div>

                                <div class="mb-3">
                                    <label for="{{ form.phone_number.id_for_label }}" class="form-label">Phone Number <span class="text-danger">*</span></label>
                                    {{ form.phone_number }}
                                    {% if form.phone_number.errors %}
                                    <div class="text-danger">
                                        {{ form.phone_number.errors }}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">{{ form.phone_number.help_text }}</div>
                                </div>

                                <div class="mb-3 form-check">
                                    {{ form.allow_leaderboard }}
                                    <label for="{{ form.allow_leaderboard.id_for_label }}" class="form-check-label">
                                        Allow employees to appear on leaderboards
                                    </label>
                                    {% if form.allow_leaderboard.errors %}
                                    <div class="text-danger">
                                        {{ form.allow_leaderboard.errors }}
                                    </div>
                                    {% endif %}
                                    <div class="form-text">If checked, employees from your company will appear on leaderboards</div>
                                </div>
                            </div>
                        </div>

                        <div class="text-end mt-3">
                            <a href="{% url 'corporate:corporate_dashboard' %}" class="btn btn-secondary me-2">
                                <i class="fas fa-times me-1"></i> Cancel
                            </a>
                            <button type="submit" class="btn btn-primary">
                                <i class="fas fa-save me-1"></i> Save Changes
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Enhanced Company Status Information -->
    <div class="card border-0 shadow-lg mt-4">
        <div class="card-header bg-gradient-info text-white border-0 py-4">
            <div class="d-flex align-items-center">
                <div class="bg-white bg-opacity-25 rounded-circle p-3 me-3">
                    <i class="fas fa-chart-line fa-lg text-white"></i>
                </div>
                <div>
                    <h4 class="mb-1 fw-bold">Company Status & Analytics</h4>
                    <p class="mb-0 text-white-75">Overview of your company's current status and metrics</p>
                </div>
            </div>
        </div>
        <div class="card-body p-4">
            <div class="row g-4">
                <div class="col-lg-6">
                    <div class="status-info-card bg-light rounded-3 p-4">
                        <h6 class="fw-bold text-primary mb-3">
                            <i class="fas fa-user-tie me-2"></i>Ownership Information
                        </h6>
                        <div class="info-item mb-3">
                            <strong class="text-muted">Company Owner:</strong>
                            <div class="mt-1">
                                <i class="fas fa-user me-2 text-primary"></i>
                                {{ company.owner.get_full_name|default:company.owner.username }}
                            </div>
                        </div>
                        <div class="info-item">
                            <strong class="text-muted">Established:</strong>
                            <div class="mt-1">
                                <i class="fas fa-calendar me-2 text-primary"></i>
                                {{ company.created_at|date:"F j, Y" }}
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-lg-6">
                    <div class="status-info-card bg-light rounded-3 p-4">
                        <h6 class="fw-bold text-primary mb-3">
                            <i class="fas fa-shield-alt me-2"></i>Account Status
                        </h6>
                        <div class="info-item mb-3">
                            <strong class="text-muted">Current Status:</strong>
                            <div class="mt-2">
                                {% if company.is_active %}
                                <span class="badge bg-success px-3 py-2 fs-6">
                                    <i class="fas fa-check-circle me-1"></i>Active & Operational
                                </span>
                                {% elif company.pending_approval %}
                                <span class="badge bg-warning px-3 py-2 fs-6">
                                    <i class="fas fa-clock me-1"></i>Pending Approval
                                </span>
                                {% else %}
                                <span class="badge bg-danger px-3 py-2 fs-6">
                                    <i class="fas fa-pause-circle me-1"></i>Inactive
                                </span>
                                {% endif %}
                            </div>
                        </div>
                        {% if company.approved_at %}
                        <div class="info-item">
                            <strong class="text-muted">Approved Date:</strong>
                            <div class="mt-1">
                                <i class="fas fa-check me-2 text-success"></i>
                                {{ company.approved_at|date:"F j, Y" }}
                            </div>
                        </div>
                        {% endif %}
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

{% block extra_css %}
<style>
/* Company Management Hero Section */
.company-management-hero {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    position: relative;
}

.hero-background::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><defs><pattern id="grain" width="100" height="100" patternUnits="userSpaceOnUse"><circle cx="50" cy="50" r="1" fill="white" opacity="0.1"/></pattern></defs><rect width="100" height="100" fill="url(%23grain)"/></svg>');
    opacity: 0.3;
}

.text-gradient {
    background: linear-gradient(45deg, #ffd700, #ffed4e);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
}

.text-shadow {
    text-shadow: 0 2px 4px rgba(0,0,0,0.3);
}

/* Company Logo Styling */
.company-logo-display img {
    object-fit: cover;
    transition: all 0.3s ease;
}

.company-logo-display img:hover {
    transform: scale(1.05);
}

.logo-placeholder {
    width: 120px;
    height: 120px;
    transition: all 0.3s ease;
}

.logo-placeholder:hover {
    transform: scale(1.05);
}

/* Form Styling */
.company-form {
    position: relative;
}

.logo-section {
    position: relative;
}

.logo-preview-container {
    position: relative;
}

.current-logo {
    width: 200px;
    height: 200px;
    object-fit: cover;
    transition: all 0.3s ease;
}

.current-logo:hover {
    transform: scale(1.02);
}

.logo-overlay {
    background: rgba(0,0,0,0.5);
    opacity: 0;
    transition: all 0.3s ease;
}

.current-logo-wrapper:hover .logo-overlay {
    opacity: 1;
}

.logo-placeholder {
    width: 200px;
    height: 200px;
    transition: all 0.3s ease;
}

.logo-placeholder:hover {
    transform: translateY(-5px);
}

/* Form Controls */
.form-control {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    border: 2px solid #e9ecef;
}

.form-control:focus {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(102, 126, 234, 0.15);
    border-color: #667eea;
}

.form-label {
    font-weight: 600;
    color: #495057;
    margin-bottom: 0.75rem;
}

.section-title {
    color: #495057;
    border-bottom: 2px solid #667eea;
    padding-bottom: 0.5rem;
}

/* Upload Wrapper */
.upload-wrapper input[type="file"] {
    border: 2px dashed #667eea;
    border-radius: 0.5rem;
    padding: 1rem;
    background: rgba(102, 126, 234, 0.05);
    transition: all 0.3s ease;
}

.upload-wrapper input[type="file"]:hover {
    background: rgba(102, 126, 234, 0.1);
    border-color: #5a67d8;
}

/* Status Cards */
.status-info-card {
    transition: all 0.3s ease;
    border: 1px solid rgba(102, 126, 234, 0.1);
}

.status-info-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 10px 30px rgba(0,0,0,0.1);
}

.info-item {
    padding: 0.5rem 0;
    border-bottom: 1px solid rgba(0,0,0,0.05);
}

.info-item:last-child {
    border-bottom: none;
}

/* Buttons */
.btn {
    transition: all 0.3s ease;
    border-radius: 0.5rem;
    font-weight: 600;
}

.btn:hover {
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0,0,0,0.15);
}

/* Responsive Design */
@media (max-width: 768px) {
    .company-management-hero .hero-content {
        padding: 2rem !important;
    }

    .display-5 {
        font-size: 2rem;
    }

    .current-logo, .logo-placeholder {
        width: 150px;
        height: 150px;
    }

    .company-logo-display img {
        width: 80px;
        height: 80px;
    }

    .logo-placeholder {
        width: 80px;
        height: 80px;
    }
}
</style>
{% endblock %}
{% endblock %}
