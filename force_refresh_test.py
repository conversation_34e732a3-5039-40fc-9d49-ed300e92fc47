#!/usr/bin/env python
"""
Script to test if refreshing the page would show the task message.
"""
import os
import django
import sys

# Set up Django
os.environ.setdefault('DJANGO_SETTINGS_MODULE', 'prompt_game.settings')
django.setup()

# Import models
from game.models import GameSession, Message
from game.all_role_tasks import get_all_role_tasks

def check_frontend_state():
    """Check what the frontend should see when it loads"""
    print("Checking what frontend should see...")
    
    # Get the most recent game session
    session = GameSession.objects.last()
    if not session:
        print("No game sessions found")
        return
    
    print(f"Backend game state:")
    print(f"  Role: {session.current_role}")
    print(f"  Current task: {session.current_task}")
    print(f"  Role challenges completed: {session.role_challenges_completed}")
    
    # Get what the frontend would receive via to_dict()
    game_state_dict = session.to_dict()
    print(f"\nFrontend game state (via to_dict()):")
    print(f"  currentRole: {game_state_dict.get('currentRole')}")
    print(f"  currentTask: {game_state_dict.get('currentTask')}")
    print(f"  roleChallengesCompleted: {game_state_dict.get('roleChallengesCompleted')}")
    
    # Check messages that would be sent to frontend
    messages = [msg.to_dict() for msg in session.messages.all().order_by('timestamp')]
    print(f"\nTotal messages: {len(messages)}")
    
    # Find challenge messages
    challenge_messages = [msg for msg in messages if msg.get('is_challenge')]
    print(f"Challenge messages: {len(challenge_messages)}")
    
    for msg in challenge_messages[-3:]:  # Last 3 challenge messages
        print(f"  Task: {msg.get('task_id')} | Sender: {msg.get('sender')} | Time: {msg.get('timestamp')}")
        print(f"    Preview: {msg.get('text', '')[:60]}...")
    
    # Check specifically for onboarding_checklist
    onboarding_messages = [msg for msg in messages if msg.get('task_id') == 'onboarding_checklist']
    print(f"\nOnboarding checklist messages: {len(onboarding_messages)}")
    for msg in onboarding_messages:
        print(f"  ID: {msg.get('id')} | Challenge: {msg.get('is_challenge')} | Time: {msg.get('timestamp')}")

if __name__ == "__main__":
    check_frontend_state()
